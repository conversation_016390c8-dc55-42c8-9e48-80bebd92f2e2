interface FeaturesProps {
  icon: string;
  title: string;
  description: string;
}

export const featureList: FeaturesProps[] = [
  {
    icon: "TabletSmartphone",
    title: "AI-Powered Task Management",
    description:
      "Utilize machine learning algorithms to prioritize tasks, ensuring your team focuses on what matters most.",
  },
  {
    icon: "BadgeCheck",
    title: "Predictive Analytics",
    description:
      "Leverage historical data to forecast trends and make proactive decisions, minimizing risks and maximizing opportunities.",
  },
  {
    icon: "Goal",
    title: "Custom Workflow Automation",
    description:
      "Design automated workflows tailored to your processes, integrating multiple tools to streamline operations seamlessly.",
  },
  {
    icon: "PictureInPicture",
    title: "Robust API Integrations",
    description:
      "Easily connect our platform with your existing software solutions, enabling data synchronization and enhancing functionality.",
  },
  {
    icon: "MousePointerClick",
    title: "Interactive Dashboards",
    description:
      "Visualize key performance indicators (KPIs) and metrics in real-time with customizable dashboards that provide actionable insights.",
  },
  {
    icon: "Lock",
    title: "Privacy and Compliance",
    description:
      "Ensure your data is protected with end-to-end encryption and compliance with regulations such as GDPR and CCPA.",
  },
];
