"use client";

import { useCart } from "@/context/cart-context";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>it<PERSON> } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Trash2, ShoppingCart } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { formatPrice } from "@/lib/utils";
import { RemoveItemDialog } from "@/components/cart/remove-item-dialog";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthContext, authHelpers } from "@/components/auth/auth-provider";

export function MiniCart() {
  const { isOpen, setIsOpen, items, removeItem, subtotal, cgst, igst, total, totalItems } = useCart();
  const [itemToRemove, setItemToRemove] = useState<{ id: string; name: string } | null>(null);
  const { requireAuth } = useAuthContext();
  const router = useRouter();

  const handleRemoveClick = (id: string, name: string) => {
    setItemToRemove({ id, name });
  };

  const handleRemoveConfirm = () => {
    if (itemToRemove) {
      removeItem(itemToRemove.id);
      setItemToRemove(null);
    }
  };

  const handleCheckoutClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();

    // Use the new auth system for consistent UX
    const isAuthenticated = requireAuth(authHelpers.requireAuthForCheckout(
      () => {
        // Close the cart and proceed to checkout
        setIsOpen(false);
        router.push("/checkout");
      },
      () => {
        // Keep cart open if user cancels auth
      }
    ));

    // If already authenticated, proceed immediately
    if (isAuthenticated) {
      setIsOpen(false);
      router.push("/checkout");
    }
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetContent side="right" className="flex flex-col w-full sm:max-w-sm border-l p-0">
          <div className="p-4 pb-0">
            <SheetHeader className="px-1 mb-3">
              <div className="flex items-center justify-between">
                <SheetTitle className="flex items-center gap-2">
                  <ShoppingCart className="size-5" />
                  <span>Your Cart</span>
                  {totalItems > 0 && (
                    <span className="text-sm font-normal text-muted-foreground">
                      ({totalItems} {totalItems === 1 ? 'item' : 'items'})
                    </span>
                  )}
                </SheetTitle>
              </div>
            </SheetHeader>
          </div>

          <Separator />

          {items.length === 0 ? (
            <div className="flex-1 flex flex-col items-center justify-center py-10 text-center">
              <ShoppingCart className="size-12 opacity-20 mb-4" />
              <h3 className="text-lg font-medium">Your cart is empty</h3>
              <p className="text-sm text-muted-foreground mt-1 mb-4">
                Looks like you haven&apos;t added any products to your cart yet.
              </p>
              <Button onClick={() => setIsOpen(false)}>Continue Shopping</Button>
            </div>
          ) : (
            <>
              <div className="flex-1 overflow-y-auto p-4">
                <ul className="space-y-4">
                  {items.map((item) => (
                    <li key={item.id} className="flex gap-3 pb-4 border-b border-muted/70 last:border-0 last:pb-0">
                      <div className="w-16 h-16 bg-background flex-shrink-0 rounded-md overflow-hidden flex items-center justify-center p-1 border border-muted/30">
                        {(item.productLogo || item.image) ? (
                          <Image 
                            src={item.productLogo || item.image || ""}
                            alt={item.productName || item.name || "Product"}
                            width={64}
                            height={64}
                            unoptimized
                            className="max-w-full max-h-full object-contain"
                            onError={(e) => {
                              console.error("Failed to load image:", item.productLogo || item.image);
                              (e.target as HTMLImageElement).src = "";
                            }}
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-muted/20">
                            <ShoppingCart className="h-6 w-6 text-muted-foreground/50" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 flex flex-col min-w-0">
                        <div className="flex justify-between items-start">
                          <h4 className="font-medium text-sm break-words pr-2 pt-0.5">
                            {item.productName || item.name}
                          </h4>
                          <Button 
                            variant="ghost" 
                            size="icon"
                            className="h-6 w-6 text-muted-foreground hover:bg-muted/60 -mr-1.5 -mt-0.5 transition-colors"
                            onClick={() => handleRemoveClick(item.id, item.productName || item.name)}
                            title="Remove item"
                          >
                            <Trash2 className="size-3" />
                          </Button>
                        </div>
                        
                        <div className="space-y-0.5 mt-0.5">
                          <div className="text-xs text-muted-foreground">
                            Plan: <span className="font-medium">{item.name}</span>
                          </div>
                          {item.planDuration && (
                            <div className="text-xs text-muted-foreground">
                              Duration: {item.planDuration === "monthly" ? "Monthly" : "Yearly"}
                            </div>
                          )}
                        </div>

                        <div className="mt-auto pt-2 flex items-center justify-end">
                          <span className="text-sm font-medium">
                            {formatPrice(item.price)}
                          </span>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="border-t border-muted p-4 bg-muted/20">
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span className="font-medium">{formatPrice(subtotal)}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">CGST (9%)</span>
                    <span>{formatPrice(cgst)}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">IGST (9%)</span>
                    <span>{formatPrice(igst)}</span>
                  </div>
                  
                  <Separator className="my-2" />
                  
                  <div className="flex items-center justify-between font-medium">
                    <span>Total</span>
                    <span className="text-primary">{formatPrice(total)}</span>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <Button asChild className="w-full" size="lg">
                    <Link href="/checkout" onClick={handleCheckoutClick}>
                      Checkout
                    </Link>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="w-full" 
                    onClick={() => setIsOpen(false)}
                  >
                    Continue Shopping
                  </Button>
                </div>
                
                <div className="text-xs text-muted-foreground text-center mt-4 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                    <rect width="18" height="16" x="3" y="4" rx="2" />
                    <path d="M8 11v5" />
                    <path d="M16 11v5" />
                    <path d="M12 11v5" />
                  </svg>
                  <span>Secure Checkout</span>
                </div>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>

      {itemToRemove && (
        <RemoveItemDialog
          isOpen={!!itemToRemove}
          onClose={() => setItemToRemove(null)}
          onConfirm={handleRemoveConfirm}
          itemName={itemToRemove.name}
        />
      )}

      {/* Auth Modal is now provided globally by AuthProvider */}
    </>
  );
}
