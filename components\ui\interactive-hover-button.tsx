"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface InteractiveHoverButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text?: string
  className?: string
}

export const InteractiveHoverButton = React.forwardRef<
  HTMLButtonElement,
  InteractiveHoverButtonProps
>(({ text = "Button", className, children, ...props }, ref) => {
  return (
    <button
      ref={ref}
      className={cn(
        // Base styles
        "group relative inline-flex items-center justify-center",
        "px-6 py-3 text-base font-medium",
        "transition-all duration-300 ease-out",
        "focus:outline-none focus:ring-2 focus:ring-offset-2",
        
        // Black theme styles
        "bg-black text-white border border-black/20",
        "hover:bg-black/90 focus:ring-black/50",
        
        // Dark mode styles
        "dark:bg-black dark:text-white dark:border-white/20",
        "dark:hover:bg-white/10 dark:focus:ring-white/50",
        
        // Shape and effects
        "rounded-lg overflow-hidden",
        "shadow-lg hover:shadow-xl",
        "transform hover:scale-[1.02] active:scale-[0.98]",
        
        className
      )}
      {...props}
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-black/0 via-white/5 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      {/* Shimmer effect */}
      <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12" />
      
      {/* Border glow effect */}
      <div className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-transparent via-white/10 to-transparent blur-sm" />
      
      {/* Content */}
      <span className="relative z-10 flex items-center gap-2">
        {children || text}
        
        {/* Arrow icon */}
        <svg
          className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 8l4 4m0 0l-4 4m4-4H3"
          />
        </svg>
      </span>
      
      {/* Ripple effect on click */}
      <div className="absolute inset-0 rounded-lg opacity-0 group-active:opacity-100 transition-opacity duration-150 bg-white/10" />
    </button>
  )
})

InteractiveHoverButton.displayName = "InteractiveHoverButton"

export default InteractiveHoverButton
