"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { toast } from "sonner"; // Import toast

// Define cart item type
export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  slug?: string; // added optional slug
  productName?: string;
  productLogo?: string;
  planCode?: string; // Plan code for subscriptions - REQUIRED for subscription items
  planDuration?: "monthly" | "yearly" | "trial" | undefined; // Add trial option and explicit undefined
  trialDays?: number; // Add trial days
}

// Define cart context type
interface CartContextType {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  subtotal: number;
  cgst: number; // 9% CGST
  igst: number; // 9% IGST
  totalTax: number; // Total tax (18%)
  total: number; // Final total with tax
  replaceItem: (oldItemId: string, newItem: CartItem) => void; // Add this new method
  onItemAdded?: (item: CartItem) => void; // Callback when item is added
  setOnItemAdded: (callback: ((item: CartItem) => void) | undefined) => void; // Set callback
}

// Create the context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component
export function CartProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false);
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoaded, setIsLoaded] = useState(false); // Track if localStorage has been loaded
  const [onItemAdded, setOnItemAdded] = useState<((item: CartItem) => void) | undefined>(undefined);

  // Load cart from localStorage on mount
  useEffect(() => {
    // Ensure this runs only in browser context
    if (typeof window !== 'undefined') {
      try {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
          const parsedCart = JSON.parse(savedCart);
          if (Array.isArray(parsedCart)) {
            setItems(parsedCart);
          }
        }
      } catch (error) {
        console.error('Failed to load cart data from localStorage:', error);
      } finally {
        setIsLoaded(true); // Mark as loaded regardless of success/failure
      }
    }
  }, []);

  // Save cart to localStorage when it changes (but only after initial load)
  useEffect(() => {
    if (isLoaded && typeof window !== 'undefined') {
      try {
        localStorage.setItem('cart', JSON.stringify(items));
      } catch (error) {
        console.error('Failed to save cart data to localStorage:', error);
      }
    }
  }, [items, isLoaded]);

  // Add item to cart with optimized toast handling
  const addItem = (newItem: CartItem) => {
    // Validate that subscription items have planCode
    if (!newItem.planCode && (newItem.planDuration === "monthly" || newItem.planDuration === "yearly" || newItem.planDuration === "trial")) {
      console.error("❌ CART ERROR - Attempt to add subscription item without plan code:", newItem);
      toast.error(`Error: Missing plan code for ${newItem.name}`);
      return;
    }

    console.log("🛒 CART - Adding item:", {
      id: newItem.id,
      name: newItem.name,
      price: newItem.price,
      planCode: newItem.planCode || "none",
      planDuration: newItem.planDuration || "none"
    });

    let shouldShowAddToast = false;
    let shouldShowUpdateToast = false;

    setItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === newItem.id);

      if (existingItem) {
        shouldShowUpdateToast = true;
        return prevItems.map(item =>
          item.id === newItem.id
            ? { ...item, quantity: item.quantity + newItem.quantity }
            : item
        );
      } else {
        shouldShowAddToast = true;
        return [...prevItems, newItem];
      }
    });

    // Store cart items in localStorage manually in case there's a crash
    try {
      const updatedItems = [...items];
      const existingItemIndex = updatedItems.findIndex(item => item.id === newItem.id);

      if (existingItemIndex >= 0) {
        updatedItems[existingItemIndex].quantity += newItem.quantity;
      } else {
        updatedItems.push(newItem);
      }

      localStorage.setItem('cart', JSON.stringify(updatedItems));
    } catch (e) {
      console.error("❌ Error manually updating cart in localStorage:", e);
    }

    // Show toast after state update to prevent duplicate notifications
    if (shouldShowAddToast) {
      setTimeout(() => toast.success(`Added ${newItem.name} to your cart`), 0);
    } else if (shouldShowUpdateToast) {
      setTimeout(() => toast.success(`Updated quantity for ${newItem.name}`), 0);
    }

    // Open mini cart when adding new items
    setIsOpen(true);

    // Call the onItemAdded callback if it exists
    if (onItemAdded && newItem) {
      try {
        // Create a safe copy of the item with all required properties
        const safeItem = {
          ...newItem,
          id: newItem.id || '',
          name: newItem.name || '',
          price: typeof newItem.price === 'number' ? newItem.price : 0,
          quantity: typeof newItem.quantity === 'number' ? newItem.quantity : 1,
          image: newItem.image || '',
          slug: newItem.slug || '',
          planDuration: newItem.planDuration || undefined
        };

        // Log the item being passed to the callback
        console.log('CART - Calling onItemAdded with item:', {
          id: safeItem.id,
          name: safeItem.name,
          price: safeItem.price,
          planDuration: safeItem.planDuration || 'undefined',
          slug: safeItem.slug
        });

        onItemAdded(safeItem);
      } catch (error) {
        console.error('Error in onItemAdded callback:', error);
      }
    }
  };

  // Optimized method to replace an item in the cart with a new one
  const replaceItem = (oldItemId: string, newItem: CartItem) => {
    try {
      console.log(`🔄 CART - Replacing item: ${oldItemId} with ${newItem.id}`);

      // First update localStorage to ensure persistence
      try {
        const currentItems = [...items];
        const updatedItems = currentItems.filter(item => item.id !== oldItemId);
        updatedItems.push(newItem);
        localStorage.setItem('cart', JSON.stringify(updatedItems));
      } catch (error) {
        console.error('Failed to update cart in localStorage:', error);
      }

      // Then update state
      setItems(prevItems => {
        const updatedItems = prevItems.filter(item => item.id !== oldItemId);
        return [...updatedItems, newItem];
      });

      // Open mini cart when changing items (with a small delay)
      setTimeout(() => {
        setIsOpen(true);
      }, 300);

      return true; // Indicate success
    } catch (error) {
      console.error('Error replacing item in cart:', error);
      toast.error(`Failed to update your cart. Please try again.`);
      return false; // Indicate failure
    }
  };

  // Remove item from cart with optimized toast
  const removeItem = (itemId: string) => {
    // Find the item before removing it
    const itemToRemove = items.find(item => item.id === itemId);

    if (itemToRemove) {
      setItems(prevItems => prevItems.filter(item => item.id !== itemId));

      // Show toast after state update to prevent duplicate notifications
      setTimeout(() => toast.info(`Removed ${itemToRemove.name} from your cart`), 0);
    }
  };

  // Update item quantity
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    setItems(prevItems => {
      const updatedItems = prevItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      );

      const updatedItem = updatedItems.find(item => item.id === itemId);
      if (updatedItem) {
        toast.success(`Updated ${updatedItem.name} quantity to ${quantity}`);
      }

      return updatedItems;
    });
  };

  // Clear cart function with improved reliability
  const clearCart = () => {
    // Check if cart is already empty to prevent unnecessary state updates
    if (items.length > 0) {
      console.log("Clearing cart of", items.length, "items");

      // First remove from localStorage to ensure persistence
      try {
        localStorage.removeItem('cart');
      } catch (error) {
        console.error('Failed to clear cart from localStorage:', error);
      }

      // Then update state
      setItems([]);

      // Close mini-cart if open
      if (isOpen) {
        setIsOpen(false);
      }

      // Only show toast if there were actually items to clear
      toast.info("Your cart has been cleared");
    } else {
      console.log("Cart already empty, no need to clear");
    }
  };

  // Calculate total items
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);

  // Calculate subtotal
  const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0);

  // Calculate taxes
  const cgst = subtotal * 0.09; // 9% CGST
  const igst = subtotal * 0.09; // 9% IGST
  const totalTax = cgst + igst; // Total 18% GST
  const total = subtotal + totalTax; // Final total with tax

  return (
    <CartContext.Provider
      value={{
        isOpen,
        setIsOpen,
        items,
        addItem,
        removeItem,
        updateQuantity,
        clearCart,
        replaceItem, // Add the new method to the context
        totalItems,
        subtotal,
        cgst,
        igst,
        totalTax,
        total,
        onItemAdded, // Add the callback
        setOnItemAdded // Add the setter for the callback
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

// Custom hook for using the cart context
export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
