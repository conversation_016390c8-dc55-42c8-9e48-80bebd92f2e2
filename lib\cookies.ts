/**
 * Server-side utility functions for working with cookies
 * This provides a server-compatible API for cookie management
 */

import { cookies } from 'next/headers';

/**
 * Get a cookie value in server components
 * @param name The name of the cookie to get
 * @returns The cookie value or undefined if not found
 */
export function getCookie(name: string): string | undefined {
  const cookieStore = cookies();
  const cookie = cookieStore.get(name);
  return cookie?.value;
}

/**
 * Set a cookie in server components
 * @param name The name of the cookie to set
 * @param value The value to set
 * @param options Cookie options
 */
export function setCookie(
  name: string,
  value: string,
  options: {
    path?: string;
    maxAge?: number;
    httpOnly?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    secure?: boolean;
  } = {}
): void {
  const cookieStore = cookies();
  cookieStore.set({
    name,
    value,
    path: options.path || '/',
    maxAge: options.maxAge,
    httpOnly: options.httpOnly,
    sameSite: options.sameSite,
    secure: options.secure
  });
}

/**
 * Delete a cookie in server components
 * @param name The name of the cookie to delete
 */
export function deleteCookie(name: string): void {
  const cookieStore = cookies();
  cookieStore.delete(name);
} 