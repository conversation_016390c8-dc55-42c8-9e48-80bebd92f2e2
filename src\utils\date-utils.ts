/**
 * Format a date into a localized string
 * @param date The date to format
 * @param options Options for Intl.DateTimeFormat
 * @returns A formatted date string
 */
export function formatDate(
  date: Date,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
): string {
  return new Intl.DateTimeFormat('en-US', options).format(date);
}

/**
 * Format a date into a relative time string (e.g., "2 days ago")
 * @param date The date to format
 * @returns A string representation of the relative time
 */
export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      if (diffMinutes === 0) {
        return 'just now';
      }
      return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ${date > now ? 'from now' : 'ago'}`;
    }
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ${date > now ? 'from now' : 'ago'}`;
  }
  
  if (diffDays < 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ${date > now ? 'from now' : 'ago'}`;
  }
  
  const diffWeeks = Math.floor(diffDays / 7);
  if (diffWeeks < 4) {
    return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ${date > now ? 'from now' : 'ago'}`;
  }
  
  const diffMonths = Math.floor(diffDays / 30);
  if (diffMonths < 12) {
    return `${diffMonths} month${diffMonths !== 1 ? 's' : ''} ${date > now ? 'from now' : 'ago'}`;
  }
  
  const diffYears = Math.floor(diffDays / 365);
  return `${diffYears} year${diffYears !== 1 ? 's' : ''} ${date > now ? 'from now' : 'ago'}`;
}

/**
 * Get the number of days remaining until a future date
 * @param futureDate The future date
 * @returns The number of days remaining, or 0 if the date is in the past
 */
export function getRemainingDays(futureDate: Date): number {
  const now = new Date();
  const diffTime = futureDate.getTime() - now.getTime();
  
  if (diffTime <= 0) {
    return 0;
  }
  
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Check if a date is in the past
 * @param date The date to check
 * @returns True if the date is in the past
 */
export function isDateInPast(date: Date): boolean {
  return date < new Date();
}

/**
 * Add a specific duration to a date
 * @param date The starting date
 * @param value The amount to add
 * @param unit The unit of time (days, weeks, months, years)
 * @returns A new Date with the duration added
 */
export function addToDate(
  date: Date,
  value: number,
  unit: 'days' | 'weeks' | 'months' | 'years'
): Date {
  const result = new Date(date);
  
  switch (unit) {
    case 'days':
      result.setDate(result.getDate() + value);
      break;
    case 'weeks':
      result.setDate(result.getDate() + (value * 7));
      break;
    case 'months':
      result.setMonth(result.getMonth() + value);
      break;
    case 'years':
      result.setFullYear(result.getFullYear() + value);
      break;
  }
  
  return result;
} 