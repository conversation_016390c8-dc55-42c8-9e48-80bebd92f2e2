declare module 'vitest' {
  export interface SpyInstance<T extends (...args: any[]) => any> {
    mockResolvedValueOnce: (value: any) => SpyInstance<T>;
    mockRejectedValueOnce: (value: any) => SpyInstance<T>;
    mockImplementation: (implementation: (...args: any[]) => any) => SpyInstance<T>;
    mockReturnValue: (value: any) => SpyInstance<T>;
    mockReturnValueOnce: (value: any) => SpyInstance<T>;
    mockReset: () => SpyInstance<T>;
    mockRestore: () => SpyInstance<T>;
    getMockName: () => string;
  }

  export function describe(name: string, fn: () => void): void;
  export function it(name: string, fn: () => void | Promise<void>): void;
  export function expect<T>(actual: T): {
    toBe: (expected: any) => void;
    toEqual: (expected: any) => void;
    toBeNull: () => void;
    toBeTruthy: () => void;
    toBeFalsy: () => void;
    toHaveProperty: (property: string, value?: any) => void;
    toHaveBeenCalled: () => void;
    toHaveBeenCalledTimes: (times: number) => void;
    toHaveBeenCalledWith: (...args: any[]) => void;
    toBeCloseTo: (expected: number, precision?: number) => void;
    rejects: {
      toThrow: (message?: string | RegExp) => Promise<void>;
    };
    not: any;
  };
  export function beforeEach(fn: () => void | Promise<void>): void;
  export function afterEach(fn: () => void | Promise<void>): void;
  export const vi: {
    fn: <T extends (...args: any[]) => any>() => SpyInstance<T>;
    resetAllMocks: () => void;
    restoreAllMocks: () => void;
    mock: (path: string, factory?: () => any) => void;
    mocked: <T>(item: T) => T;
  };
} 