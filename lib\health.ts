import os from "os";

export interface MemoryInfo {
  available: number;
  percent: number;
  total: number;
}

export interface ProcessInfo {
  cpu_percent: number;
  memory_info: MemoryInfo;
  memory_percent: number;
  pid: number;
}

export interface HealthCheckResponse {
  process: ProcessInfo;
  status: string;
  timestamp: number;
}

// Store previous CPU measurements for accurate usage calculation
let prevCpuUsage = process.cpuUsage();
let prevTimestamp = Date.now();

// Get number of CPU cores for normalization
const numCpuCores = os.cpus().length;

export function getHealthStatus(): HealthCheckResponse {
  // Get PID
  const pid = process.pid;

  // Get accurate CPU usage by measuring delta
  const currentCpuUsage = process.cpuUsage();
  const currentTime = Date.now();
  
  // Calculate deltas
  const userDelta = currentCpuUsage.user - prevCpuUsage.user;
  const systemDelta = currentCpuUsage.system - prevCpuUsage.system;
  const timeDelta = currentTime - prevTimestamp;
  
  // Calculate CPU percentage (user + system CPU time divided by elapsed time)
  // Then normalize by number of CPU cores and cap at 100%
  let cpuPercent = 0;
  if (timeDelta > 0) {
    // Convert from microseconds to milliseconds (divide by 1000)
    // Then divide by elapsed time to get percentage of a single CPU core
    cpuPercent = ((userDelta + systemDelta) / 1000) / timeDelta * 100;
    
    // Normalize by number of CPU cores (optional, comment this out if you want per-core percentage)
    cpuPercent = cpuPercent / numCpuCores;
    
    // Cap at 100% for cleaner reporting
    cpuPercent = Math.min(cpuPercent, 100);
  }
  
  // Store current values for next call
  prevCpuUsage = currentCpuUsage;
  prevTimestamp = currentTime;

  // Get memory information
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryPercent = (usedMemory / totalMemory) * 100;

  // Memory info object
  const memoryInfo: MemoryInfo = {
    available: freeMemory,
    percent: Number(memoryPercent.toFixed(1)),
    total: totalMemory
  };

  // Response data
  return {
    process: {
      cpu_percent: Number(cpuPercent.toFixed(1)),
      memory_info: memoryInfo,
      memory_percent: Number(memoryPercent.toFixed(15)),
      pid
    },
    status: "UP",
    timestamp: Date.now() / 1000
  };
}

export function getErrorHealthStatus(): HealthCheckResponse {
  return {
    process: {
      cpu_percent: 0,
      memory_info: {
        available: 0,
        percent: 0,
        total: 0
      },
      memory_percent: 0,
      pid: process.pid || 0
    },
    status: "DOWN",
    timestamp: Date.now() / 1000
  };
} 