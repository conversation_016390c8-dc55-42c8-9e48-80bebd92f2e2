import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_PRICING_CONTENT_BY_SLUG = gql`
  query PricingBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      ProductCard {
        productName
        productLogo {
          url
        }
      }
      sections {
        ... on ComponentSectionsPricingSection {
          id
          heading
          title
          subscriptionPlan {
            monthlyPricing
            yearlyPricing
            tag
            name
            plan_code_monthly
            plan_code_yearly
            id
            description
            button {
              text
              id
            }
            fetaures {
              feature
              isIncluded
              id
            }
          }
          enterprisePlan {
            name
            tag
            description
            id
            button {
              text
              id
            }
            features {
              feature
              isIncluded
              id
            }
          }
          trialPlan {
            trialDurationInDays
            tag
            plan_code
            name
            id
            features {
              isIncluded
              feature
              id
            }
            description
            button {
              text
              id
            }
          }
        }
      }
    }
  }
`;

export const getPricingContentBySlug = async (slug: string) => {
  try {
    console.log(`Fetching pricing data for slug: ${slug}`);
    const { data } = await client.query({
      query: GET_PRICING_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    
    if (!data.products || !data.products[0]) {
      console.error("No product found for slug:", slug);
      return null;
    }
    
    const product = data.products[0];
    
    // Match the exact structure that works in other sections
    const pricingSection = product.sections.find(
      (section: { __typename: string }) => section.__typename === "ComponentSectionsPricingSection"
    );
    
    if (!pricingSection) {
      console.error("No pricing section found for slug:", slug);
      return null;
    }
    
    // Add product information to the pricing section
    const enhancedPricingSection = {
      ...pricingSection,
      productInfo: {
        productName: product.ProductCard?.productName || "",
        productLogo: product.ProductCard?.productLogo?.url || null
      }
    };
    
    // Log product info for debugging
    console.log("Product info extracted:", {
      name: product.ProductCard?.productName,
      logoUrl: product.ProductCard?.productLogo?.url
    });
    
    return enhancedPricingSection || null;
  } catch (error) {
    console.error("Error fetching pricing section data:", error);
    throw error;
  }
};
