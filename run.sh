#!/bin/bash

echo "Checking for running containers..."
if sudo docker ps --filter "name=onebiz" --format "{{.Names}}" | grep -q "onebiz"; then
    echo "Removing old container..."
    sudo docker stop onebiz
    sudo docker rm onebiz
else
    echo "Container 'onebiz' is not running."
fi

echo "Removing old Image if available..."
sudo docker rmi onebiz &> /dev/null

echo "Clearing cache..."
sudo docker builder prune -af &> /dev/null

echo "Building Fresh docker image..."
sudo docker build -t onebiz . &> /dev/null

echo "Starting the container..."
sudo docker run -d --name onebiz -p 3000:3000 onebiz &> /dev/null

echo "The application is started on port :3000"