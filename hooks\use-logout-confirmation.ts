"use client";

import { useState } from 'react';
import { signOut } from 'next-auth/react';

export interface LogoutOptions {
  callbackUrl?: string;
  clearLocalStorage?: boolean;
  clearSessionStorage?: boolean;
  clearCookies?: boolean;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Custom hook for logout confirmation functionality
 * Provides a consistent logout experience with confirmation dialog
 */
export function useLogoutConfirmation() {
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);

  /**
   * Show the logout confirmation dialog
   */
  const confirmLogout = () => {
    setShowLogoutDialog(true);
  };

  /**
   * Handle the actual logout with cleanup
   */
  const handleLogout = async (options: LogoutOptions = {}) => {
    const {
      callbackUrl = '/',
      clearLocalStorage = true,
      clearSessionStorage = true,
      clearCookies = true,
      onSuccess,
      onError
    } = options;

    try {
      // Client-side cleanup before calling signOut
      if (clearLocalStorage) {
        // Clear authentication-related items from localStorage
        const keysToRemove = [
          'keycloak-token',
          'auth_return_url',
          'user_id',
          'user_email',
          'user_name',
          'is_keycloak_user'
        ];
        
        keysToRemove.forEach(key => {
          try {
            localStorage.removeItem(key);
          } catch (error) {
            console.warn(`Failed to remove ${key} from localStorage:`, error);
          }
        });
      }

      if (clearSessionStorage) {
        // Clear authentication-related items from sessionStorage
        try {
          sessionStorage.removeItem('keycloak-token');
        } catch (error) {
          console.warn('Failed to clear sessionStorage:', error);
        }
      }

      if (clearCookies) {
        // Clear all cookies with path='/'
        try {
          document.cookie.split(';').forEach(cookie => {
            const [name] = cookie.trim().split('=');
            if (name) {
              document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
            }
          });
        } catch (error) {
          console.warn('Failed to clear cookies:', error);
        }
      }

      // Perform NextAuth signOut
      await signOut({ callbackUrl });
      
      // Call success callback if provided
      onSuccess?.();
      
    } catch (error) {
      console.error("Error during logout:", error);
      
      // Call error callback if provided
      onError?.(error instanceof Error ? error : new Error('Logout failed'));
      
      // Fallback to basic signOut if cleanup fails
      try {
        signOut({ callbackUrl });
      } catch (fallbackError) {
        console.error("Fallback logout also failed:", fallbackError);
      }
    }
  };

  /**
   * Close the logout dialog
   */
  const closeLogoutDialog = () => {
    setShowLogoutDialog(false);
  };

  return {
    showLogoutDialog,
    confirmLogout,
    handleLogout,
    closeLogoutDialog,
    setShowLogoutDialog
  };
} 