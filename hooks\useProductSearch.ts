/**
 * React Query hook for searching products using Meilisearch
 */
import { useQuery } from '@tanstack/react-query';
import { useState, useCallback, useRef } from 'react';
import { MeilisearchHit, MeilisearchResponse, SearchHit, SearchState } from '@/types/search';
import Logger from '@/utils/logger';

/**
 * Generate a section anchor from a section component name and ID
 */
export const generateSectionAnchor = (component: string, id: number): string => {
  return `${component.replace('sections.', '')}-${id}`;
};

/**
 * Hook for searching products with Meilisearch
 */
export function useProductSearch(initialQuery: string = '') {
  const [query, setQuery] = useState<string>(initialQuery);
  const [searchState, setSearchState] = useState<SearchState>({
    isLoading: false,
    error: null,
    data: null,
    latency: null,
  });

  // Use a ref to track if a search is in progress
  const searchInProgressRef = useRef<boolean>(false);

  // Function to search Meilisearch
  const searchProducts = useCallback(async (searchQuery: string): Promise<SearchHit[]> => {
    if (!searchQuery.trim()) {
      return [];
    }

    const startTime = performance.now();
    console.log(`Searching for: "${searchQuery}"`);

    try {
      // Get Meilisearch API key and URL from environment variables
      const apiKey = process.env.NEXT_PUBLIC_MEILISEARCH_API_KEY || 'ohYQooC62EJ7VXAX96CAifTXL';
      const searchUrl = process.env.NEXT_PUBLIC_MEILISEARCH_URL || 'http://localhost:7700/indexes/product/search';

      // Log the API key for debugging (remove in production)
      console.log(`Using API key: ${apiKey.substring(0, 4)}...`);
      console.log(`Using search URL: ${searchUrl}`);

      // Make the search request
      console.log('Making search request...');
      const response = await fetch(searchUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify({ q: searchQuery }),
      });

      if (!response.ok) {
        throw new Error(`Search failed with status: ${response.status}`);
      }

      const result: MeilisearchResponse = await response.json();
      const endTime = performance.now();
      const latency = endTime - startTime;

      console.log(`Search completed in ${latency.toFixed(2)}ms with ${result.hits.length} hits`);
      console.log('Raw search response:', result);

      // For debugging, log the first hit
      if (result.hits.length > 0) {
        console.log('First hit:', result.hits[0]);
      }

      // Transform the raw hits into the format we need
      const transformedHits = transformSearchHits(result.hits);
      console.log(`Transformed ${transformedHits.length} hits`);

      return transformedHits;
    } catch (error) {
      console.error(`Search failed:`, error);

      // Return empty array instead of throwing to avoid crashing the UI
      return [];
    }
  }, []);

  // Transform raw Meilisearch hits into our SearchHit format
  const transformSearchHits = (hits: MeilisearchHit[]): SearchHit[] => {
    const transformedHits: SearchHit[] = [];
    console.log('Transforming hits:', JSON.stringify(hits, null, 2));

    if (!hits || hits.length === 0) {
      console.log('No hits to transform');
      return [];
    }

    hits.forEach(hit => {
      // Make sure ProductCard exists
      if (!hit.ProductCard) {
        console.warn(`Hit missing ProductCard: ${hit.id}`);
        return;
      }

      const productName = hit.ProductCard.productName || 'Unknown Product';
      const slug = hit.slug;

      console.log(`Processing hit: ${productName} (${slug})`);

      // Always add a product-level hit
      transformedHits.push({
        productName,
        slug,
        sectionTitle: null,
        sectionAnchor: null,
      });

      // If there are sections, add a hit for each section
      if (hit.sections && hit.sections.length > 0) {
        console.log(`Found ${hit.sections.length} sections for ${productName}`);

        hit.sections.forEach(section => {
          if (section.__component && section.id) {
            const sectionAnchor = generateSectionAnchor(section.__component, section.id);
            const sectionTitle = section.title || section.__component.split('.').pop() || null;

            console.log(`Processing section: ${sectionTitle} (${sectionAnchor})`);

            // Only add if we have a valid title
            if (sectionTitle) {
              transformedHits.push({
                productName,
                slug,
                sectionTitle,
                sectionAnchor,
              });
            }
          }
        });
      }
    });

    console.log(`Transformed ${transformedHits.length} hits:`, transformedHits);
    return transformedHits;
  };

  // Use a ref to track the current search query to prevent race conditions
  const currentSearchRef = useRef<string>('');

  // Use a timeout ref to handle debouncing manually
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Track if initial search has been performed
  const initialSearchPerformedRef = useRef<boolean>(false);

  // Use a simpler approach without React Query's refetch
  const search = useCallback(async (newQuery: string) => {
    // Store the query we're about to search for
    currentSearchRef.current = newQuery;
    setQuery(newQuery);

    // If query is empty, reset the search state
    if (!newQuery.trim()) {
      setSearchState({
        isLoading: false,
        error: null,
        data: null,
        latency: null,
      });
      return;
    }

    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set loading state immediately
    setSearchState(prev => {
      console.log('Setting loading state, previous state:', prev);
      return {
        ...prev,
        isLoading: true,
        // Important: Don't clear the data yet to prevent flashing
        // data: null
      };
    });

    // Execute search immediately for the first time
    const shouldExecuteImmediately = !initialSearchPerformedRef.current;

    // Function to execute the search
    const executeSearch = async () => {
      // Only proceed if this is still the current query
      if (currentSearchRef.current !== newQuery) {
        return;
      }

      // Mark that a search is in progress and that initial search has been performed
      searchInProgressRef.current = true;
      initialSearchPerformedRef.current = true;

      try {
        const startTime = performance.now();
        console.log(`Executing search for query: "${newQuery}"`);
        const results = await searchProducts(newQuery);
        const endTime = performance.now();

        console.log(`Search completed for "${newQuery}", got ${results.length} results`);

        // Only update state if this is still the current search
        if (currentSearchRef.current === newQuery) {
          console.log(`Updating state with ${results.length} results for "${newQuery}"`);
          console.log(`Setting final state with ${results.length} results`);
          setSearchState({
            isLoading: false,
            error: null,
            data: results,
            latency: endTime - startTime,
          });
          console.log('State updated with search results');
        } else {
          console.log(`Not updating state for "${newQuery}", current query is "${currentSearchRef.current}"`);
        }
      } catch (error) {
        console.error(`Error searching for query: "${newQuery}"`, error);

        // Only update state if this is still the current search
        if (currentSearchRef.current === newQuery) {
          setSearchState({
            isLoading: false,
            error: error instanceof Error ? error : new Error('Unknown search error'),
            data: null,
            latency: null,
          });
        }
      } finally {
        // Mark that the search is no longer in progress
        searchInProgressRef.current = false;
      }
    };

    // Either execute immediately or after a short delay
    if (shouldExecuteImmediately) {
      console.log('Executing search immediately for first time');
      executeSearch();
    } else {
      // Use a timeout to prevent too many API calls and state updates
      searchTimeoutRef.current = setTimeout(executeSearch, 100); // Small delay to batch rapid keystrokes
    }
  }, [searchProducts]);

  return {
    search,
    query,
    ...searchState,
  };
}
