"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useLogoutConfirmation } from '@/hooks/use-logout-confirmation';
import { LogoutConfirmationDialog } from './logout-confirmation-dialog';

interface LogoutButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showIcon?: boolean;
  children?: React.ReactNode;
  callbackUrl?: string;
}

/**
 * LogoutButton - A reusable logout button with confirmation dialog
 * Can be used anywhere in the app for consistent logout experience
 */
export function LogoutButton({
  variant = "destructive",
  size = "default",
  className = "",
  showIcon = true,
  children = "Logout",
  callbackUrl = "/"
}: LogoutButtonProps) {
  const { data: session } = useSession();
  const { showLogoutDialog, confirmLogout, handleLogout, closeLogoutDialog } = useLogoutConfirmation();

  // Don't render if user is not logged in
  if (!session) {
    return null;
  }

  const handleLogoutWithCallback = () => {
    handleLogout({ 
      callbackUrl,
      onSuccess: () => {
        console.log("✅ LOGOUT BUTTON - User successfully logged out");
      },
      onError: (error) => {
        console.error("❌ LOGOUT BUTTON - Logout error:", error);
      }
    });
  };

  return (
    <>
      <Button
        onClick={confirmLogout}
        variant={variant}
        size={size}
        className={className}
        aria-label="Logout"
      >
        {showIcon && <LogOut className="w-4 h-4 mr-2" />}
        {children}
      </Button>

      <LogoutConfirmationDialog
        open={showLogoutDialog}
        onOpenChange={closeLogoutDialog}
        onConfirm={handleLogoutWithCallback}
        userName={session?.user?.name || undefined}
      />
    </>
  );
}

/**
 * Example usage:
 * 
 * // Basic usage
 * <LogoutButton />
 * 
 * // Custom styling
 * <LogoutButton 
 *   variant="outline" 
 *   size="sm" 
 *   className="ml-2"
 * >
 *   Sign Out
 * </LogoutButton>
 * 
 * // Without icon
 * <LogoutButton showIcon={false}>
 *   Leave
 * </LogoutButton>
 * 
 * // Custom callback URL
 * <LogoutButton callbackUrl="/goodbye">
 *   Exit
 * </LogoutButton>
 */ 