"use client";

import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>Footer,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertCircle, HelpCircle } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

interface PlanChangeBlockedDialogProps {
  open: boolean;
  onClose: () => void;
  message?: string;
  actionType: 'change' | 'cancel' | 'switch';
}

export function PlanChangeBlockedDialog({
  open,
  onClose,
  message,
  actionType
}: PlanChangeBlockedDialogProps) {
  const [showContactInfo, setShowContactInfo] = useState(false);

  // Get appropriate title and default message based on action type
  const getTitle = () => {
    switch (actionType) {
      case 'change':
        return "Plan Change Not Available";
      case 'cancel':
        return "Subscription Cancellation Not Available";
      case 'switch':
        return "Product Switch Not Available";
      default:
        return "Action Not Available";
    }
  };

  const getDefaultMessage = () => {
    switch (actionType) {
      case 'change':
        return "To change your subscription plan, please contact support.";
      case 'cancel':
        return "Subscription cancellation is currently not supported.";
      case 'switch':
        return "Each product has its own isolated plans. You cannot switch between products.";
      default:
        return "This action is not available through the app. Please contact support for assistance.";
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            {getTitle()}
          </DialogTitle>
          <DialogDescription>
            {message || getDefaultMessage()}
          </DialogDescription>
        </DialogHeader>

        <Alert variant="destructive" className="mt-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Action not available in-app</AlertTitle>
          <AlertDescription>
            This action requires assistance from our support team.
          </AlertDescription>
        </Alert>

        {showContactInfo ? (
          <div className="bg-muted p-4 rounded-lg mt-4">
            <h4 className="font-medium mb-2">Contact Support</h4>
            <p className="text-sm mb-2">
              Please reach out to our support team for assistance with your subscription:
            </p>
            <ul className="text-sm space-y-2">
              <li>Email: <a href="mailto:<EMAIL>" className="text-primary"><EMAIL></a></li>
              <li>Phone: <a href="tel:+1234567890" className="text-primary">+1 (234) 567-890</a></li>
              <li>Support Hours: Monday-Friday, 9 AM - 6 PM IST</li>
            </ul>
          </div>
        ) : null}

        <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
          <Button 
            variant="outline" 
            onClick={onClose}
            className="sm:mr-auto"
          >
            Close
          </Button>
          
          <Button
            variant={showContactInfo ? "outline" : "default"}
            onClick={() => setShowContactInfo(!showContactInfo)}
            className="flex items-center gap-2"
          >
            {showContactInfo ? "Hide Contact Info" : "Contact Support"}
            <HelpCircle className="h-4 w-4" />
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
