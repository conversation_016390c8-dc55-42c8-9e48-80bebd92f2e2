"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { SubscriptionCard } from "@/components/subscription/subscription-card";
import { Button } from "@/components/ui/button";
import { addToDate } from "@/src/utils/date-utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function SubscriptionCardDemo() {
  const [customPlanCode, setCustomPlanCode] = useState("onerestro_premium_y");
  const [customStatus, setCustomStatus] = useState("active");
  
  // Generate dates for active subscriptions
  const now = new Date();
  const oneMonthAgo = addToDate(now, -1, "months");
  const twoMonthsAgo = addToDate(now, -2, "months");
  
  const oneMonthFromNow = addToDate(now, 1, "months");
  const threeMonthsFromNow = addToDate(now, 3, "months");
  const oneWeekFromNow = addToDate(now, 1, "weeks");
  const oneDayFromNow = addToDate(now, 1, "days");
  const expiredDate = addToDate(now, -1, "days");
  
  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">Subscription Card Component</h1>
      <p className="text-muted-foreground mb-8">
        A showcase of the subscription card component in different states.
      </p>
      
      <Tabs defaultValue="gallery">
        <TabsList className="mb-6">
          <TabsTrigger value="gallery">Gallery</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>
        
        <TabsContent value="gallery">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Active subscription */}
            <Card>
              <CardHeader>
                <CardTitle>Active Subscription</CardTitle>
                <CardDescription>A typical active subscription</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode="onerestro_premium_m"
                  status="active"
                  startedAt={twoMonthsAgo.toISOString()}
                  expiresAt={threeMonthsFromNow.toISOString()}
                  onManage={() => alert("Manage clicked")}
                  onRenew={() => alert("Renew clicked")}
                />
              </CardContent>
            </Card>
            
            {/* Trial subscription */}
            <Card>
              <CardHeader>
                <CardTitle>Trial Subscription</CardTitle>
                <CardDescription>A trial subscription</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode="onerestro_trial"
                  status="active"
                  startedAt={now.toISOString()}
                  expiresAt={oneMonthFromNow.toISOString()}
                  onManage={() => alert("Manage clicked")}
                />
              </CardContent>
            </Card>
            
            {/* Expiring soon */}
            <Card>
              <CardHeader>
                <CardTitle>Expiring Soon</CardTitle>
                <CardDescription>Subscription expiring soon</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode="onerestro_starter_y"
                  status="active"
                  startedAt={oneMonthAgo.toISOString()}
                  expiresAt={oneWeekFromNow.toISOString()}
                  onManage={() => alert("Manage clicked")}
                  onRenew={() => alert("Renew clicked")}
                />
              </CardContent>
            </Card>
            
            {/* Expiring very soon */}
            <Card>
              <CardHeader>
                <CardTitle>Expiring Tomorrow</CardTitle>
                <CardDescription>Subscription about to expire</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode="onebiz_enterprise_m"
                  status="active" 
                  startedAt={oneMonthAgo.toISOString()}
                  expiresAt={oneDayFromNow.toISOString()}
                  onManage={() => alert("Manage clicked")}
                  onRenew={() => alert("Renew clicked")}
                />
              </CardContent>
            </Card>
            
            {/* Expired */}
            <Card>
              <CardHeader>
                <CardTitle>Expired</CardTitle>
                <CardDescription>An expired subscription</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode="onebiz_premium_m"
                  status="terminated"
                  startedAt={twoMonthsAgo.toISOString()}
                  expiresAt={expiredDate.toISOString()}
                  onManage={() => alert("Manage clicked")}
                  onRenew={() => alert("Renew clicked")}
                />
              </CardContent>
            </Card>
            
            {/* Pending */}
            <Card>
              <CardHeader>
                <CardTitle>Pending</CardTitle>
                <CardDescription>A pending subscription</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode="onebiz_starter_y" 
                  status="pending"
                  startedAt={now.toISOString()}
                  onManage={() => alert("Manage clicked")}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="custom">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Custom Subscription</CardTitle>
                <CardDescription>Configure the subscription parameters</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="planCode">Plan Code</Label>
                  <Input
                    id="planCode"
                    value={customPlanCode}
                    onChange={(e) => setCustomPlanCode(e.target.value)}
                    placeholder="e.g., onerestro_premium_m"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={customStatus} onValueChange={setCustomStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="canceled">Canceled</SelectItem>
                      <SelectItem value="terminated">Terminated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setCustomPlanCode(customPlanCode + "_trial")}
                    className="mr-2"
                  >
                    Make Trial
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => {
                      const base = customPlanCode.split('_')[0];
                      setCustomPlanCode(`${base}_trial`);
                    }}
                  >
                    Reset to Trial
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>The subscription card with your configuration</CardDescription>
              </CardHeader>
              <CardContent>
                <SubscriptionCard
                  planCode={customPlanCode}
                  status={customStatus}
                  startedAt={oneMonthAgo.toISOString()}
                  expiresAt={oneMonthFromNow.toISOString()}
                  onManage={() => alert("Manage clicked")}
                  onRenew={() => alert("Renew clicked")}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 