variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

stages:
  - pull_changes
  - install_dependency
  - deploy

pull_changes:
  stage: pull_changes
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh
    - chmod 0400 $AWS_PEM_FILE
  script:
    - chmod 0400 $AWS_PEM_FILE
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@$SERVER_IP "cd /home/<USER>/saas-ui-v2/ && git stash && git pull ${CI_REPOSITORY_URL} stg"
  only:
    - stg

install_dependency:
  stage: install_dependency
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh
    - chmod 0400 $AWS_PEM_FILE
  script:
    - chmod 0400 $AWS_PEM_FILE
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@$SERVER_IP "cd /home/<USER>/saas-ui-v2/ && sudo chown -R ubuntu:ubuntu /home/<USER>/saas-ui-v2/ && npm install"
  only:
    - stg

deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache openssh
    - chmod 0400 $AWS_PEM_FILE
  script:
    - chmod 0400 $AWS_PEM_FILE
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@$SERVER_IP "cd /home/<USER>/saas-ui-v2/ && pm2 restart ecosystem.config.json"
  only:
    - stg