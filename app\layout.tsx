import { Inter, Bricolage_Grotesque } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { Navbar } from "@/components/layout/navbar";
import { Toaster } from "@/components/ui/sonner";
import { Providers } from "@/components/providers";
import { Suspense } from "react";

const inter = Inter({ subsets: ["latin"] });
const bricolageGrotesque = Bricolage_Grotesque({
  subsets: ["latin"],
  variable: "--bricolage-grotesque",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-br" suppressHydrationWarning>
      <head>
        {/* Early loader script - shows loader immediately */}
        <script dangerouslySetInnerHTML={{
          __html: `
            (function() {
              // Create and show initial loader before React hydrates
              const loader = document.createElement('div');
              loader.id = 'initial-loader';
              loader.style.position = 'fixed';
              loader.style.top = '0';
              loader.style.left = '0';
              loader.style.width = '100%';
              loader.style.height = '100%';
              loader.style.backgroundColor = 'var(--background, #fff)';
              loader.style.zIndex = '9999';
              loader.style.display = 'flex';
              loader.style.alignItems = 'center';
              loader.style.justifyContent = 'center';
              
              // Add spinner
              const spinner = document.createElement('div');
              spinner.style.width = '50px';
              spinner.style.height = '50px';
              spinner.style.border = '3px solid rgba(var(--primary, #000), 0.1)';
              spinner.style.borderRadius = '50%';
              spinner.style.borderTop = '3px solid rgba(var(--primary, #000), 0.8)';
              spinner.style.animation = 'spin 1s linear infinite';
              
              // Add animation
              const style = document.createElement('style');
              style.innerHTML = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
              
              document.head.appendChild(style);
              loader.appendChild(spinner);
              
              // Add to DOM when ready
              if (document.body) {
                document.body.appendChild(loader);
              } else {
                window.addEventListener('DOMContentLoaded', function() {
                  document.body.appendChild(loader);
                });
              }
              
              // Remove initial loader when React loader is ready
              window.removeInitialLoader = function() {
                const loader = document.getElementById('initial-loader');
                if (loader) {
                  loader.style.opacity = '0';
                  loader.style.transition = 'opacity 0.3s ease';
                  setTimeout(function() {
                    loader.remove();
                  }, 300);
                }
              };
            })();
          `
        }} />
      </head>
      <body
        className={cn("min-h-screen bg-background", inter.className, bricolageGrotesque.variable)}
      >
        <Suspense fallback={null}>
          <Providers>
            <Navbar />
            {children}
            <Toaster position="bottom-left" />
          </Providers>
        </Suspense>
      </body>
    </html>
  );
}
