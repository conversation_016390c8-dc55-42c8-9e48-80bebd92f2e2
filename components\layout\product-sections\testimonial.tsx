"use client";

import { useEffect, useState, forwardRef } from "react";
import { getTestimonialsContentBySlug } from "@/app/api/testimonialSection";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Star } from "lucide-react";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";

type Testimonial = {
  id: string;
  name: string;
  photo?: { url: string } | null;
  rating: number;
  designation: string;
  description: string;
};

type TestimonialSectionProps = {
  slug: string;
  sectionComponent?: string;
  sectionId?: number;
};

export const TestimonialSection = forwardRef<HTMLDivElement, TestimonialSectionProps>(
  ({ slug, sectionComponent, sectionId }, ref) => {
    const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
    const [title, setTitle] = useState<string>("");

    useEffect(() => {
      const fetchData = async () => {
        try {
          const data = await getTestimonialsContentBySlug(slug);
          if (data) {
            setTestimonials(data.testimonials);
            setTitle(data.title);
          }
        } catch (error) {
          console.error("Error fetching testimonials:", error);
        }
      };

      fetchData();
    }, [slug]);

    // Function to get initials safely
    const getInitials = (name: string | undefined | null): string => {
      if (!name) return '?';
      return name.charAt(0) || '?';
    };

    return (
      <SectionContainer
        id="testimonials"
        ref={ref}
        sectionComponent={sectionComponent}
        sectionId={sectionId}
      >
        <SectionHeader subTitle="Testimonials" title={title} />
        <Carousel
          opts={{
            align: "start",
          }}
          className="relative w-[80%] sm:w-[90%] lg:max-w-screen-xl mx-auto"
        >
          <CarouselContent>
            {testimonials.map((review) => (
              <CarouselItem
                key={review.id}
                className="md:basis-1/2 lg:basis-1/3"
              >
                <Card className="bg-muted">
                  <CardContent className="pt-6 pb-0">
                    <div className="flex gap-1 pb-6">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="size-4 fill-orange-400 text-orange-400" />
                      ))}
                    </div>
                    {`"${review.description}"`}
                  </CardContent>

                  <CardHeader>
                    <div className="flex flex-row items-center gap-4">
                      <Avatar>
                        <AvatarImage src={review.photo?.url || '/placeholder-avatar.png'} alt={review.name} />
                        <AvatarFallback>{getInitials(review.name)}</AvatarFallback>
                      </Avatar>

                      <div className="flex flex-col">
                        <CardTitle className="text-lg">{review.name}</CardTitle>
                        <CardDescription>{review.designation}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </SectionContainer>
    );
  }
);

TestimonialSection.displayName = "TestimonialSection";
