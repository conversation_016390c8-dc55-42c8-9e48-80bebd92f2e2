"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, AlertCircle, CheckCircle, XCircle, ArrowRight, Loader2 } from "lucide-react";

interface TrialStatusDialogProps {
  open: boolean;
  onClose: () => void;
  productName: string;
  status: {
    isLoading: boolean;
    hasActiveSubscription: boolean;
    hasExpiredSubscription: boolean;
    hasPendingSubscription?: boolean;
    hasUsedTrial: boolean;
    trialStatus?: string;
    subscriptionDetails?: {
      planName: string;
      isPaidPlan: boolean;
      isTrialPlan: boolean;
      isPending?: boolean;
      daysRemaining?: number;
      expirationDate?: string;
    };
  };
  onViewPricing: () => void;
  onStartTrial: () => void;
  onGoToDashboard?: () => void;
  onContactSupport?: () => void;
}

export function TrialStatusDialog({
  open,
  onClose,
  productName,
  status,
  onViewPricing,
  onStartTrial,
  onGoToDashboard,
  onContactSupport = () => window.open('mailto:<EMAIL>', '_blank'),
}: TrialStatusDialogProps) {
  // Render content based on status
  const renderContent = () => {
    if (status.isLoading) {
      return (
        <div className="py-8 text-center">
          <Loader2 className="h-12 w-12 mx-auto animate-spin text-primary" />
          <p className="mt-4 text-muted-foreground">Checking your subscription status...</p>
        </div>
      );
    }

    if (status.hasPendingSubscription) {
      return (
        <div className="space-y-6">
          <div className="rounded-lg bg-blue-50 dark:bg-blue-950 p-4 text-blue-800 dark:text-blue-200">
            <h4 className="font-medium flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Trial Pending Activation
            </h4>
            <p className="text-sm mt-1 text-blue-700 dark:text-blue-300">
              Your trial for {productName} is pending activation. This usually takes a few minutes.
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">
                      {status.subscriptionDetails?.planName || "Trial Plan"}
                    </h3>
                    <div className="mt-1">
                      <Badge className="bg-blue-500/10 text-blue-600 hover:bg-blue-500/20">
                        <Clock className="h-3.5 w-3.5 mr-1" />
                        Pending
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="flex justify-end">
                  {onGoToDashboard && (
                    <Button
                      className="mr-2"
                      onClick={() => {
                        window.open(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`, '_blank');
                        onGoToDashboard?.();
                      }}>
                      Check Dashboard
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (status.hasActiveSubscription) {
      const details = status.subscriptionDetails;
      return (
        <div className="space-y-6">
          <div className="rounded-lg bg-green-50 dark:bg-green-950 p-4 text-green-800 dark:text-green-200">
            <h4 className="font-medium flex items-center">
              <CheckCircle className="h-4 w-4 mr-2" />
              Active Subscription
            </h4>
            <p className="text-sm mt-1 text-green-700 dark:text-green-300">
              You have an active {details?.isTrialPlan ? "trial" : "paid"} subscription for {productName}.
            </p>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">{details?.planName || "Current Plan"}</h3>
                    <div className="mt-1">
                      <Badge className="bg-green-500/10 text-green-600 hover:bg-green-500/20">
                        <CheckCircle className="h-3.5 w-3.5 mr-1" />
                        Active
                      </Badge>
                      {details?.isTrialPlan && (
                        <Badge className="ml-2 bg-blue-500/10 text-blue-600 hover:bg-blue-500/20">
                          <Clock className="h-3.5 w-3.5 mr-1" />
                          Trial
                        </Badge>
                      )}
                    </div>
                  </div>
                  {details?.daysRemaining !== undefined && (
                    <div className="text-right">
                      <span className="block text-sm font-medium">Time Remaining</span>
                      <span className="text-sm text-muted-foreground">
                        {details.daysRemaining} days
                      </span>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="flex justify-end">
                  {onGoToDashboard && (
                    <Button
                      className="mr-2"
                      onClick={() => {
                        window.open(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`, '_blank');
                        onGoToDashboard?.();
                      }}>
                      Go to Dashboard
                    </Button>
                  )}
                  {details?.isTrialPlan && (
                    <Button onClick={onViewPricing} variant="outline">
                      Upgrade Plan
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (status.hasExpiredSubscription) {
      return (
        <div className="space-y-6">
          <div className="rounded-lg bg-amber-50 dark:bg-amber-950 p-4 text-amber-800 dark:text-amber-200">
            <h4 className="font-medium flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              Subscription Expired
            </h4>
            <p className="text-sm mt-1 text-amber-700 dark:text-amber-300">
              Your subscription to {productName} has expired. Upgrade to continue accessing all features.
            </p>
          </div>

          <div className="flex justify-center">
            <Button onClick={onViewPricing} size="lg">
              View Plans <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      );
    }

    if (status.hasUsedTrial) {
      // Determine the appropriate message based on trial status
      let title = "Trial Already Used";
      let description = `You have already used the trial for ${productName}. Consider upgrading to a paid plan.`;
      let bgClass = "bg-amber-50 dark:bg-amber-950";
      let textClass = "text-amber-800 dark:text-amber-200";
      let descTextClass = "text-amber-700 dark:text-amber-300";

      // Check for pending status
      if (status.trialStatus === "pending") {
        title = "Trial Pending Activation";
        description = `Your trial for ${productName} is currently pending activation.`;
        bgClass = "bg-blue-50 dark:bg-blue-950";
        textClass = "text-blue-800 dark:text-blue-200";
        descTextClass = "text-blue-700 dark:text-blue-300";
      }
      // Check for product family scenarios
      else if (status.trialStatus?.includes("product family")) {
        if (status.trialStatus?.includes("paid plan")) {
          title = "Paid Plan Already Active";
          description = `You already have a paid subscription in this product family. Please check your account for details.`;
          bgClass = "bg-blue-50 dark:bg-blue-950";
          textClass = "text-blue-800 dark:text-blue-200";
          descTextClass = "text-blue-700 dark:text-blue-300";
        } else {
          title = "Trial Used in Product Family";
          description = `You have already used a trial within this product family. Each customer is eligible for one trial per product family.`;
        }
      }

      return (
        <div className="space-y-6">
          <div className={`rounded-lg ${bgClass} p-4 ${textClass}`}>
            <h4 className="font-medium flex items-center">
              {status.trialStatus === "pending" ?
                <Clock className="h-4 w-4 mr-2" /> :
                <XCircle className="h-4 w-4 mr-2" />
              }
              {title}
            </h4>
            <p className={`text-sm mt-1 ${descTextClass}`}>
              {description}
            </p>
          </div>

          <div className="flex justify-center">
            <Button onClick={onViewPricing} size="lg">
              View Plans <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      );
    }

    // Default case: Eligible for trial
    return (
      <div className="space-y-6">
        <div className="rounded-lg bg-blue-50 dark:bg-blue-950 p-4 text-blue-800 dark:text-blue-200">
          <h4 className="font-medium flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            Trial Available
          </h4>
          <p className="text-sm mt-1 text-blue-700 dark:text-blue-300">
            You&apos;re eligible to start a free trial of {productName}. No credit card required.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button onClick={onStartTrial} size="lg" className="w-full sm:w-auto">
            Start Free Trial <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Button onClick={onContactSupport} size="lg" variant="outline" className="w-full sm:w-auto">
            Need Help? Contact Us
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Trial Status</DialogTitle>
          <DialogDescription>
            Check your trial eligibility for {productName}.
          </DialogDescription>
        </DialogHeader>
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
}