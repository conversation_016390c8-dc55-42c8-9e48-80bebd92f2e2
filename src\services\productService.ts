import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

// Define the GraphQL query to fetch product information
const GET_PRODUCT_BY_SLUG = gql`
  query ProductBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      slug
      ProductCard {
        productName
        productDescription
        productLogo {
          url
        }
        productRedirectURL
        productCategory
      }
    }
  }
`;

// Define the GraphQL query to fetch all products
const GET_ALL_PRODUCTS = gql`
  query Products {
    products {
      slug
      ProductCard {
        productRedirectURL
        productName
        productLogo {
          url
        }
        productCategory
        productDescription
      }
    }
  }
`;

/**
 * Interface for product information
 */
export interface ProductInfo {
  slug: string;
  ProductCard: {
    productName: string;
    productDescription?: string;
    productLogo?: {
      url: string;
    };
    productRedirectURL?: string | null;
    productCategory?: string;
  };
}

/**
 * Get product information by slug
 * @param slug - The product slug
 * @returns Product information or null if not found
 */
export async function getProductBySlug(slug: string): Promise<ProductInfo | null> {
  try {
    console.log(`🔍 PRODUCT SERVICE - Fetching product data for slug: ${slug}`);

    const { data } = await client.query({
      query: GET_PRODUCT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });

    if (!data.products || data.products.length === 0) {
      console.error(`❌ PRODUCT SERVICE - No product found for slug: ${slug}`);
      return null;
    }

    console.log(`✅ PRODUCT SERVICE - Found product for slug: ${slug}`);
    return data.products[0];
  } catch (error) {
    console.error(`❌ PRODUCT SERVICE - Error fetching product data:`, error);
    throw error;
  }
}

/**
 * Get all products
 * @returns Array of product information
 */
export async function getAllProducts(): Promise<ProductInfo[]> {
  try {
    console.log(`🔍 PRODUCT SERVICE - Fetching all products`);

    const { data } = await client.query({
      query: GET_ALL_PRODUCTS,
      fetchPolicy: "no-cache",
    });

    if (!data.products || data.products.length === 0) {
      console.error(`❌ PRODUCT SERVICE - No products found`);
      return [];
    }

    console.log(`✅ PRODUCT SERVICE - Found ${data.products.length} products`);
    return data.products;
  } catch (error) {
    console.error(`❌ PRODUCT SERVICE - Error fetching all products:`, error);
    throw error;
  }
}

/**
 * Get product redirect URL by slug
 * @param slug - The product slug
 * @returns The product redirect URL or null if not found
 */
export async function getProductRedirectURL(slug: string): Promise<string | null> {
  try {
    const product = await getProductBySlug(slug);
    return product?.ProductCard?.productRedirectURL || null;
  } catch (error) {
    console.error(`❌ PRODUCT SERVICE - Error fetching product redirect URL:`, error);
    return null;
  }
}

/**
 * Get product display name by slug
 * @param slug - The product slug
 * @returns The product display name or null if not found
 */
export async function getProductDisplayName(slug: string): Promise<string | null> {
  try {
    const product = await getProductBySlug(slug);
    // Simply return the product name as is without any formatting
    return product?.ProductCard?.productName || null;
  } catch (error) {
    console.error(`❌ PRODUCT SERVICE - Error fetching product display name:`, error);
    return null;
  }
}
