"use client";

import React, { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from "@/components/layout/theme-provider";
import { CartProvider } from "@/context/cart-context";
import { UserProvider } from "@/context/user-context";
import { ClientProvider } from "@/components/client-provider";
import { SubscriptionRecovery } from "@/components/payment/subscription-recovery";
import { SessionErrorHandler } from "@/components/auth/session-error-handler";
import { AuthProvider } from "@/components/auth/auth-provider";
import { LoadingProvider } from "@/context/loading-context";
import PageLoader from "@/components/ui/page-loader";
import { ReactQueryProvider } from "@/lib/react-query";

interface ProvidersProps {
  children: ReactNode;
}

/**
 * A combined provider component that wraps all context providers
 * This ensures proper nesting and avoids any React context issues
 */
export function Providers({ children }: ProvidersProps) {
  return (
    <ReactQueryProvider>
      <LoadingProvider>
        <PageLoader />
        <SessionProvider>
          <CartProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <UserProvider>
                <AuthProvider>
                  <ClientProvider>
                    <SubscriptionRecovery />
                    <SessionErrorHandler />
                    {children}
                  </ClientProvider>
                </AuthProvider>
              </UserProvider>
            </ThemeProvider>
          </CartProvider>
        </SessionProvider>
      </LoadingProvider>
    </ReactQueryProvider>
  );
}
