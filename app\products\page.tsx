"use client";

import React, { useEffect, useState, useRef } from "react";
import { gql } from "@apollo/client";
import client from "@/lib/apolloClient";
import SectionContainer from "@/components/layout/section-container";
import SectionHeader from "@/components/layout/section-header";
import { ContactSection } from "@/components/layout/sections/contact";
import {
  AlertDialog,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
} from "@/components/ui/alert-dialog";
import { Loader2, Search, Mail } from "lucide-react";
import { FooterSection } from "@/components/layout/sections/footer";
import { ShiftCardDemo } from "@/components/ui/shift-card-demo";
import { InteractiveHoverButton } from "@/components/ui/interactive-hover-button";


const GET_PRODUCTS = gql`
  query ProductCard {
    products {
      ProductCard {
        productCategory
        productName
        productLogo {
          url
        }
        productRedirectURL
        id
        productDescription
        label
      }
    }
  }
`;

interface Product {
  ProductCard: {
    productCategory: string;
    productName: string;
    productLogo: {
      url: string;
    };
    productRedirectURL: string | null;
    id: string;
    productDescription: string;
    label: string | null;
  };
}

export default function AllProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const contactSectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        const { data } = await client.query({
          query: GET_PRODUCTS,
          fetchPolicy: "no-cache",
        });
        setProducts(data.products);
        
        // Extract unique categories from products
        const categorySet = new Set<string>();
        data.products.forEach((product: Product) => {
          categorySet.add(product.ProductCard.productCategory);
        });
        setAvailableCategories(Array.from(categorySet));
        
        setError(null);
      } catch (error) {
        console.error('Failed to load products:', error);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Format a category name for display (capitalize first letter)
  const formatCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
  };

  // Filter products based on selected category and search query
  const filteredProducts = products.filter((product) => {
    const productCategory = product.ProductCard.productCategory.toLowerCase();
    const productName = product.ProductCard.productName.toLowerCase();
    const productDescription = product.ProductCard.productDescription?.toLowerCase() || '';
    const searchLower = searchQuery.toLowerCase();
    
    // If there's a search query, search across name, description and category
    if (searchQuery) {
      return (
        productName.includes(searchLower) || 
        productDescription.includes(searchLower) || 
        productCategory.includes(searchLower)
      );
    } else {
      // If no search query, filter by selected category
      return selectedCategory === "All" || product.ProductCard.productCategory === selectedCategory;
    }
  });

  // Group products by their API category
  const groupProductsByCategory = () => {
    const result: Record<string, Product[]> = {};
    
    filteredProducts.forEach(product => {
      const category = product.ProductCard.productCategory;
      if (!result[category]) {
        result[category] = [];
      }
      result[category].push(product);
    });
    
    return result;
  };

  const groupedProducts = groupProductsByCategory();

  // All available categories for the tabs
  const categories = ["All", ...availableCategories];

  // Function to handle contact button click - scroll to contact section
  const handleContactClick = () => {
    contactSectionRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    });
  };

  if (loading) {
    return (
      <SectionContainer>
        <div className="pt-16"> {/* Add padding top to avoid navbar overlap */}
          <SectionHeader
            subTitle="Find the perfect tools to grow your business"
            title="Discover Top SaaS Products"
            description=""
          />
          <div className="flex justify-center items-center min-h-[300px]">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </div>
      </SectionContainer>
    );
  }

  if (error) {
    return (
      <SectionContainer>
        <div className="pt-16"> {/* Add padding top to avoid navbar overlap */}
          <SectionHeader
            subTitle="Find the perfect tools to grow your business"
            title="Discover Top SaaS Products"
            description=""
          />
          <AlertDialog open={true}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Error</AlertDialogTitle>
                <AlertDialogDescription>{error}</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogAction onClick={() => {}}>Close</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </SectionContainer>
    );
  }

  return (
    <>
      <SectionContainer>
        <div className="pt-16"> {/* Add padding top to avoid navbar overlap */}
          <div className="text-center mb-4">
            <p className="text-gray-600 mb-2">Find the perfect tools to grow your business</p>
            <h1 className="text-4xl font-bold">Discover Top SaaS Products</h1>
          </div>
          
          {/* Search bar */}
          <div className="relative w-full max-w-3xl mx-auto my-8">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-6 pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search for products, categories, or features..."
                className="w-full p-4 pl-14 pr-4 rounded-full border border-red-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          {/* Category tabs - dynamically built from available categories */}
          <div className="flex flex-wrap gap-2 my-6 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => {
                  setSelectedCategory(category);
                  setSearchQuery(""); // Clear search when changing category
                }}
                className={`py-2 px-6 rounded-full transition-colors ${
                  selectedCategory === category
                    ? "bg-red-500 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {category === "All" ? "All" : formatCategoryName(category)}
              </button>
            ))}
          </div>
          
          {/* Product listings */}
          {Object.keys(groupedProducts).length > 0 ? (
            Object.entries(groupedProducts).map(([category, categoryProducts]) => (
              <div key={category} className="mb-12">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold">{formatCategoryName(category)}</h2>
                  <InteractiveHoverButton
                    text="View all"
                    className="px-4 py-2 text-sm"
                    onClick={() => {
                      // Filter to show only this category
                      setSelectedCategory(category);
                      setSearchQuery("");
                    }}
                  />
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 justify-items-center">
                  {categoryProducts.map((product) => (
                    <ShiftCardDemo
                      key={product.ProductCard.id}
                      product={product}
                      onViewProduct={() => {
                        try {
                          // Create slug from product name
                          const productSlug = product.ProductCard.productName
                            .toLowerCase()
                            .replace(/[^\w\s-]/g, '') // Remove special characters
                            .replace(/\s+/g, '-'); // Replace spaces with hyphens

                          window.location.href = `/products/${productSlug}`;
                        } catch (error) {
                          console.error("Navigation error:", error);
                        }
                      }}
                      onBuyNow={() => {
                        try {
                          // Create slug from product name
                          const productSlug = product.ProductCard.productName
                            .toLowerCase()
                            .replace(/[^\w\s-]/g, '') // Remove special characters
                            .replace(/\s+/g, '-'); // Replace spaces with hyphens

                          window.location.href = `/products/${productSlug}#pricing-section`;
                        } catch (error) {
                          console.error("Navigation error:", error);
                        }
                      }}
                    />
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
                  <Search className="h-8 w-8 text-red-500" />
                </div>
                <h3 className="text-2xl font-bold mb-2">No products found</h3>
                <p className="text-gray-600 max-w-md mb-6">
                  We couldn&apos;t find any products matching your criteria. Please try a different search term or browse our categories.
                </p>
                <button 
                  onClick={handleContactClick}
                  className="inline-flex items-center px-5 py-3 rounded-md bg-red-500 text-white hover:bg-red-600 transition-colors"
                >
                  <Mail className="h-5 w-5 mr-2" />
                  Contact us for assistance
                </button>
              </div>
              
              <div className="w-full max-w-2xl">
                <h4 className="font-bold text-lg mb-3">Popular categories</h4>
                <div className="flex flex-wrap gap-2 justify-center">
                  {availableCategories.map((category) => (
                    <button
                      key={category}
                      onClick={() => {
                        setSelectedCategory(category);
                        setSearchQuery("");
                      }}
                      className="py-2 px-4 bg-gray-100 rounded-full text-gray-700 hover:bg-gray-200 transition-colors"
                    >
                      {formatCategoryName(category)}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </SectionContainer>
      
      {/* Contact Section */}
      <div ref={contactSectionRef}>
        <ContactSection />
      </div>
      <FooterSection />
    </>
  );
} 