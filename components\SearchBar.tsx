"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { Search, Loader2 } from "lucide-react";
import { useDebounce } from "use-debounce";
import { useProductSearch } from "@/hooks/useProductSearch";
import Logger from "@/utils/logger";
import { SearchHit } from "@/types/search";
import { Button } from "@/components/ui/button";
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

export function SearchBar() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  // Use a shorter debounce time for more responsive search
  const [debouncedValue] = useDebounce(inputValue, 300);
  const { search, data, isLoading, error, latency } = useProductSearch();
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle keyboard shortcut to open search
  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setOpen((open) => !open);
      }
    };

    // Handle custom event from mobile menu
    const handleOpenSearch = () => {
      setOpen(true);
    };

    document.addEventListener("keydown", down);
    document.addEventListener("open-global-search", handleOpenSearch);

    return () => {
      document.removeEventListener("keydown", down);
      document.removeEventListener("open-global-search", handleOpenSearch);
    };
  }, []);

  // Handle dialog open/close
  useEffect(() => {
    if (!open) {
      // Clear input when dialog closes, but don't clear results yet
      // This prevents the flash of "no results" when reopening
      setInputValue("");
    } else {
      // When dialog opens, focus the input
      if (inputRef.current) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 50);
      }
    }
  }, [open]);

  // Trigger search when debounced value changes
  useEffect(() => {
    console.log(`Debounced value changed to: "${debouncedValue}", dialog open: ${open}`);

    // Always call search - it will handle empty queries internally
    if (open) { // Only search when the dialog is open
      console.log(`Calling search with: "${debouncedValue}"`);
      search(debouncedValue);
    } else {
      console.log('Dialog closed, not searching');
    }
  }, [debouncedValue, search, open]);

  // Force an immediate search when the dialog opens
  useEffect(() => {
    if (open && inputValue.trim().length > 0) {
      console.log(`Dialog opened with existing input: "${inputValue}", forcing immediate search`);
      search(inputValue);
    }
  }, [open, inputValue, search]);

  // Handle input change
  const handleInputChange = (value: string) => {
    console.log(`Input changed to: "${value}"`);
    setInputValue(value);

    // For very short queries (1-2 chars), trigger search immediately
    // This helps with the first-time search issue
    if (value.trim().length > 0 && value.trim().length <= 2) {
      console.log(`Short query detected (${value.length} chars), triggering immediate search`);
      search(value);
    }
    // Don't clear results immediately when typing - let the debounce handle it
  };

  // Store previous search results to prevent flashing
  const prevResultsRef = useRef<SearchHit[] | null>(null);

  // Update the previous results ref when data changes
  useEffect(() => {
    console.log('Data changed:', data ? `${data.length} results` : 'null');
    if (data && data.length > 0) {
      console.log('Updating previous results ref with new data');
      prevResultsRef.current = data;
    }
  }, [data]);

  // Handle navigation to search result
  const handleSelect = (hit: SearchHit) => {
    Logger.info(`Selected search result: ${hit.productName} - ${hit.sectionTitle || "Product"}`, {
      context: "SearchBar",
    });

    setOpen(false);
    setInputValue("");

    // Construct the URL with the anchor if available
    const url = hit.sectionAnchor
      ? `/products/${hit.slug}#${hit.sectionAnchor}`
      : `/products/${hit.slug}`;

    console.log(`Navigating to: ${url}`);

    router.push(url);
  };

  // We're now calculating grouped results inline in the render function

  // Highlight matching text in search results
  const highlightMatch = (text: string, query: string) => {
    if (!query.trim() || !text) return text;

    try {
      const regex = new RegExp(`(${query.trim()})`, 'gi');
      const parts = text.split(regex);

      return parts.map((part, i) =>
        regex.test(part) ? <span key={i} className="bg-yellow-200 dark:bg-yellow-800">{part}</span> : part
      );
    } catch {
      return text;
    }
  };

  return (
    <>
      <Button
        variant="outline"
        className="relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2"
        onClick={() => setOpen(true)}
      >
        <Search className="h-4 w-4 xl:mr-2" aria-hidden="true" />
        <span className="hidden xl:inline-flex">Search products...</span>
        <span className="sr-only">Search products</span>
        <kbd className="pointer-events-none absolute right-1.5 top-2 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-xs font-medium opacity-100 xl:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      <CommandDialog
        open={open}
        onOpenChange={(isOpen) => {
          // When closing, don't immediately clear the results
          // This prevents the flash when reopening
          setOpen(isOpen);
        }}
      >
        <CommandInput
          ref={inputRef}
          placeholder="Search products and sections..."
          value={inputValue}
          onValueChange={handleInputChange}
        />
        <CommandList>
          {/* Loading indicator is now handled in the search results section */}

          {error && (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <p className="text-sm font-medium text-destructive">Search failed</p>
              <p className="text-xs text-muted-foreground">{error.message}</p>
            </div>
          )}

          <CommandEmpty>
            {!isLoading && inputValue.trim().length === 0 && (
              <div className="flex flex-col items-center py-6 text-center">
                <p className="text-sm">Start typing to search</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Search for products, features, pricing, and more
                </p>
              </div>
            )}
          </CommandEmpty>

          {/* Search Results */}
          <div className="search-results-container">
            {/* Search results rendering */}

            {/* Show results when we have data */}
            {(() => {
              // Use data if available, otherwise use previous results to prevent flashing
              const resultsToShow = data || prevResultsRef.current;

              console.log('Rendering search results, data:', data ? `${data.length} results` : 'null');
              console.log('Previous results:', prevResultsRef.current ? `${prevResultsRef.current.length} results` : 'null');
              console.log('Results to show:', resultsToShow ? `${resultsToShow.length} results` : 'null');

              if (resultsToShow && resultsToShow.length > 0) {
                console.log('Showing results:', resultsToShow.length);

                // Calculate grouped results for the data we're showing
                const groupsToShow = resultsToShow.reduce<Record<string, SearchHit[]>>((acc, hit) => {
                  if (!hit.productName) {
                    console.warn('Hit missing productName:', hit);
                    return acc;
                  }
                  if (!acc[hit.productName]) acc[hit.productName] = [];
                  acc[hit.productName].push(hit);
                  return acc;
                }, {});

                console.log('Grouped results:', Object.keys(groupsToShow));

                return (
                  <div className="search-results">
                    {Object.entries(groupsToShow).map(([productName, hits]) => {
                      console.log(`Rendering group for ${productName} with ${hits.length} hits`);
                      return (
                        <CommandGroup key={productName} heading={productName}>
                          {hits.map((hit, index) => (
                            <CommandItem
                              key={`${hit.slug}-${hit.sectionAnchor || index}`}
                              onSelect={() => handleSelect(hit)}
                              className="flex items-center"
                            >
                              <div className="flex flex-col">
                                <div className="font-medium">
                                  {hit.sectionTitle
                                    ? highlightMatch(hit.sectionTitle, inputValue)
                                    : <span className="text-muted-foreground">Product Overview</span>}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {hit.slug}
                                  {hit.sectionAnchor && <span className="ml-1 opacity-60">#{hit.sectionAnchor}</span>}
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      );
                    })}
                  </div>
                );
              }

              // Show loading state
              if (isLoading && debouncedValue.trim().length > 0) {
                return (
                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    <span className="ml-2 text-sm text-muted-foreground">Searching...</span>
                  </div>
                );
              }

              // Show "no results" message when appropriate
              if (debouncedValue.trim().length > 0 && !isLoading && data && data.length === 0) {
                console.log('Rendering no results message for query:', debouncedValue);
                return (
                  <div className="flex flex-col items-center py-6 text-center">
                    <p className="text-sm">No results found for &quot;{debouncedValue}&quot;</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Try searching for a different term or browse our products
                    </p>
                  </div>
                );
              }

              return null;
            })()}
          </div>

          {latency && (
            <div className="px-2 py-1.5 text-xs text-muted-foreground">
              {data && data.length > 0 ? (
                <>Found {data.length} results in {latency.toFixed(0)}ms</>
              ) : (
                <>Search completed in {latency.toFixed(0)}ms</>
              )}
            </div>
          )}
        </CommandList>
      </CommandDialog>
    </>
  );
}
