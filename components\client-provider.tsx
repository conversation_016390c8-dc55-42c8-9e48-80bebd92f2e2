"use client";

import React from "react";
import { MiniCart } from "./cart/mini-cart";
import { PaymentSuccessListener } from "./payment/payment-success-listener";
import { SubscriptionRecovery } from "./payment/subscription-recovery";

interface ClientProviderProps {
  children: React.ReactNode;
}

export function ClientProvider({
  children
}: ClientProviderProps) {
  return (
    <>
      {/* Add payment success listener that will clear cart after successful payments */}
      <PaymentSuccessListener />
      {/* Add subscription recovery to automatically recover failed subscription attempts */}
      <SubscriptionRecovery />
      {children}
      <MiniCart />
    </>
  );
}

export default ClientProvider;
