"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { toast } from "sonner";

export interface AuthOptions {
  message?: string;
  title?: string;
  description?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  returnUrl?: string;
  showSignUp?: boolean;
  defaultTab?: 'login' | 'signup';
  storeData?: Record<string, string>;
}

/**
 * Enhanced authentication hook with modal-based UX
 * Provides consistent authentication patterns across the app
 */
export function useAuth() {
  const { data: session, status } = useSession();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authOptions, setAuthOptions] = useState<AuthOptions>({});

  /**
   * Check if user is authenticated and show modal if not
   * Returns true if authenticated, false if modal was shown
   */
  const requireAuth = (options: AuthOptions = {}): boolean => {
    // If still loading, show loading message
    if (status === "loading") {
      toast.info("Please wait while we check your authentication...", {
        duration: 2000
      });
      return false;
    }

    // If authenticated, call success callback and return true
    if (status === "authenticated" && session) {
      options.onSuccess?.();
      return true;
    }

    // User is not authenticated - store any additional data
    if (options.storeData) {
      Object.entries(options.storeData).forEach(([key, value]) => {
        try {
          localStorage.setItem(key, value);
        } catch (error) {
          console.warn(`Failed to store ${key} in localStorage:`, error);
        }
      });
    }

    // Store return URL
    const returnUrl = options.returnUrl || window.location.href;
    try {
      localStorage.setItem("auth_return_url", returnUrl);
    } catch (error) {
      console.warn("Failed to store return URL:", error);
    }

    // Set auth options and show modal
    setAuthOptions({
      message: options.message || "Please sign in to continue",
      title: options.title || "Authentication Required",
      description: options.description || "Sign in to access this feature and enjoy a personalized experience.",
      onSuccess: options.onSuccess,
      onCancel: options.onCancel,
      returnUrl,
      showSignUp: options.showSignUp !== false, // Default to true
      defaultTab: options.defaultTab || 'login'
    });
    
    setShowAuthModal(true);
    return false;
  };

  /**
   * Close the auth modal
   */
  const closeAuthModal = () => {
    setShowAuthModal(false);
    authOptions.onCancel?.();
  };

  /**
   * Check if user is authenticated (without showing modal)
   */
  const isAuthenticated = status === "authenticated" && !!session;

  /**
   * Check if authentication is still loading
   */
  const isLoading = status === "loading";

  /**
   * Get current user data
   */
  const user = session?.user || null;

  return {
    // State
    isAuthenticated,
    isLoading,
    user,
    session,
    status,
    
    // Modal state
    showAuthModal,
    authOptions,
    
    // Actions
    requireAuth,
    closeAuthModal,
  };
}

/**
 * Auth messages for different scenarios
 */
export const AUTH_MESSAGES = {
  CART: "Please sign in to add items to your cart and proceed with checkout",
  TRIAL: "Please sign in to start your free trial and explore our features",
  SUBSCRIPTION: "Please sign in to subscribe and access premium features",
  PRICING: "Please sign in to view pricing and purchase plans",
  GENERAL: "Please sign in to continue",
  CHECKOUT: "Please sign in to proceed with checkout",
  DASHBOARD: "Please sign in to access your dashboard",
  PROFILE: "Please sign in to view and edit your profile",
} as const;

/**
 * Quick helper functions for common auth scenarios
 */
export const authHelpers = {
  requireAuthForCart: (onSuccess?: () => void, onCancel?: () => void) => ({
    message: AUTH_MESSAGES.CART,
    title: "Sign in to Continue Shopping",
    description: "Add items to your cart and enjoy a seamless checkout experience.",
    onSuccess,
    onCancel,
  }),

  requireAuthForTrial: (productName?: string, onSuccess?: () => void, onCancel?: () => void) => ({
    message: productName ? `Please sign in to start your ${productName} trial` : AUTH_MESSAGES.TRIAL,
    title: "Start Your Free Trial",
    description: "Sign in to begin your trial and explore all the features we have to offer.",
    onSuccess,
    onCancel,
  }),

  requireAuthForSubscription: (planName?: string, onSuccess?: () => void, onCancel?: () => void) => ({
    message: planName ? `Please sign in to subscribe to ${planName}` : AUTH_MESSAGES.SUBSCRIPTION,
    title: "Subscribe to Continue",
    description: "Sign in to subscribe and unlock premium features and benefits.",
    onSuccess,
    onCancel,
  }),

  requireAuthForCheckout: (onSuccess?: () => void, onCancel?: () => void) => ({
    message: AUTH_MESSAGES.CHECKOUT,
    title: "Complete Your Purchase",
    description: "Sign in to securely complete your purchase and access your products.",
    onSuccess,
    onCancel,
  }),
};
