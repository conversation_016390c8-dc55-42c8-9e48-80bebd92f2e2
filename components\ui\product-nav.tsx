"use client";

import React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { MenuItem, ProductItem } from "./navbar-menu";
import type { ProductResponse } from "../../@types/product";

// Section Navigation Item component for product pages
export const SectionNavItem = ({
  label,
  sectionId,
  onSectionClick,
}: {
  label: string;
  sectionId: string;
  onSectionClick: (sectionId: string) => void;
}) => {
  return (
    <div
      className="cursor-pointer px-3 py-1 mx-1 hover:text-red-500 transition-colors"
      onClick={() => onSectionClick(sectionId)}
    >
      {label}
    </div>
  );
};

// Product Navigation Menu wrapper
export const ProductNavMenu = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex items-center space-x-1 bg-transparent", className)}>
      {children}
    </div>
  );
};

// Regular Link Item for the product page (non-section links)
export const ProductNavLink = ({
  href,
  label,
}: {
  href: string;
  label: string;
}) => {
  return (
    <Link
      href={href}
      className="px-3 py-1 mx-1 hover:text-red-500 transition-colors"
    >
      {label}
    </Link>
  );
};

// Product Page Navigation component
export const ProductNav = ({
  activeItem,
  setActiveItem,
  products,
  loading,
  productSections,
  routeList,
  scrollToSection,
}: {
  activeItem: string | null;
  setActiveItem: (item: string | null) => void;
  products: ProductResponse[];
  loading: boolean;
  productSections: {id: string; label: string}[];
  routeList: {href: string; label: string}[];
  scrollToSection: (sectionId: string) => void;
}) => {
  // Generate navigation items for product pages
  const getNavItems = () => {
    const navItems: React.ReactNode[] = [];
    
    // Always include Products as the first item with its dropdown menu
    navItems.push(
      <div className="relative group" key="products-menu-item">
        <MenuItem 
          key="products"
          setActive={setActiveItem} 
          active={activeItem} 
          item="Products"
        >
          <div className="flex flex-col space-y-1 py-1 group-hover:block hidden">
            {loading ? (
              <div className="py-2 px-3 text-center text-sm">Loading products...</div>
            ) : (
              products.slice(0, 5).map((product) => (
                <ProductItem
                  key={product.slug}
                  title={product.ProductCard.productName}
                  description={product.ProductCard.productDescription || ""}
                  href={`/products/${product.slug}`}
                  src={product.ProductCard.productLogo?.url || "/placeholder.jpg"}
                />
              ))
            )}
          </div>
        </MenuItem>
      </div>
    );
    
    // Filter out duplicates between product sections and standard routes
    const standardRouteLabels = routeList.map(route => route.label.toLowerCase());
    
    // For product sections, use section navigation components
    productSections
      .filter(section => !standardRouteLabels.includes(section.label.toLowerCase()))
      .forEach(section => {
        navItems.push(
          <SectionNavItem
            key={section.id}
            label={section.label}
            sectionId={section.id}
            onSectionClick={scrollToSection}
          />
        );
      });
    
    // Add standard route items that don't overlap with product sections
    routeList
      .filter(route => !productSections.some(section => 
        section.label.toLowerCase() === route.label.toLowerCase()))
      .forEach(route => {
        navItems.push(
          <ProductNavLink
            key={route.href}
            href={route.href}
            label={route.label}
          />
        );
      });
      
    return navItems;
  };

  return (
    <ProductNavMenu>
      {getNavItems()}
    </ProductNavMenu>
  );
};

// Generate mobile menu items for product pages
export const getProductMobileMenuItems = (
  productSections: {id: string; label: string}[],
  routeList: {href: string; label: string}[],
  scrollToSection: (sectionId: string) => void,
  closeMenu: () => void
) => {
  const items: React.ReactNode[] = [];
  
  // Filter out duplicates between product sections and standard routes
  const standardRouteLabels = routeList.map(route => route.label.toLowerCase());
  
  // Add product-specific section buttons first
  productSections
    .filter(section => !standardRouteLabels.includes(section.label.toLowerCase()))
    .forEach(section => {
      items.push(
        <button
          key={section.id}
          onClick={() => {
            closeMenu();
            setTimeout(() => scrollToSection(section.id), 300);
          }}
          className="w-full text-left px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-md transition-colors text-base"
        >
          {section.label}
        </button>
      );
    });
  
  // Add standard route buttons that don't overlap with product sections
  routeList
    .filter(route => !productSections.some(section => 
      section.label.toLowerCase() === route.label.toLowerCase()))
    .forEach(route => {
      items.push(
        <Link
          key={route.href}
          href={route.href}
          onClick={() => closeMenu()}
          className="block w-full text-left px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-md transition-colors text-base"
        >
          {route.label}
        </Link>
      );
    });
  
  return items;
}; 