"use client";

import { useState } from "react";
import { <PERSON>ton, ButtonProps } from "@/components/ui/button";
import { validateSubscriptionCancellation } from "@/src/services/subscriptionValidationService";
import { PlanChangeBlockedDialog } from "./plan-change-blocked-dialog";
import { useUser } from "@/context/user-context";
import { toast } from "sonner";

interface SubscriptionCancelButtonProps extends ButtonProps {
  subscriptionId: string;
  productSlug: string;
  planCode: string;
}

export function SubscriptionCancelButton({
  productSlug,
  planCode,
  children,
  ...props
}: SubscriptionCancelButtonProps) {
  const { userId } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [showBlockedDialog, setShowBlockedDialog] = useState(false);
  const [blockedMessage, setBlockedMessage] = useState<string>("");

  const handleCancelClick = async () => {
    if (!userId) {
      toast.error("You must be logged in to cancel a subscription");
      return;
    }

    setIsLoading(true);

    try {
      // Validate if cancellation is allowed
      const validationResult = await validateSubscriptionCancellation({
        userId,
        productSlug,
        planCode
      });

      // Cancellation is not allowed according to business rules
      if (!validationResult.isValid) {
        setBlockedMessage(validationResult.message);
        setShowBlockedDialog(true);
        return;
      }

      // If we get here, cancellation would be allowed (but we don't implement it)
      // This should never happen with current business rules, but we'll handle it anyway
      setBlockedMessage("Subscription cancellation is currently not supported.");
      setShowBlockedDialog(true);
    } catch (error) {
      console.error("Error validating subscription cancellation:", error);
      toast.error("An error occurred while processing your request");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        variant="destructive"
        onClick={handleCancelClick}
        disabled={isLoading}
        {...props}
      >
        {isLoading ? "Please wait..." : children || "Cancel Subscription"}
      </Button>

      <PlanChangeBlockedDialog
        open={showBlockedDialog}
        onClose={() => setShowBlockedDialog(false)}
        message={blockedMessage}
        actionType="cancel"
      />
    </>
  );
}
