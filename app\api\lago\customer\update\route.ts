import { NextRequest, NextResponse } from "next/server";
import { safeFetch } from "@/lib/fetch-utils";
import { LAGO_API_URL, LAGO_API_KEY, corsHeaders } from "@/src/constants/api";

// Validate that LAGO_API_URL is defined
if (!LAGO_API_URL) {
  console.error("❌ API - LAGO_API_URL is not defined. Please check your environment variables.");
}

const CUSTOMER_URL = `${LAGO_API_URL}/customers`;

/**
 * Helper function to sanitize Keycloak ID
 */
function sanitizeKeycloakId(id: string): string | null {
  if (!id) return null;

  // Standard UUID format check
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(id)) return id;

  // Remove customer- prefix if present
  if (id.startsWith('customer-')) {
    const withoutPrefix = id.substring(9);
    if (uuidRegex.test(withoutPrefix)) return withoutPrefix;
  }

  return id; // Return original if no sanitization was possible
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// POST route to update a customer in Lago
export async function POST(request: NextRequest) {
  console.log(`📝 TAX FLOW API - Starting customer update API route`);

  if (!LAGO_API_URL) {
    console.error('❌ TAX FLOW API - LAGO_API_URL is undefined in POST /api/lago/customer/update');
    return NextResponse.json({ error: 'Missing API configuration' }, {
      status: 500,
      headers: corsHeaders
    });
  }

  try {
    const body = await request.json();
    console.log(`📝 TAX FLOW API - Received update request:`, body);

    if (!body || !body.customer || !body.customer.external_id) {
      console.error('❌ TAX FLOW API - Missing required fields in request body');
      return NextResponse.json(
        { error: 'Missing required fields (customer data with external_id)' },
        { status: 400, headers: corsHeaders }
      );
    }

    // Sanitize the Keycloak ID
    const externalId = body.customer.external_id;
    const sanitizedId = sanitizeKeycloakId(externalId);

    if (sanitizedId) {
      body.customer.external_id = sanitizedId;
      console.log(`✅ TAX FLOW API - Using sanitized Keycloak ID: ${sanitizedId}`);
    } else {
      console.log(`📝 TAX FLOW API - Using original ID (not UUID format): ${externalId}`);
    }

    // Process GST information if provided
    if (body.customer.tax_identification_number) {
      console.log(`📝 TAX FLOW API - Processing Tax Identification Number: ${body.customer.tax_identification_number}`);

      // Normalize GST number (uppercase and trim)
      const originalGst = body.customer.tax_identification_number;
      body.customer.tax_identification_number = body.customer.tax_identification_number.trim().toUpperCase();

      if (originalGst !== body.customer.tax_identification_number) {
        console.log(`📝 TAX FLOW API - Normalized Tax Identification Number: ${originalGst} -> ${body.customer.tax_identification_number}`);
      }

      // Validate GST number format
      const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
      if (!gstRegex.test(body.customer.tax_identification_number)) {
        console.error(`❌ TAX FLOW API - Invalid Tax Identification Number format: ${body.customer.tax_identification_number}`);
        return NextResponse.json(
          { error: 'Invalid Tax Identification Number format' },
          { status: 400, headers: corsHeaders }
        );
      }
      console.log(`✅ TAX FLOW API - Tax Identification Number format is valid`);

      // Ensure business name is provided if GST number is valid
      if (!body.customer.legal_name) {
        console.error(`❌ TAX FLOW API - Legal name is required when Tax Identification Number is provided`);
        return NextResponse.json(
          { error: 'Legal name is required when Tax Identification Number is provided' },
          { status: 400, headers: corsHeaders }
        );
      } else {
        // Trim business name
        const originalName = body.customer.legal_name;
        body.customer.legal_name = body.customer.legal_name.trim();

        if (originalName !== body.customer.legal_name) {
          console.log(`📝 TAX FLOW API - Trimmed legal name: '${originalName}' -> '${body.customer.legal_name}'`);
        }
      }

      console.log(`✅ TAX FLOW API - Valid Tax Identification details: ${body.customer.tax_identification_number}, ${body.customer.legal_name}`);
    } else if (body.customer.legal_name) {
      // If business name is provided but no GST, trim it
      const originalName = body.customer.legal_name;
      body.customer.legal_name = body.customer.legal_name.trim();

      if (originalName !== body.customer.legal_name) {
        console.log(`📝 TAX FLOW API - Trimmed legal name (no Tax ID): '${originalName}' -> '${body.customer.legal_name}'`);
      }
    }

    console.log(`🔄 TAX FLOW API - Updating customer with ID: ${body.customer.external_id}`);
    console.log(`📝 TAX FLOW API - Final payload to Lago:`, body);

    // Forward to Lago API
    const lagoUrl = `${CUSTOMER_URL}`;
    console.log(`📝 TAX FLOW API - Sending request to Lago URL: ${lagoUrl}`);

    const response = await safeFetch(lagoUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      },
      body: JSON.stringify(body)
    });

    console.log(`🔄 TAX FLOW API - Customer update response status: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ TAX FLOW API - Customer updated successfully:`, data);
      return NextResponse.json(data, { headers: corsHeaders });
    } else {
      const errorText = await response.text();
      console.error(`❌ TAX FLOW API - Error updating customer: ${response.status}`, errorText);

      try {
        // Try to parse the error as JSON for more details
        const errorJson = JSON.parse(errorText);
        console.error(`❌ TAX FLOW API - Error details:`, errorJson);
      } catch {
        // If not JSON, just log the raw text
        console.error(`❌ TAX FLOW API - Raw error:`, errorText);
      }

      // Check for specific error cases
      if (response.status === 404) {
        return NextResponse.json(
          { error: 'Customer not found' },
          { status: 404, headers: corsHeaders }
        );
      }

      return NextResponse.json(
        { error: `Failed to update customer: ${errorText}` },
        { status: response.status, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('❌ API - Exception in PUT /api/lago/customer/update:', error);
    return NextResponse.json({ error: 'Server error' }, {
      status: 500,
      headers: corsHeaders
    });
  }
}
