"use client"

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'

interface CinematicTransitionOverlayProps {
  isActive: boolean
  cardData?: {
    image: string
    title: string
    position: DOMRect
  }
  onComplete?: () => void
}

export const CinematicTransitionOverlay = ({
  isActive,
  cardData,
  onComplete
}: CinematicTransitionOverlayProps) => {
  const overlayRef = useRef<HTMLDivElement>(null)
  const cardCloneRef = useRef<HTMLDivElement>(null)
  const pagePreviewRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!isActive || !cardData || !overlayRef.current) return

    const overlay = overlayRef.current
    const cardClone = cardCloneRef.current
    const pagePreview = pagePreviewRef.current

    if (!cardClone || !pagePreview) return

    // Set initial positions
    gsap.set(overlay, {
      opacity: 1,
      pointerEvents: 'none'
    })

    gsap.set(card<PERSON>lone, {
      x: cardData.position.left,
      y: cardData.position.top,
      width: cardData.position.width,
      height: cardData.position.height,
      scale: 1,
      opacity: 1
    })

    gsap.set(pagePreview, {
      scale: 0.1,
      opacity: 0,
      x: '50%',
      y: '50%'
    })

    // Create animation timeline
    const tl = gsap.timeline({
      onComplete: () => {
        onComplete?.()
      }
    })

    // Phase 1: Zoom card
    tl.to(cardClone, {
      scale: 3.5,
      x: window.innerWidth / 2 - cardData.position.width / 2,
      y: window.innerHeight / 2 - cardData.position.height / 2,
      duration: 0.8,
      ease: "power2.inOut"
    })

    // Phase 2: Show page preview
    tl.to(pagePreview, {
      opacity: 1,
      scale: 0.3,
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.4")

    // Phase 3: Scale page to full size
    tl.to(pagePreview, {
      scale: 1,
      duration: 0.6,
      ease: "power2.inOut"
    })

    // Phase 4: Fade out card
    tl.to(cardClone, {
      opacity: 0,
      scale: 4,
      duration: 0.4,
      ease: "power2.in"
    }, "-=0.3")

    // Phase 5: Complete transition
    tl.to(overlay, {
      opacity: 0,
      duration: 0.3,
      ease: "power2.inOut"
    })

    return () => {
      tl.kill()
    }
  }, [isActive, cardData, onComplete])

  if (!isActive) return null

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 z-[10000] pointer-events-none"
      style={{
        background: 'rgba(0, 0, 0, 0.8)'
      }}
    >
      {/* Card Clone */}
      {cardData && (
        <div
          ref={cardCloneRef}
          className="absolute"
          style={{
            backgroundImage: `url(${cardData.image})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            borderRadius: '12px',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)'
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-xl" />
          <div className="absolute bottom-4 left-4 text-white">
            <h3 className="text-lg font-semibold">{cardData.title}</h3>
          </div>
        </div>
      )}

      {/* Page Preview */}
      <div
        ref={pagePreviewRef}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl overflow-hidden shadow-2xl"
        style={{
          width: '100vw',
          height: '100vh'
        }}
      >
        <div className="w-full h-full bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto mb-4 animate-pulse" />
            <p className="text-gray-600">Loading product page...</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Hook for managing cinematic transitions
export const useCinematicTransition = () => {
  const createTransition = (
    cardElement: Element,
    productData: {
      image: string
      title: string
    },
    onComplete: () => void
  ) => {
    const cardRect = cardElement.getBoundingClientRect()
    
    return {
      cardData: {
        image: productData.image,
        title: productData.title,
        position: cardRect
      },
      onComplete
    }
  }

  return { createTransition }
}

export default CinematicTransitionOverlay
