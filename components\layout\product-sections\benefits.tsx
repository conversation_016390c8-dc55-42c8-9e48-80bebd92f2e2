import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Icon } from "@/components/ui/extras/icon";
import { cn } from "@/lib/utils";
import { icons } from "lucide-react";
import SectionContainer from "../section-container";
import SectionHeader from "../section-header";
import { forwardRef } from "react";

type BenefitsSectionProps = {
  title: string;
  description: string;
  cards: { description: string; icon: string; text: string; id: string }[];
  sectionComponent?: string;
  sectionId?: number;
};

export const BenefitsSection = forwardRef<HTMLDivElement, BenefitsSectionProps>(
  ({ title, description, cards, sectionComponent, sectionId }, ref) => {
    return (
      <SectionContainer
        id="benefits"
        ref={ref}
        sectionComponent={sectionComponent}
        sectionId={sectionId}
      >
        <div className="grid lg:grid-cols-2 lg:gap-24">
          <div>
            <SectionHeader
              className="sticky lg:top-[22rem] text-center lg:text-start max-w-full"
              subTitle="Benefits"
              title={title}
              description={description}
            />
          </div>

          <div className="flex flex-col gap-6 lg:gap-[14rem] w-full ">
            {cards?.map(({ icon, text, description }, index) => (
              <Card
                key={text}
                className={cn(
                  "group/number lg:sticky shadow-none border bg-muted"
                )}
                style={{ top: `${20 + index + 2}rem` }}
              >
                <CardHeader>
                  <div className="flex justify-between">
                    <Icon
                      name={icon as keyof typeof icons}
                      size={40}
                      color="hsl(var(--primary))"
                      className="mb-6 text-primary bg-primary/20 p-2 rounded-full ring-8 ring-primary/10"
                    />
                    <span className="text-5xl text-muted-foreground/15 font-medium transition-all delay-75 group-hover/number:text-muted-foreground/30">
                      0{index + 1}
                    </span>
                  </div>
                  <CardTitle>{text}</CardTitle>
                </CardHeader>
                <CardContent className="text-muted-foreground">
                  {description}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </SectionContainer>
    );
  }
);

BenefitsSection.displayName = "BenefitsSection";
