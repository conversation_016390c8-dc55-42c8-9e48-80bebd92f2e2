"use client";

import React from "react";
import { Menu, MenuItem, ProductItem, HoveredLink } from "./navbar-menu";
import type { ProductResponse } from "../../@types/product";
import Link from "next/link";

// Main Page Navigation component
export const MainNav = ({
  activeItem,
  setActiveItem,
  products,
  loading,
  routeList,
}: {
  activeItem: string | null;
  setActiveItem: (item: string | null) => void;
  products: ProductResponse[];
  loading: boolean;
  routeList: {href: string; label: string}[];
}) => {
  // Generate navigation items for the main page
  const getNavItems = () => {
    const navItems: React.ReactNode[] = [];
    
    // Always include Products as the first item with its dropdown menu
    navItems.push(
      <div className="relative group" key="products-menu-item">
        <MenuItem 
          key="products"
          setActive={setActiveItem} 
          active={activeItem} 
          item="Products"
        >
          <div className="flex flex-col space-y-1 py-1 group-hover:block hidden">
            {loading ? (
              <div className="py-2 px-3 text-center text-sm">Loading products...</div>
            ) : (
              products.slice(0, 5).map((product) => (
                <ProductItem
                  key={product.slug}
                  title={product.ProductCard.productName}
                  description={product.ProductCard.productDescription || ""}
                  href={`/products/${product.slug}`}
                  src={product.ProductCard.productLogo?.url || "/placeholder.jpg"}
                />
              ))
            )}
          </div>
        </MenuItem>
      </div>
    );
    
    // For non-product pages, use the standard route items with dropdowns
    routeList.forEach(route => {
      navItems.push(
        <MenuItem 
          key={route.href} 
          setActive={setActiveItem} 
          active={activeItem} 
          item={route.label}
        >
          <div className="flex flex-col space-y-1 py-1">
            <HoveredLink href={route.href}>{route.label}</HoveredLink>
          </div>
        </MenuItem>
      );
    });
    
    return navItems;
  };

  return (
    <Menu 
      setActive={setActiveItem} 
      className="py-0 px-4 bg-transparent border-none shadow-none flex items-center h-10"
    >
      {getNavItems()}
    </Menu>
  );
};

// Generate mobile menu items for main pages
export const getMainMobileMenuItems = (
  routeList: {href: string; label: string}[],
  closeMenu: () => void
) => {
  return routeList.map(({ href, label }) => (
    <Link
      key={href}
      href={href}
      onClick={() => closeMenu()}
      className="block w-full text-left px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-md transition-colors text-base"
    >
      {label}
    </Link>
  ));
}; 