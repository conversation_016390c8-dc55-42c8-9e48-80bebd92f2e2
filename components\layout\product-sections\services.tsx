import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";
import { forwardRef } from "react";

type ServicesSectionProps = {
  title: string;
  description: string;
  servicesCard: { tag: string | null; title: string; decription: string; id: string }[] | undefined;
  sectionComponent?: string;
  sectionId?: number;
};

export const ServicesSection = forwardRef<HTMLDivElement, ServicesSectionProps>(
  ({ title, description, servicesCard, sectionComponent, sectionId }, ref) => {
    if (!servicesCard || servicesCard.length === 0) {
      return null;
    }

    return (
      <SectionContainer
        id="services"
        ref={ref}
        sectionComponent={sectionComponent}
        sectionId={sectionId}
      >
        <SectionHeader
          subTitle="Services"
          title={title}
          description={description}
        />
        <div className="grid sm:grid-cols-2 lg:grid-cols-2 gap-6 w-full max-w-screen-lg mx-auto">
          {servicesCard.map(({ title, decription, tag, id }) => (
            <Card key={id} className="bg-muted h-full relative">
              <CardHeader>
                <CardTitle>{title}</CardTitle>
                <CardDescription>{decription}</CardDescription>
              </CardHeader>
              {tag && (
                <Badge
                  data-pro={tag === "pro"}
                  variant="secondary"
                  className="absolute -top-2 -right-3"
                >
                  {tag}
                </Badge>
              )}
            </Card>
          ))}
        </div>
      </SectionContainer>
    );
  }
);

ServicesSection.displayName = "ServicesSection";
