"use client"

import { useEffect, useState } from 'react'

interface PagePreloaderProps {
  urls: string[]
  onComplete?: (loadedUrls: string[]) => void
  timeout?: number
}

export const PagePreloader = ({ 
  urls, 
  onComplete, 
  timeout = 5000 
}: PagePreloaderProps) => {
  const [loadedUrls, setLoadedUrls] = useState<string[]>([])
  const [isComplete, setIsComplete] = useState(false)

  useEffect(() => {
    if (urls.length === 0) {
      setIsComplete(true)
      onComplete?.([])
      return
    }

    const preloadPromises = urls.map(url => preloadUrl(url, timeout))
    
    Promise.allSettled(preloadPromises).then(results => {
      const successful = results
        .map((result, index) => result.status === 'fulfilled' ? urls[index] : null)
        .filter(Boolean) as string[]
      
      setLoadedUrls(successful)
      setIsComplete(true)
      onComplete?.(successful)
    })
  }, [urls, onComplete, timeout])

  return null // This component doesn't render anything
}

// Utility function to preload a URL
const preloadUrl = (url: string, timeout: number): Promise<string> => {
  return new Promise((resolve, reject) => {
    // Create a hidden iframe for preloading
    const iframe = document.createElement('iframe')
    iframe.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      width: 1px;
      height: 1px;
      opacity: 0;
      pointer-events: none;
    `

    const timeoutId = setTimeout(() => {
      cleanup()
      reject(new Error(`Preload timeout for ${url}`))
    }, timeout)

    const cleanup = () => {
      clearTimeout(timeoutId)
      if (document.body.contains(iframe)) {
        document.body.removeChild(iframe)
      }
    }

    iframe.onload = () => {
      cleanup()
      resolve(url)
    }

    iframe.onerror = () => {
      cleanup()
      reject(new Error(`Failed to preload ${url}`))
    }

    document.body.appendChild(iframe)
    iframe.src = url
  })
}

// Hook for managing page preloading
export const usePagePreloader = () => {
  const [preloadedUrls, setPreloadedUrls] = useState<Set<string>>(new Set())
  const [isPreloading, setIsPreloading] = useState(false)

  const preloadPages = async (urls: string[], timeout = 5000): Promise<string[]> => {
    setIsPreloading(true)
    
    try {
      const promises = urls.map(url => {
        if (preloadedUrls.has(url)) {
          return Promise.resolve(url)
        }
        return preloadUrl(url, timeout)
      })

      const results = await Promise.allSettled(promises)
      const successful = results
        .map((result, index) => result.status === 'fulfilled' ? urls[index] : null)
        .filter(Boolean) as string[]

      // Update preloaded URLs cache
      setPreloadedUrls(prev => {
        const newSet = new Set(prev)
        successful.forEach(url => newSet.add(url))
        return newSet
      })

      return successful
    } finally {
      setIsPreloading(false)
    }
  }

  const isUrlPreloaded = (url: string): boolean => {
    return preloadedUrls.has(url)
  }

  const clearPreloadCache = () => {
    setPreloadedUrls(new Set())
  }

  return {
    preloadPages,
    isUrlPreloaded,
    isPreloading,
    preloadedUrls: Array.from(preloadedUrls),
    clearPreloadCache
  }
}

// Component for preloading product pages on hover
interface ProductPagePreloaderProps {
  productSlug: string
  children: React.ReactNode
  preloadDelay?: number
}

export const ProductPagePreloader = ({ 
  productSlug, 
  children, 
  preloadDelay = 500 
}: ProductPagePreloaderProps) => {
  const { preloadPages, isUrlPreloaded } = usePagePreloader()
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null)

  const handleMouseEnter = () => {
    const productUrl = `/products/${productSlug}`
    
    if (!isUrlPreloaded(productUrl)) {
      const timeout = setTimeout(() => {
        preloadPages([productUrl]).catch(console.warn)
      }, preloadDelay)
      
      setHoverTimeout(timeout)
    }
  }

  const handleMouseLeave = () => {
    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
  }

  return (
    <div
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  )
}

export default PagePreloader
