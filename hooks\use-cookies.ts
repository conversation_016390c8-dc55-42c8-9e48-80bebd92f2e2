"use client";

/**
 * A simple custom hook for handling cookies in client components
 * This replaces the next-client-cookies package which is causing build issues
 */
export function useCookies() {
  // Get a cookie value by name
  const get = (name: string): string | undefined => {
    if (typeof document === 'undefined') {
      return undefined;
    }
    
    const cookies = document.cookie.split(';');
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].trim();
      // Check if this cookie starts with the name we want
      if (cookie.substring(0, name.length + 1) === name + '=') {
        return decodeURIComponent(cookie.substring(name.length + 1));
      }
    }
    return undefined;
  };

  // Set a cookie with the specified attributes
  const set = (
    name: string, 
    value: string, 
    options: {
      path?: string;
      maxAge?: number;
      sameSite?: 'strict' | 'lax' | 'none';
      secure?: boolean;
      expires?: Date;
    } = {}
  ): void => {
    if (typeof document === 'undefined') {
      return;
    }
    
    let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
    
    if (options.path) {
      cookieString += `; path=${options.path}`;
    }
    
    if (options.maxAge) {
      cookieString += `; max-age=${options.maxAge}`;
    }
    
    if (options.sameSite) {
      cookieString += `; samesite=${options.sameSite}`;
    }
    
    if (options.secure) {
      cookieString += '; secure';
    }
    
    if (options.expires) {
      cookieString += `; expires=${options.expires.toUTCString()}`;
    }
    
    document.cookie = cookieString;
  };

  // Remove a cookie
  const remove = (name: string, path = '/'): void => {
    if (typeof document === 'undefined') {
      return;
    }
    
    document.cookie = `${encodeURIComponent(name)}=; path=${path}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
  };

  return { get, set, remove };
} 