/**
 * Test file for subscription API route
 * 
 * This file contains test cases to verify the plan code normalization logic
 * in the subscription API route.
 */

import { validateAndNormalizePlanCode } from "@/src/services/lago/subscriptionService";
import { extractProductCode } from "@/src/utils/plan-utils";

// Test function to simulate the API route logic
export function testPlanCodeNormalization(planCode: string): string {
  const productCode = extractProductCode(planCode);
  console.log(`Test - Extracted product code: ${productCode} from plan: ${planCode}`);

  // Define known product prefixes that should skip normalization
  const knownProductPrefixes = ['onesociety_', 'onegate_', 'onepos_', 'onefooddialer_'];
  const shouldSkipNormalization = knownProductPrefixes.some(prefix => planCode.startsWith(prefix));

  if (shouldSkipNormalization) {
    // Skip normalization for known product codes to preserve original format
    console.log(`Test - Using original plan code for known product: ${planCode}`);
    return planCode;
  } else {
    // Normalize only for unknown product codes
    const normalizedPlanCode = validateAndNormalizePlanCode(
      planCode,
      {
        productName: productCode || undefined,
        isTrial: false
      }
    );

    if (normalizedPlanCode !== planCode) {
      console.log(`Test - Plan code normalized: ${planCode} → ${normalizedPlanCode}`);
      return normalizedPlanCode;
    }
    
    return planCode;
  }
}

// Test cases
export function runTests() {
  const testCases = [
    { input: 'onesociety_unlimited_m', expected: 'onesociety_unlimited_m' },
    { input: 'onegate_premium_y', expected: 'onegate_premium_y' },
    { input: 'onepos_standard_m', expected: 'onepos_standard_m' },
    { input: 'onefooddialer_basic_y', expected: 'onefooddialer_basic_y' },
    { input: 'custom_standard_m', expected: 'custom_standard_m' }, // Should be normalized if needed
  ];

  console.log('Running plan code normalization tests:');
  console.log('=====================================');

  testCases.forEach((testCase, index) => {
    const result = testPlanCodeNormalization(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`Test ${index + 1}: ${passed ? 'PASSED' : 'FAILED'}`);
    console.log(`  Input:    ${testCase.input}`);
    console.log(`  Expected: ${testCase.expected}`);
    console.log(`  Actual:   ${result}`);
    console.log('-------------------------------------');
  });
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  console.log('Tests can be run by calling runTests() in the browser console');
} else {
  runTests();
}
