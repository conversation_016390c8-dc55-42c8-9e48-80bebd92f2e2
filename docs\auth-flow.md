# Authentication Flow & User Identity Dependency

## 1. Technology Stack
* **Next.js App Router** (v14) – full-stack React framework
* **next-auth** – authentication framework (JWT strategy)
* **Keycloak** – OpenID-Connect (OIDC) identity provider
* **Credentials provider** – optional fallback login
* **Axios / Fetch** – network requests
* **TypeScript** throughout for type-safety
* **Lago API** – customer billing records (maps 1-to-1 to Keycloak user-ID)
* **Keycloak Service** – dedicated utility service for ID validation/sanitization
* **Middleware** – route protection and payment flow handling

---

## 2. High-level Sequence Diagram
```mermaid
sequenceDiagram
    participant Browser
    participant Middleware
    participant LoginPage (Credentials)
    participant next-auth client
    participant NextAuth API Route
    participant Keycloak
    participant UserProvider
    participant KeycloakService
    participant Lago API

    Browser->>Middleware: Request to protected route
    Middleware->>Browser: Allow/redirect based on auth status
    Browser->>LoginPage: enter phone / pwd OR click "Sign in with Keycloak"
    LoginPage->>next-auth client: signIn(provider, opts)
    next-auth client->>NextAuth API Route: /api/auth/*
    alt Keycloak provider
        NextAuth API Route->>Keycloak: OIDC Code Flow
        Keycloak-->>NextAuth API Route: id_token, access_token, refresh_token
        NextAuth API Route->>NextAuth API Route: fetchUserProfile()
    else Credentials provider
        NextAuth API Route: validate via authorize()
    end
    NextAuth API Route->>NextAuth API Route: callbacks.jwt() + optimizeTokenSize()
    Note over NextAuth API Route: • Persist minimal fields<br/>• embed Keycloak `sub` as `token.sub`<br/>• Apply size optimization
    NextAuth API Route-->>next-auth client: encrypted, chunked JWT cookie
    next-auth client-->>Browser: session stored
    Browser->>UserProvider: useSession()
    UserProvider->>KeycloakService: validateAndSanitizeId()
    KeycloakService-->>UserProvider: validated Keycloak ID
    UserProvider->>Lago API: getOrCreateUser() via userService
    Note over UserProvider: • Store in localStorage<br/>• Update context state<br/>• Handle fallback scenarios
```

---

## 3. Detailed Flow

### 3.1 Entry points
1. **`/app/login/page.tsx`** – username/password form ➜ `signIn('credentials')`
2. **Anywhere** – `signIn('keycloak')` (default social button or silent refresh)
3. **`SessionErrorHandler`** – auto-retries silent `signIn('keycloak', {prompt:'none'})` on refresh errors
4. **Middleware protection** – routes can be protected via `middleware.ts`
5. **Trial/subscription flows** – automatic auth redirect with state preservation in localStorage

### 3.2 Middleware Layer (`middleware.ts`)
* **Payment flow handling** – manages payment success cookies and cart clearing
* **Product slug redirects** – maps root-level product URLs to `/products/[slug]`
* **Route protection placeholder** – currently commented out but available for implementation
* **CORS and security headers** – proper cookie handling for cross-domain scenarios

### 3.3 `app/api/auth/[...nextauth]/options.ts`
* **Providers**
  * **KeycloakProvider** (primary) – scopes: `openid email profile`, enables offline refresh
  * **CredentialsProvider** – mock implementation; returns `{ id, name }`
* **Session strategy**: `jwt` with 12 h `maxAge`
* **Environment validation** – validates all required env vars on startup
* **Cookie configuration** – production-ready with `__Secure-` prefixes, domain sharing
* **JWT chunking** – handles large tokens automatically

#### 3.3.1 Callbacks in Detail
* **`jwt` callback**
  * **Initial sign-in**: stores `accessToken`, `refreshToken`, `accessTokenExpires`, `provider`
  * **User profile fetch**: calls `fetchUserProfile()` to get minimal data from Keycloak `/userinfo`
  * **Critical ID storage**: `token.sub = userProfile.sub` (Keycloak user-id) – ESSENTIAL for all downstream flows
  * **Refresh logic**: 
    * Triggers when < 60s to expiry
    * Calls `refreshAccessToken()` with retry logic
    * Implements exponential backoff (5min intervals)
    * Sets `RefreshAccessTokenError` flag on failure
  * **Size optimization**: `optimizeTokenSize()` keeps tokens < 2.5KB, removes `id_token`
  * **Logout handling**: `trigger === "signOut"` sets `isLoggedOut: true`

* **`session` callback**
  * **Logout validation**: rejects if `token.isLoggedOut`
  * **Error handling**: manages `RefreshAccessTokenError` with 30min timeout
  * **Minimal session object**:
    ```ts
    {
      user: {
        id: token.sub, // CRITICAL: Keycloak user ID
        name: token.name,
        email: token.email
      },
      accessToken: token.accessToken,
      error: token.error,
      expires: "2025-06-26T14:00:00.000Z" // ISO-8601 timestamp
    }
    ```

* **`redirect` callback**
  * **URL validation**: strict allowlist for security
  * **Keycloak integration**: allows redirects to Keycloak server
  * **Forced re-auth**: `/login` or `/signin` always redirect to Keycloak with `prompt=login`
  * **Callback URL handling**: extracts and validates callback URLs from parameters

* **`events`**
  * **`signOut`**: executes `doFinalSignoutHandshake()` for complete Keycloak logout
  * **`signIn`**: logs successful authentications for audit
  * **`session`**: tracks session errors and updates

### 3.4 Client-side Authentication Components

#### 3.4.1 `context/user-context.tsx` (UserProvider)
* **Session monitoring**: uses `useSession()` to track auth state
* **Multi-source ID extraction**:
  1. Primary: `session.user.id` (preferred method)
  2. Fallback: decode `session.accessToken` JWT payload for `sub`
  3. Emergency: localStorage recovery
* **ID validation**: uses UUID regex to verify Keycloak ID format
* **Lago integration**: calls `getOrCreateUser()` to ensure customer exists
* **State persistence**: stores `user_id`, `user_email`, `user_name`, `is_keycloak_user` in localStorage
* **Error scenarios**: handles missing IDs gracefully with extensive logging
* **Manual overrides**: `setUserInfo()` and `clearUserInfo()` methods

#### 3.4.2 `components/auth/session-error-handler.tsx`
* **Error monitoring**: listens for `session.error` changes
* **Toast notifications**: user-friendly error messages
* **Automatic recovery**:
  * `RefreshAccessTokenError`: silent re-auth attempt
  * `SessionExpired`: redirect to login after delay
  * `UserLoggedOut`: no action needed
* **Prevents duplicate toasts**: uses unique IDs

#### 3.4.3 Provider Hierarchy (`components/providers.tsx`)
```tsx
<SessionProvider>
  <ThemeProvider>
    <LoadingProvider>
      <ReactQueryProvider>
        <UserProvider>
          <CartProvider>
            <ClientProvider>
              <SessionErrorHandler />
              <SubscriptionRecovery />
              {children}
            </ClientProvider>
          </CartProvider>
        </UserProvider>
      </ReactQueryProvider>
    </LoadingProvider>
  </ThemeProvider>
</SessionProvider>
```

### 3.5 Keycloak Service Layer (`src/services/keycloakService.ts`)
* **ID validation**: `isValidKeycloakId()` with UUID regex
* **ID sanitization**: `sanitizeKeycloakId()` handles various formats:
  * Extracts UUID patterns from larger strings
  * Converts 32-char strings to UUID format
  * Removes `customer-` prefixes
* **Multi-source resolution**: `resolveKeycloakId()` with priority order:
  1. URL parameters (highest priority)
  2. JWT token payload
  3. NextAuth session
  4. localStorage (lowest priority)
* **Session extraction**: `getKeycloakIdFromSession()` handles various session formats
* **Token parsing**: `getKeycloakIdFromToken()` decodes JWT safely

### 3.6 Backend Services Integration

#### 3.6.1 `src/services/userService.ts`
* **Core mapping layer**: Keycloak user-id ↔ Lago customer.external_id
* **Operations**:
  * `checkUserById()` – GET `/customers/{external_id}`
  * `createUser()` – POST with idempotency key
  * `getOrCreateUser()` – orchestration method used by UserProvider
* **ID validation**: uses `validateAndSanitizeId()` with extensive logging
* **Error resilience**: continues with original ID if validation fails
* **Debugging support**: localStorage snapshots for troubleshooting

#### 3.6.2 API Routes with Keycloak Integration
* **`/api/lago/customer`** – customer CRUD with ID sanitization
* **`/api/payment`** – payment processing with Keycloak ID validation
* **`/api/checkout/confirm`** – webhook handling with multi-source ID resolution
* **`/api/lago/subscription`** – subscription management with ID sanitization

---

## 4. Authentication Scenarios & Flows

### 4.1 Standard Login Flow
1. User clicks "Sign In" → `signIn('keycloak')`
2. Redirect to Keycloak → OIDC flow → tokens returned
3. `jwt` callback stores minimal user data + Keycloak `sub`
4. `session` callback creates minimal session object
5. UserProvider extracts ID → validates → calls Lago API
6. User context updated → app functionality unlocked

### 4.2 Trial Activation Flow (Unauthenticated)
1. User clicks "Start Trial" on product page
2. Form data + plan details saved to localStorage
3. `signIn('keycloak', { callbackUrl: '/products/[slug]?trial=true' })`
4. After auth: page checks localStorage → processes trial
5. Calls subscription API with validated Keycloak ID

### 4.3 Subscription Purchase Flow
1. User adds items to cart (anonymous)
2. Clicks "Subscribe" → checks auth status
3. If unauthenticated: saves plan to localStorage → redirects to login
4. After auth: processes pending subscription
5. Payment API requires validated Keycloak ID
6. Webhook confirmation uses multi-source ID resolution

### 4.4 Token Refresh Scenarios
1. **Successful refresh**: transparent to user, updates tokens
2. **Refresh failure**: sets `RefreshAccessTokenError` flag
3. **Recovery attempt**: retries after 5min interval
4. **Final failure**: invalidates session after 30min, redirects to login

### 4.5 Session Recovery Scenarios
1. **Page reload**: UserProvider checks localStorage first
2. **Cross-tab sync**: localStorage changes trigger context updates
3. **Network issues**: graceful degradation with cached user data
4. **ID mismatch**: validation attempts with fallback to original

---

## 5. Where User-ID Is Consumed

### 5.1 Critical Dependencies
1. **Session (`session.user.id`)** – required by server actions
2. **UserProvider state** – feeds all UI components via `useUser()`
3. **Billing/subscription flows** – Lago API expects `external_id = Keycloak sub`
4. **Cart/checkout** – payment processing requires validated Keycloak ID
5. **API routes** – all customer-related endpoints need ID for data isolation

### 5.2 Failure Impact Matrix
| Missing/Invalid ID | Impact | Affected Components |
|-------------------|--------|-------------------|
| No `session.user.id` | Cart functionality fails | Checkout, Subscriptions |
| Invalid UUID format | Lago API 404/422 errors | All billing operations |
| Missing from localStorage | Context initialization fails | UI state, persistence |
| Token decode failure | Fallback scenarios triggered | Recovery flows |

---

## 6. Comprehensive Error Handling Matrix

| Stage | Error Code/Flag | Handling | Recovery |
|-------|----------------|----------|----------|
| **Environment** | Missing env vars | Startup failure | Fix configuration |
| **Token refresh** | `RefreshAccessTokenError` | JWT flag → UI toast → Silent retry | 5min intervals, 30min timeout |
| **Manual sign-out** | `isLoggedOut: true` | Session invalidation | Full re-authentication |
| **Session callback** | `SessionExpired` | Expired timestamp → UI toast | Redirect to login |
| **User context** | Invalid/missing ID | Console errors + fallbacks | Validation + sanitization |
| **Lago API** | 422 Customer exists | Swallow error → retry check | `checkUserById()` |
| **Lago API** | 404 Not found | Return null → trigger create | `createUser()` |
| **Payment API** | Missing Keycloak ID | 400 error → block payment | Require authentication |
| **Webhook** | ID validation failure | Log warning → continue | Use original ID |
| **Network** | Request timeout | Retry logic | Exponential backoff |
| **Storage** | localStorage errors | Catch + ignore | Graceful degradation |

---

## 7. Security Considerations

### 7.1 Cookie Security
* **Production cookies**: `__Secure-` and `__Host-` prefixes
* **Attributes**: `httpOnly`, `secure`, `sameSite: "lax"`
* **Domain sharing**: configurable via `COOKIE_DOMAIN`
* **Size limits**: token optimization prevents cookie overflow

### 7.2 Token Management
* **Refresh tokens**: never exposed to client, server-only
* **Access tokens**: minimal exposure, automatic rotation
* **ID tokens**: removed from cookies to save space
* **Validation**: all tokens validated before use

### 7.3 Redirect Protection
* **URL allowlist**: prevents open-redirect vulnerabilities
* **Domain validation**: only trusted domains allowed
* **Callback validation**: extracts and validates callback URLs

### 7.4 API Security
* **CORS headers**: proper cross-origin handling
* **Input validation**: all IDs validated/sanitized
* **Idempotency keys**: prevent duplicate operations
* **Error disclosure**: minimal error information to clients

---

## 8. Performance & Optimization

### 8.1 Token Size Management
* **Size monitoring**: logs token size in bytes
* **Aggressive optimization**: removes non-essential fields
* **Chunking support**: NextAuth handles large tokens automatically
* **Threshold alerts**: warns when approaching 2.5KB limit

### 8.2 Caching Strategies
* **localStorage persistence**: reduces API calls
* **Session validation**: cached results for performance
* **User context**: minimizes re-initialization
* **API responses**: leverages browser caching where appropriate

---

## 9. Development & Debugging

### 9.1 Logging Strategy
* **Structured logging**: consistent emoji prefixes for easy filtering
* **Debug levels**: INFO, SUCCESS, WARNING, ERROR
* **Context preservation**: includes request IDs and user identifiers
* **Storage snapshots**: localStorage debugging data

### 9.2 Debug Tools
* **localStorage inspection**: validation history, error logs
* **Console filtering**: search by emoji prefixes (🔍, ✅, ❌, ⚠️)
* **Session debugging**: full session object logging in development
* **Token inspection**: JWT payload examination

### 9.3 Testing Scenarios
* **ID format variations**: test UUID, non-UUID, malformed IDs
* **Network failures**: simulate timeout and error conditions
* **Storage limitations**: test localStorage quota exceeded
* **Cross-browser compatibility**: different cookie handling

---

## 10. Extensibility Tips

### 10.1 Adding New Providers
1. Register provider in `authOptions.providers`
2. Ensure `token.sub` uniqueness across providers
3. Update `fetchUserProfile()` if needed
4. Test ID extraction and validation

### 10.2 Enhanced User Data
1. Extend `fetchUserProfile()` for additional claims
2. Update JWT/Session interfaces
3. **Critical**: Keep token size under 2.5KB
4. Update UserProvider context as needed

### 10.3 Additional Services
1. Follow Keycloak ID validation pattern
2. Use `keycloakService` utilities for consistency
3. Implement proper error handling
4. Add comprehensive logging

---

## 11. Troubleshooting Checklist

### 11.1 Authentication Issues
- [ ] All required environment variables set?
- [ ] Keycloak server accessible?
- [ ] Client ID/secret correct?
- [ ] Callback URLs configured in Keycloak?

### 11.2 Session Issues
- [ ] `session.user.id` present and valid UUID?
- [ ] Token size under 2.5KB limit?
- [ ] Cookie domain configuration correct?
- [ ] No infinite refresh loops?

### 11.3 User Context Issues
- [ ] UserProvider properly wrapped around app?
- [ ] localStorage accessible (not in incognito)?
- [ ] ID validation passing?
- [ ] Lago API endpoints responding?

### 11.4 Payment/Subscription Issues
- [ ] Keycloak ID passed to payment API?
- [ ] Webhook receiving correct customer ID?
- [ ] Subscription service can find customer?
- [ ] Cart context properly initialized?

---

## 12. Emergency Recovery

### 12.1 User ID Recovery Script
The codebase includes `src/utils/emergency-subscription.js` for manual ID recovery:
- Checks NextAuth session
- Validates localStorage data
- Parses stored tokens
- Extracts from URL parameters

### 12.2 Manual Session Reset
```javascript
// Clear all auth-related storage
localStorage.removeItem('user_id');
localStorage.removeItem('user_email');
localStorage.removeItem('user_name');
localStorage.removeItem('is_keycloak_user');
// Force sign out and re-authentication
signOut({ callbackUrl: '/login' });
```

---

_Last updated: 2025-01-25_
_Covers: NextAuth 4.x, Keycloak integration, Lago API, comprehensive error handling_ 