import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_TESTIMONIALS_CONTENT_BY_SLUG = gql`
  query ClientReviewsBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
        slug
      sections {
        ... on ComponentSectionsTestimonialSection {
          id
          title
          testimonials {
            id
            name
            photo {
              url
            }
            rating
            designation
            description
          }
        }
      }
    }
  }
`;

export const getTestimonialsContentBySlug = async (slug: string) => {
  try {
    const { data } = await client.query({
      query: GET_TESTIMONIALS_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    const testimonialSection = data.products[0]?.sections.find(
      (section: { __typename: string }) => section.__typename === "ComponentSectionsTestimonialSection"
    );
    return testimonialSection || null;
  } catch (error) {
    console.error("Error fetching testimonial section data:", error);
    throw error;
  }
};
