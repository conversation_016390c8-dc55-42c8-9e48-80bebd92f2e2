/**
 * Service for user-related operations including interactions with Lago API
 */

import { LAGO_API_URL, LAGO_API_KEY } from "../constants/api";

// Base URL for customer operations
const CUSTOMER_URL = `${LAGO_API_URL}/customers`;

// User interface matching Lago API requirements
export interface UserData {
  external_id: string;  // This should be the Keycloak ID
  email?: string;
  name?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  country?: string;
  currency?: string;
  tax_identification_number?: string;
  phone?: string;
  state?: string;
  zipcode?: string;
  customer_type?: string;
  // Add additional fields as needed for Lago API
}

/**
 * Validates the Keycloak ID format
 * @param id The ID to validate
 * @returns Whether the ID looks like a valid Keycloak UUID
 */
function isValidKeycloakId(id: string): boolean {
  if (!id) return false;
  
  // Basic UUID format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Logs a detailed diagnostic message about the ID validation
 * @param id The ID being validated
 * @param source Where the ID came from
 * @param isValid Whether the ID is valid
 */
function logIdValidation(id: string, source: string, isValid: boolean) {
  const validationMessage = isValid 
    ? "✅ ID validation passed" 
    : "❌ ID validation failed";
  
  console.log(`🔍 VALIDATION [${source}] - ${validationMessage}:`, {
    id,
    isValid,
    format: "UUID",
    length: id.length,
    hasDashes: id.includes('-'),
    firstChar: id.charAt(0),
    lastChar: id.charAt(id.length - 1)
  });
  
  // Store all ID validations for debugging
  try {
    const validations = JSON.parse(localStorage.getItem('id_validations') || '[]');
    validations.push({
      id: id.substring(0, 8) + '...',
      source,
      isValid,
      timestamp: Date.now()
    });
    localStorage.setItem('id_validations', JSON.stringify(validations.slice(-20))); // Keep last 20
  } catch {
    // Ignore storage errors
  }
}

/**
 * Enhanced validation that tries to sanitize the ID if needed
 * @param id The ID to validate and sanitize if necessary
 * @param source Where the ID came from for logging
 * @returns The sanitized ID if possible, or null if invalid
 */
function validateAndSanitizeId(id: string, source: string): string | null {
  if (!id) {
    console.error(`❌ USER - Empty ID from ${source}`);
    return null;
  }
  
  // Try direct validation first
  const isValid = isValidKeycloakId(id);
  logIdValidation(id, source, isValid);
  
  if (isValid) {
    return id; // ID is already valid
  }
  
  // If not valid, try to sanitize it
  console.log(`⚠️ USER - Attempting to sanitize non-standard ID format from ${source}:`, id);
  
  // Try to extract UUID pattern if embedded in a larger string
  const uuidPattern = /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i;
  const match = id.match(uuidPattern);
  
  if (match) {
    const extractedId = match[1];
    console.log(`✅ USER - Successfully extracted UUID format from ${source}:`, extractedId);
    logIdValidation(extractedId, `${source} (extracted)`, true);
    return extractedId;
  }
  
  // If no UUID pattern found, check if it's a UUID without dashes
  if (id.length === 32) {
    try {
      // Attempt to insert dashes in UUID format
      const withDashes = `${id.substring(0, 8)}-${id.substring(8, 12)}-${id.substring(12, 16)}-${id.substring(16, 20)}-${id.substring(20)}`;
      if (isValidKeycloakId(withDashes)) {
        console.log(`✅ USER - Successfully reformatted UUID from ${source}:`, withDashes);
        logIdValidation(withDashes, `${source} (reformatted)`, true);
        return withDashes;
      }
    } catch (error) {
      console.error(`❌ USER - Error reformatting ID from ${source}:`, error);
    }
  }
  
  // Could not sanitize into valid format
  console.error(`❌ USER - Could not sanitize ID from ${source} into valid Keycloak format:`, id);
  return null;
}

/**
 * Find a user by email - only used for debugging, not in the main user flow
 * @param email The email to search for
 * @returns Array of users with matching email
 */
export async function findUserByEmail(email: string): Promise<UserData[]> {
  if (!email) {
    console.error("❌ USER - Cannot find user by email: Email is required");
    return [];
  }
  
  console.log(`🔍 USER - Searching for user with email: ${email}`);
  
  try {
    // Note: This is not a standard Lago API endpoint
    // This is just for demonstration and would need to be adjusted for your actual API
    const response = await fetch(`${LAGO_API_URL}/customers/search?email=${encodeURIComponent(email)}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      const users = data.customers || [];
      console.log(`✅ USER - Found ${users.length} users with email: ${email}`);
      return users;
    } else {
      console.error(`❌ USER - Error searching users by email (${response.status})`);
      return [];
    }
  } catch {
    console.error("❌ USER - Exception searching users by email:");
    return [];
  }
}

/**
 * Checks if a user exists in Lago by their Keycloak ID
 * @param keycloakId The Keycloak ID to check
 * @returns The user object if found, null otherwise
 */
export async function checkUserById(keycloakId: string): Promise<UserData | null> {
  if (!keycloakId) {
    console.error("❌ USER - Cannot check user: No ID provided");
    return null;
  }
  
  // Validate and sanitize ID
  const validatedId = validateAndSanitizeId(keycloakId, "checkUserById");
  if (!validatedId) {
    console.error(`❌ USER - Cannot check user with invalid ID format: ${keycloakId}`);
    // Log this for debugging, but don't block the flow as requested
    try {
      localStorage.setItem('invalid_check_id', keycloakId);
    } catch {
      // Ignore storage errors
    }
    // Continue with original ID instead of returning null
    console.log(`⚠️ USER - Continuing check with original ID: ${keycloakId}`);
  }
  
  // Use the validated ID if available, otherwise use the original
  const idToUse = validatedId || keycloakId;
  console.log(`🔍 USER - Checking if user exists with ID: ${idToUse}`);
  
  // Record this ID for debugging
  try {
    localStorage.setItem('last_checked_keycloak_id', idToUse);
    localStorage.setItem('lago_api_check_url', `${CUSTOMER_URL}/${idToUse}`);
  } catch {
    // Ignore storage errors
  }
  
  try {
    // IMPORTANT: Use the correct endpoint format with the ID directly
    // This should be: /api/v1/customers/{external_id}
    const response = await fetch(`${CUSTOMER_URL}/${idToUse}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });
    
    console.log(`🔄 USER - Customer check response status: ${response.status} for ID: ${idToUse}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ USER - User found with ID ${idToUse}`);
      try {
        localStorage.setItem('last_found_user', JSON.stringify({
          id: idToUse,
          timestamp: Date.now()
        }));
      } catch {
        // Ignore storage errors
      }
      return data;
    } else if (response.status === 404) {
      console.log(`ℹ️ USER - User not found with ID: ${idToUse}`);
      return null; // Let the caller decide whether to create
    } else {
      const errorText = await response.text();
      console.error(`❌ USER - API error checking user (${response.status}):`, errorText);
      try {
        localStorage.setItem('user_check_error', JSON.stringify({
          id: idToUse,
          status: response.status,
          error: errorText,
          timestamp: Date.now()
        }));
      } catch {
        // Ignore storage errors
      }
      return null;
    }
  } catch {
    console.error("❌ USER - Error checking user by ID:");
    return null;
  }
}

/**
 * Creates a new user in Lago
 * @param userData The user data containing the Keycloak ID as external_id
 * @returns The created user object if successful, null otherwise
 */
export async function createUser(userData: UserData): Promise<UserData | null> {
  if (!userData) {
    console.error("❌ USER - Cannot create user: Invalid user data");
    return null;
  }
  
  let validatedId: string | null = null;
  
  // Check if we have an external_id to validate
  if (userData.external_id) {
    // CRITICAL: Validate the Keycloak ID format if available
    validatedId = validateAndSanitizeId(userData.external_id, "createUser");
    if (validatedId) {
      // If validation succeeded, always use the validated ID
      userData.external_id = validatedId;
      console.log(`✅ USER - Using validated Keycloak ID: ${validatedId}`);
    } else {
      // If validation failed but we're instructed to continue anyway
      console.error(`❌ USER - Cannot validate Keycloak ID format: ${userData.external_id}`);
      console.log(`⚠️ USER - Continuing with original ID: ${userData.external_id}`);
    }
  } else {
    console.warn("⚠️ USER - No external_id provided, creating user with empty ID");
  }
  
  console.log(`🔄 USER - Creating user with ID: ${userData.external_id || 'EMPTY'}`);
  
  // Save for debugging
  try {
    localStorage.setItem('last_create_attempt', JSON.stringify({
      id: userData.external_id || 'EMPTY',
      timestamp: Date.now()
    }));
  } catch {
    // Ignore storage errors
  }
  
  // Generate an idempotency key that's stable for the same external_id
  // This helps prevent duplicate users from being created
  const idempotencyKey = userData.external_id ? `create-${userData.external_id}` : `create-${Date.now()}`;
  
  try {
    // If we have an ID, first check if the user already exists
    if (userData.external_id) {
      const existingUser = await checkUserById(userData.external_id);
      if (existingUser) {
        console.log(`ℹ️ USER - User already exists with ID: ${userData.external_id}, skipping creation`);
        return existingUser;
      }
    }
    
    // CRITICAL: Prepare the payload with the ID (which might be validated Keycloak ID or original)
    // Always ensuring we're using the correct ID format in the API call
    const payload = {
      customer: {
        external_id: userData.external_id || '', // Use empty string if no ID
        name: userData.name || 'Customer',
        email: userData.email,
        currency: "INR", // Default to INR
        country: "IN",  // Default to India
        customer_type: "individual",
        // Optional fields - only include if provided
        ...(userData.address_line1 && { address_line1: userData.address_line1 }),
        ...(userData.address_line2 && { address_line2: userData.address_line2 }),
        ...(userData.city && { city: userData.city }),
        ...(userData.state && { state: userData.state }),
        ...(userData.zipcode && { zipcode: userData.zipcode }),
        ...(userData.phone && { phone: userData.phone }),
        ...(userData.tax_identification_number && { tax_identification_number: userData.tax_identification_number })
      }
    };
    
    // Log the full payload for verification
    console.log(`📤 USER - Creating user with payload:`, JSON.stringify(payload));
    
    // Additional verification log for the external_id
    if (userData.external_id) {
      console.log(`🔍 USER - Verifying customer external_id: ${
        payload.customer.external_id === userData.external_id ? '✅ Correct' : '❌ Mismatch'
      }`);
    } else {
      console.log(`🔍 USER - Customer being created with empty external_id as requested`);
    }
    
    // Record the payload for debugging
    try {
      localStorage.setItem('last_create_payload', JSON.stringify(payload));
      localStorage.setItem('lago_api_create_url', CUSTOMER_URL);
    } catch {
      // Ignore storage errors
    }
    
    const response = await fetch(CUSTOMER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`,
        'Idempotency-Key': idempotencyKey
      },
      body: JSON.stringify(payload)
    });
    
    console.log(`🔄 USER - User creation response status: ${response.status} for ID: ${userData.external_id || 'EMPTY'}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ USER - User created successfully with ID: ${userData.external_id || 'EMPTY'}`);
      // Verify the returned customer has the correct external_id
      if (data.customer && data.customer.external_id) {
        console.log(`🔍 USER - Verifying created customer external_id: ${
          data.customer.external_id === userData.external_id ? '✅ Correct' : '❌ Mismatch'
        } - Got: ${data.customer.external_id}, Expected: ${userData.external_id}`);
      }
      
      // Save successful creation
      try {
        localStorage.setItem('last_created_user', JSON.stringify({
          id: userData.external_id || 'EMPTY',
          timestamp: Date.now()
        }));
      } catch {
        // Ignore storage errors
      }
      return data;
    } else {
      const errorText = await response.text();
      console.error(`❌ USER - Error creating user (${response.status}):`, errorText);
      
      // Special handling for 422 errors (validation errors)
      if (response.status === 422) {
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.error === "Customer already exists") {
            console.log(`ℹ️ USER - User already exists with ID: ${userData.external_id || 'EMPTY'}`);
            // Retry getting the user
            return await checkUserById(userData.external_id || '');
          }
        } catch {
          // Ignore JSON parse errors
        }
      }
      
      // Save error for debugging
      try {
        localStorage.setItem('user_create_error', JSON.stringify({
          id: userData.external_id || 'EMPTY',
          status: response.status,
          error: errorText,
          timestamp: Date.now()
        }));
      } catch {
        // Ignore storage errors
      }
      
      return null;
    }
  } catch {
    console.error("❌ USER - Exception creating user:");
    return null;
  }
}

/**
 * Get an existing user by Keycloak ID or create one if not found
 * @param userData User data containing the Keycloak ID as external_id
 * @returns The user object if found or created, null if failed
 */
export async function getOrCreateUser(userData: UserData): Promise<UserData | null> {
  if (!userData) {
    console.error("❌ USER - Cannot get or create user: Missing user data");
    return null;
  }
  
  // If no external_id is provided, log it but continue with empty ID
  if (!userData.external_id) {
    console.error("❌ USER - Warning: Missing Keycloak ID in getOrCreateUser");
    console.log("⚠️ USER - Continuing with empty external_id as requested");
  } else {
    // Validate and sanitize ID if available
    const validatedId = validateAndSanitizeId(userData.external_id, "getOrCreateUser");
    if (validatedId) {
      // Always use the validated ID if available
      userData.external_id = validatedId;
      console.log(`✅ USER - Using validated Keycloak ID: ${validatedId}`);
    } else {
      console.error(`❌ USER - Invalid Keycloak ID format: ${userData.external_id}`);
      // Continue with the original ID even if invalid as requested
      console.log(`⚠️ USER - Continuing with original ID: ${userData.external_id}`);
    }
  }
  
  console.log(`🔍 USER - Get or create user with ID: ${userData.external_id || 'EMPTY'}`);
  
  try {
    // First check if user exists if we have an ID
    if (userData.external_id) {
      console.log(`🔄 USER - Checking if user exists with ID: ${userData.external_id}`);
      const existingUser = await checkUserById(userData.external_id);
      
      if (existingUser) {
        console.log(`✅ USER - Found existing user with ID: ${userData.external_id}`);
        return existingUser;
      }
    }
    
    // User not found or no ID provided, create new one
    console.log(`🔄 USER - User not found, creating new user ${userData.external_id ? `with ID: ${userData.external_id}` : 'with empty ID'}`);
    return await createUser(userData);
  } catch {
    console.error(`❌ USER - Error in getOrCreateUser:`);
    return null;
  }
}