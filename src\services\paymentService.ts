/**
 * Payment Service
 *
 * Handles all interactions with payment gateway.
 * Ensures Keycloak ID is used as customer_id.
 */

// Types
export interface PaymentRequestData {
  fullName: string;
  email: string;
  phone: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  pincode: string;
  gstNumber?: string;
  businessName?: string; // Business name for GST registration
  product: string;
  price: number;
  keycloakId: string; // Explicit Keycloak ID to use as customer_id
  items: PaymentItem[];
}

export interface PaymentItem {
  id: string;
  name: string;
  productName?: string;
  price?: number;
  planCode?: string;
  planDuration?: string;
}

export interface PaymentResponse {
  url?: string;
  customerId?: string;
  error?: string;
}

/**
 * Extract Keycloak ID from session or other sources
 * @param session NextAuth session or similar object
 * @returns Keycloak ID if found, null otherwise
 */
export function extractKeycloakId(session: unknown): string | null {
  if (!session || typeof session !== 'object') return null;

  const sessionObj = session as Record<string, unknown>;
  const user = sessionObj.user as Record<string, unknown> | undefined;

  // Try different possible locations in the session
  if (user?.sub && typeof user.sub === 'string') {
    return user.sub;
  } else if (user?.id && typeof user.id === 'string') {
    return user.id;
  } else if (sessionObj.sub && typeof sessionObj.sub === 'string') {
    return sessionObj.sub;
  }

  // Couldn't find a Keycloak ID
  return null;
}

/**
 * Validates if the ID looks like a Keycloak UUID
 */
export function isValidKeycloakId(id: string): boolean {
  if (!id) return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Initiate a payment with the payment gateway
 * @param paymentData Payment request data including Keycloak ID
 * @returns Payment response with URL to redirect to
 */
export async function initiatePayment(paymentData: PaymentRequestData): Promise<PaymentResponse> {
  if (!paymentData) {
    console.error('❌ PAYMENT - Cannot initiate: Missing payment data');
    return { error: 'Missing payment data' };
  }

  const { keycloakId } = paymentData;

  if (!keycloakId) {
    console.error('❌ PAYMENT - Cannot initiate: Missing Keycloak ID');
    return { error: 'Missing Keycloak ID' };
  }

  // Validate Keycloak ID format
  const isValid = isValidKeycloakId(keycloakId);
  console.log(`🔍 PAYMENT - Keycloak ID validation: ${isValid ? '✅ Valid UUID format' : '⚠️ Not a standard UUID format'}`);

  // Log key payment information
  console.log(`🔄 PAYMENT - Initiating payment for Keycloak ID: ${keycloakId}`, {
    email: paymentData.email,
    amount: paymentData.price,
    product: paymentData.product,
    items: paymentData.items.length
  });

  try {
    // Initiate payment through API route
    const response = await fetch('/api/payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...paymentData,
        customerId: keycloakId // Ensure Keycloak ID is passed as customerId
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ PAYMENT - Initiation failed:', errorData);
      return { error: errorData.error || 'Payment initiation failed' };
    }

    const data = await response.json();
    console.log('✅ PAYMENT - Initiated successfully:', {
      url: data.url ? '(URL available)' : 'Missing',
      customerId: data.customerId
    });

    return {
      url: data.url,
      customerId: data.customerId
    };
  } catch (error) {
    console.error('❌ PAYMENT - Exception initiating payment:', error);
    return { error: String(error) };
  }
}

/**
 * Parse payment confirmation data from URL search params
 * @param searchParams URL search params from the payment confirmation
 * @returns Object with payment details and Keycloak ID
 */
export function parsePaymentConfirmation(searchParams: URLSearchParams): {
  success: boolean;
  paymentId?: string;
  orderId?: string;
  keycloakId?: string;
  signature?: string;
  amount?: number;
  error?: string;
} {
  // Extract payment details
  const paymentId = searchParams.get('razorpay_payment_id') || '';
  const orderId = searchParams.get('razorpay_order_id') || '';
  const signature = searchParams.get('razorpay_signature') || '';
  const amount = searchParams.get('amount') ? Number(searchParams.get('amount')) / 100 : undefined;
  const status = searchParams.get('status') || '';
  const error = searchParams.get('error_message') || '';

  // Extract Keycloak ID from various possible parameters
  const keycloakId =
    searchParams.get('keycloak_id') ||
    searchParams.get('customer_id') ||
    searchParams.get('customerId') ||
    '';

  // Determine if payment was successful
  const success = Boolean(paymentId && signature && status !== 'error');

  return {
    success,
    paymentId,
    orderId,
    keycloakId,
    signature,
    amount,
    error: status === 'error' ? error : undefined
  };
}