import { NextRequest, NextResponse } from 'next/server';
import { getKeycloakAdminToken, checkExistingUser } from '../register/route';

// This is a placeholder since the original file is large. 
// I'll need to export the functions from the register route file.
// In a real scenario, these would be in a shared lib file.

export async function POST(request: NextRequest) {
  try {
    const { email, mobile } = await request.json();

    if (!email || !mobile) {
      return NextResponse.json({ success: false, message: 'Email and mobile are required.' }, { status: 400 });
    }

    const adminToken = await getKeycloakAdminToken();
    if (!adminToken) {
      return NextResponse.json({ success: false, message: 'Could not connect to authentication service.' }, { status: 500 });
    }
    
    // Check for email and mobile conflicts in a single call
    const emailCheck = await checkExistingUser(adminToken, { email });
    if (emailCheck.exists) {
        return NextResponse.json({
            success: false,
            field: emailCheck.field,
            message: emailCheck.message,
        }, { status: 409 });
    }

    const mobileCheck = await checkExistingUser(adminToken, { mobile });
    if (mobileCheck.exists) {
        return NextResponse.json({
            success: false,
            field: mobileCheck.field,
            message: mobileCheck.message,
        }, { status: 409 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('[VALIDATE_STEP1] Error:', error);
    return NextResponse.json({ success: false, message: 'An unexpected error occurred.' }, { status: 500 });
  }
} 