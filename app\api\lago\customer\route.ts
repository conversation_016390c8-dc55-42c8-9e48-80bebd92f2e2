import { NextRequest, NextResponse } from "next/server";
import { safeFetch } from "@/lib/fetch-utils";
import { LAGO_API_URL, LAGO_API_KEY, corsHeaders } from "@/src/constants/api";

// Validate that LAGO_API_URL is defined
if (!LAGO_API_URL) {
  console.error("❌ API - LAGO_API_URL is not defined. Please check your environment variables.");
}

const CUSTOMER_URL = `${LAGO_API_URL}/customers`;

/**
 * Helper function to validate Keycloak ID format
 */
// function isValidKeycloakId(id: string): boolean {
//   if (!id) return false;

//   // Standard UUID format check
//   const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
//   return uuidRegex.test(id);
// }

// Helper function to sanitize Keycloak ID
function sanitizeKeycloakId(id: string): string | null {
  if (!id) return null;

  // Already valid UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(id)) return id;

  // Try to extract UUID pattern
  const uuidPattern = /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i;
  const match = id.match(uuidPattern);
  if (match) return match[1];

  // Try UUID without dashes (32 chars)
  if (id.length === 32) {
    try {
      const withDashes = `${id.substring(0, 8)}-${id.substring(8, 12)}-${id.substring(12, 16)}-${id.substring(16, 20)}-${id.substring(20)}`;
      if (uuidRegex.test(withDashes)) return withDashes;
    } catch {}
  }

  // Remove customer- prefix if present
  if (id.startsWith('customer-')) {
    const withoutPrefix = id.substring(9);
    if (uuidRegex.test(withoutPrefix)) return withoutPrefix;
  }

  return id; // Return original if no sanitization was possible
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// GET route to check if a customer exists in Lago
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const id = url.searchParams.get('id');
  const email = url.searchParams.get('email');

  if (!id && !email) {
    return NextResponse.json({ error: 'Missing id or email parameter' }, {
      status: 400,
      headers: corsHeaders
    });
  }

  if (!LAGO_API_URL) {
    console.error('❌ API - LAGO_API_URL is undefined in GET /api/lago/customer');
    return NextResponse.json({ error: 'Missing API configuration' }, {
      status: 500,
      headers: corsHeaders
    });
  }

  try {
    // If ID is provided, check customer by ID
    if (id) {
      const sanitizedId = sanitizeKeycloakId(id) || id;
      console.log(`🔍 API - Checking if customer exists by ID: ${sanitizedId}`);

      const response = await safeFetch(`${CUSTOMER_URL}/${sanitizedId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json(data, { headers: corsHeaders });
      } else if (response.status === 404) {
        return NextResponse.json({ error: 'Customer not found' }, {
          status: 404,
          headers: corsHeaders
        });
      } else {
        const error = await response.text();
        console.error(`❌ API - Error checking customer: ${response.status}`, error);
        return NextResponse.json({ error: 'Error checking customer' }, {
          status: response.status,
          headers: corsHeaders
        });
      }
    }

    // If email is provided (for search by email)
    if (email) {
      // Handle email search (if you have such an endpoint)
      // This is just a placeholder - adjust based on your actual API
      return NextResponse.json({ error: 'Email search not implemented' }, {
        status: 501,
        headers: corsHeaders
      });
    }

  } catch (error) {
    console.error('❌ API - Exception in GET /api/lago/customer:', error);
    return NextResponse.json({ error: 'Server error' }, {
      status: 500,
      headers: corsHeaders
    });
  }
}

// POST route to create a customer in Lago
export async function POST(request: NextRequest) {
  if (!LAGO_API_URL) {
    console.error('❌ API - LAGO_API_URL is undefined in POST /api/lago/customer');
    return NextResponse.json({ error: 'Missing API configuration' }, {
      status: 500,
      headers: corsHeaders
    });
  }

  try {
    const body = await request.json();

    if (!body || !body.customer || !body.customer.external_id) {
      return NextResponse.json(
        { error: 'Missing required fields (customer data with external_id)' },
        { status: 400, headers: corsHeaders }
      );
    }

    // Sanitize the Keycloak ID
    const externalId = body.customer.external_id;
    const sanitizedId = sanitizeKeycloakId(externalId);

    if (sanitizedId) {
      body.customer.external_id = sanitizedId;
      console.log(`✅ API - Using sanitized Keycloak ID: ${sanitizedId}`);
    }

    // Process GST information if provided
    if (body.customer.tax_identification_number) {
      // Normalize GST number (uppercase and trim)
      body.customer.tax_identification_number = body.customer.tax_identification_number.trim().toUpperCase();

      // Validate GST number format
      const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
      if (!gstRegex.test(body.customer.tax_identification_number)) {
        console.error(`❌ API - Invalid GST number format: ${body.customer.tax_identification_number}`);
        return NextResponse.json(
          { error: 'Invalid GST number format' },
          { status: 400, headers: corsHeaders }
        );
      }

      // Ensure business name is provided if GST number is valid
      if (!body.customer.legal_name) {
        console.error(`❌ API - Business name is required when GST number is provided`);
        return NextResponse.json(
          { error: 'Business name is required when GST number is provided' },
          { status: 400, headers: corsHeaders }
        );
      } else {
        // Trim business name
        body.customer.legal_name = body.customer.legal_name.trim();
      }

      console.log(`✅ API - Valid GST details: ${body.customer.tax_identification_number}, ${body.customer.legal_name}`);
    } else if (body.customer.legal_name) {
      // If business name is provided but no GST, trim it
      body.customer.legal_name = body.customer.legal_name.trim();
    }

    // Generate idempotency key to prevent duplicates
    const idempotencyKey = `create-${body.customer.external_id}-${Date.now()}`;

    console.log(`🔄 API - Creating customer with ID: ${body.customer.external_id}`);

    // Forward to Lago API
    const response = await safeFetch(CUSTOMER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`,
        'Idempotency-Key': idempotencyKey
      },
      body: JSON.stringify(body)
    });

    console.log(`🔄 API - Customer creation response: ${response.status}`);

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json(data, { headers: corsHeaders });
    } else {
      const errorText = await response.text();

      // Check for duplicate customer error
      if (response.status === 422) {
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.error === "Customer already exists") {
            // If customer already exists, try to fetch them
            const getResponse = await safeFetch(`${CUSTOMER_URL}/${body.customer.external_id}`, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Authorization': `Bearer ${LAGO_API_KEY}`
              }
            });

            if (getResponse.ok) {
              const data = await getResponse.json();
              return NextResponse.json(data, { headers: corsHeaders });
            }
          }
        } catch {}
      }

      return NextResponse.json(
        { error: errorText },
        { status: response.status, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error('❌ API - Exception in POST /api/lago/customer:', error);
    return NextResponse.json(
      { error: 'Server error creating customer' },
      { status: 500, headers: corsHeaders }
    );
  }
}
