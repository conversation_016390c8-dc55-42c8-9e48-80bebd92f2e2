"use client";

import { useEffect } from "react";
import { useSession, signIn } from "next-auth/react";
import { toast } from "sonner";

/**
 * SessionErrorHandler component
 * 
 * This component monitors the session for errors and handles them appropriately.
 * It provides a better user experience when refresh token errors occur.
 */
export function SessionErrorHandler() {
  const { data: session, status } = useSession();

  useEffect(() => {
    // Skip if session is loading or not authenticated
    if (status === "loading" || status === "unauthenticated") return;

    // Check for session errors
    if (session?.error) {
      console.log(`Session error detected: ${session.error}`);

      // Handle different error types
      switch (session.error) {
        case "RefreshAccessTokenError":
          // Show a toast notification
          toast.error(
            "Your session needs to be refreshed. Please wait while we try to reconnect.",
            {
              duration: 5000,
              id: "refresh-token-error", // Prevent duplicate toasts
            }
          );

          // Try to silently refresh the session
          setTimeout(() => {
            signIn("keycloak", { 
              redirect: false,
              prompt: "none" // Try silent authentication
            });
          }, 1000);
          break;

        case "SessionExpired":
          // Show a toast notification
          toast.error(
            "Your session has expired. Please sign in again.",
            {
              duration: 5000,
              id: "session-expired", // Prevent duplicate toasts
            }
          );

          // Redirect to login after a short delay
          setTimeout(() => {
            signIn("keycloak");
          }, 2000);
          break;

        case "UserLoggedOut":
          // User is already logged out, no need to show an error
          break;

        default:
          // Handle any other session errors
          toast.error(
            "There was a problem with your session. Please try signing in again.",
            {
              duration: 5000,
              id: "session-error", // Prevent duplicate toasts
            }
          );
      }
    }
  }, [session?.error, status]);

  // This component doesn't render anything visible
  return null;
}
