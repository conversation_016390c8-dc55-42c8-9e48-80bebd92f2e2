@tailwind base;
@tailwind components;
@tailwind utilities;

@import "~@devnomic/marquee/dist/index.css";

html {
  scroll-behavior: smooth;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --color-1: 0 100% 63%;
    --color-2: 270 100% 63%;
    --color-3: 210 100% 63%;
    --color-4: 195 100% 63%;
    --color-5: 90 100% 63%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --color-1: 0 100% 63%;
    --color-2: 270 100% 63%;
    --color-3: 210 100% 63%;
    --color-4: 195 100% 63%;
    --color-5: 90 100% 63%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  @apply text-black font-bold dark:text-white;
}

.btn-bezel {
  --bezel-shadow-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 0 2px var(--bezel-shadow-color), inset 0 0 0 2px var(--bezel-shadow-color);
}

@media (prefers-color-scheme: dark) {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Section highlight animation */
.highlight-section {
  animation: highlight-pulse 2s ease-in-out;
  position: relative;
  z-index: 1;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
  20% {
    box-shadow: 0 0 20px 10px rgba(59, 130, 246, 0.3);
    background-color: rgba(59, 130, 246, 0.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    background-color: rgba(59, 130, 246, 0);
  }
}

.dark .highlight-section {
  animation: highlight-pulse-dark 2s ease-in-out;
}

@keyframes highlight-pulse-dark {
  0% {
    box-shadow: 0 0 0 0 rgba(96, 165, 250, 0);
  }
  20% {
    box-shadow: 0 0 20px 10px rgba(96, 165, 250, 0.3);
    background-color: rgba(96, 165, 250, 0.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(96, 165, 250, 0);
    background-color: rgba(96, 165, 250, 0);
  }
}

/* Add styles for subscription badges */
.badge-trial {
  background-color: rgba(var(--blue-500), 0.1);
  color: rgb(var(--blue-500));
}

.badge-paid {
  background-color: rgba(var(--green-500), 0.1);
  color: rgb(var(--green-500));
}

.badge-expired {
  background-color: rgba(var(--red-500), 0.1);
  color: rgb(var(--red-500));
}

/* Animations for subscription dialog */
.subscription-dialog-enter {
  opacity: 0;
  transform: scale(0.95);
}

.subscription-dialog-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.subscription-dialog-exit {
  opacity: 1;
  transform: scale(1);
}

.subscription-dialog-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 300ms, transform 300ms;
}

/* Lottie Loader Styles */
.lottie-container {
  display: flex;
  align-items: center;
  justify-content: center;
  will-change: transform;
}

.lottie-container svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 8px rgba(var(--primary), 0.2));
}

/* Loading Page Animation */
.page-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: hsl(var(--background) / 0.85);
  backdrop-filter: blur(6px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.25s ease;
  will-change: opacity;
}
