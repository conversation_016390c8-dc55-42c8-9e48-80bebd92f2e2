"use client";

import { useState, useEffect, useRef, use<PERSON>allback } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useSession, signIn } from "next-auth/react";
import { useUser } from "@/context/user-context";
import { useCart } from "@/context/cart-context";
import { createSubscription } from "@/src/services/subscriptionService";
import { checkUserById } from "@/src/services/userService";
import { hasUserUsedTrial } from "@/src/services/trialService";
import { validateTrialEligibility } from "@/src/services/subscriptionValidationService";
import { extractProductCode } from "@/src/utils/plan-utils";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  StepperProvider,
  Stepper,
  StepperHeader,
  Step,
  StepperActions,
} from "@/components/ui/stepper";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { CheckCircle, ArrowRight, Loader2, XCircle } from "lucide-react";

// Form schema for user information
const userFormSchema = z.object({
  firstName: z.string().min(2, { message: "First name is required" }),
  lastName: z.string().min(2, { message: "Last name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
});

type UserFormValues = z.infer<typeof userFormSchema>;

interface TrialFlowProps {
  open: boolean;
  onClose: () => void;
  productSlug: string;
  trialPlan?: {
    name: string;
    tag?: string;
    plan_code: string;
    trialDurationInDays: number;
    features?: { feature: string; isIncluded: boolean; id: string }[];
    description?: string;
    button?: { text: string; id?: string };
  };
}

export function TrialFlow({ open, onClose, productSlug, trialPlan }: TrialFlowProps) {
  const router = useRouter();
  const { status } = useSession();
  const { userId, userEmail, userName } = useUser();
  const { clearCart } = useCart();
  const [isCheckingUser, setIsCheckingUser] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [hasAlreadyUsedTrial, setHasAlreadyUsedTrial] = useState(false);
  const [redirectCounter, setRedirectCounter] = useState(5);
  const redirectTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize form
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: userEmail || "",
    },
  });

  // Fill form with user data if available
  useEffect(() => {
    if (userEmail) {
      form.setValue("email", userEmail);

      if (userName) {
        const nameParts = userName.split(" ");
        if (nameParts.length > 0) {
          form.setValue("firstName", nameParts[0]);

          if (nameParts.length > 1) {
            form.setValue("lastName", nameParts.slice(1).join(" "));
          }
        }
      }
    }

    // Check for stored form data (after authentication)
    const storedFormData = localStorage.getItem("trial_form_data");
    if (storedFormData) {
      try {
        const formData = JSON.parse(storedFormData);
        Object.entries(formData).forEach(([key, value]) => {
          if (typeof value === "string") {
            // @ts-expect-error - we know these fields exist in the form
            form.setValue(key, value);
          }
        });
      } catch (error) {
        console.error("Error parsing stored form data:", error);
      }
    }
  }, [userEmail, userName, form]);

  // Check if user already exists in Lago and has used the trial
  useEffect(() => {
    const checkExistingUser = async () => {
      if (!userId || !productSlug || !trialPlan?.plan_code) return;

      setIsCheckingUser(true);
      try {
        // Use the validation service to check trial eligibility
        const validationResult = await validateTrialEligibility({
          userId,
          productSlug,
          planCode: trialPlan.plan_code
        });

        console.log(`🔍 TRIAL FLOW - Trial eligibility check:`, validationResult);

        if (!validationResult.isValid) {
          setHasError(true);
          setHasAlreadyUsedTrial(true);

          // Set appropriate error message based on validation code
          switch (validationResult.code) {
            case "TRIAL_ALREADY_USED":
              setErrorMessage("You have already used the trial for this product.");
              break;
            case "HAS_PAID_PLAN":
              setErrorMessage("You already have a paid subscription for this product.");
              break;
            case "HAS_ACTIVE_TRIAL":
              setErrorMessage("You already have an active trial for this product.");
              break;
            default:
              setErrorMessage(validationResult.message);
          }

          // Give the user time to see the message before closing
          setTimeout(() => {
            onClose();
          }, 4000);
          return;
        }

        // Check if user exists in Lago to ensure we can create a subscription
        const user = await checkUserById(userId);
        if (!user) {
          console.log("⚠️ TRIAL FLOW - User not found in Lago, will be created during trial activation");
        } else {
          console.log("✅ TRIAL FLOW - User exists in Lago:", user.name || user.email || userId);
        }
      } catch (error) {
        console.error("❌ TRIAL FLOW - Error checking user:", error);
        setHasError(true);
        setErrorMessage("Error checking eligibility. Please try again later.");
      } finally {
        setIsCheckingUser(false);
      }
    };

    checkExistingUser();
  }, [userId, productSlug, trialPlan, onClose]);

  // Handle form completion
  const handleComplete = useCallback(() => {
    if (hasError) {
      // If there was an error, just close the dialog
      onClose();
      return;
    }

    // Stop the countdown if it's running
    if (redirectTimerRef.current) {
      clearTimeout(redirectTimerRef.current);
    }

    // Redirect to dashboard immediately
    router.push(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`);
    onClose();
  }, [hasError, onClose, router, redirectTimerRef]);

  // Start the redirect countdown when trial is successfully activated
  useEffect(() => {
    if (isSuccessful && redirectCounter > 0) {
      redirectTimerRef.current = setTimeout(() => {
        setRedirectCounter((prev) => prev - 1);
      }, 1000);
    } else if (isSuccessful && redirectCounter === 0) {
      handleComplete();
    }

    return () => {
      if (redirectTimerRef.current) {
        clearTimeout(redirectTimerRef.current);
      }
    };
  }, [isSuccessful, redirectCounter, handleComplete]);

  const onSubmit = async (data: UserFormValues) => {
    if (!trialPlan?.plan_code) {
      toast.error("Trial plan code is missing");
      setErrorMessage("Trial plan code is missing");
      setHasError(true);
      return false;
    }

    // If user is not authenticated, save form data and trigger sign in
    if (status !== "authenticated") {
      try {
        // Store form data in local storage to retrieve after auth
        localStorage.setItem("trial_form_data", JSON.stringify(data));
        localStorage.setItem("trial_product_slug", productSlug);
        localStorage.setItem("trial_plan_code", trialPlan.plan_code);

        // Redirect to sign in
        signIn("keycloak", { callbackUrl: `/products/${productSlug}?trial=true` });
      } catch (error) {
        console.error("Error storing form data:", error);
        toast.error("Failed to store form data. Please try again.");
        return false;
      }
      return false;
    }

    return true;
  };

  const activateTrial = async () => {
    if (!userId || !trialPlan?.plan_code) {
      toast.error("Missing user ID or trial plan code");
      setErrorMessage("Missing user ID or trial plan code");
      setHasError(true);
      return false;
    }

    setIsActivating(true);
    setHasError(false);

    try {
      // Step 3: Double-check subscription and trial status again before activation
      console.log(`🔄 TRIAL FLOW - Re-checking trial eligibility at Step 3 for user ${userId}`);

      // Use the validation service to check trial eligibility one more time before activation
      const validationResult = await validateTrialEligibility({
        userId,
        productSlug,
        planCode: trialPlan.plan_code
      });

      console.log(`🔍 TRIAL FLOW - Final trial eligibility check:`, validationResult);

      if (!validationResult.isValid) {
        setHasAlreadyUsedTrial(true);
        setErrorMessage(validationResult.message);
        setHasError(true);
        return false;
      }

      // Final check for the specific plan to ensure it's not pending
      const specificPlanCheck = await hasUserUsedTrial(userId, trialPlan.plan_code);
      if (specificPlanCheck.isPending) {
        setErrorMessage(
          `Your trial for this product is currently pending activation. Please wait a few minutes and try again.`
        );
        setHasAlreadyUsedTrial(true);
        setHasError(true);
        return false;
      }

      // Extract product code from the trial plan code
      const productCode = extractProductCode(trialPlan.plan_code);

      // Check if user has any active subscriptions for this product
      // This is a final safety check to prevent duplicate subscriptions
      const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&status[]=active&status[]=pending`;

      console.log(`🔍 TRIAL FLOW - Final check for any active subscriptions for product ${productCode}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const subscriptions = data.subscriptions || [];

        // Filter for subscriptions of this product
        const productSubscriptions = subscriptions.filter((sub: { plan_code: string }) => {
          const subProductCode = extractProductCode(sub.plan_code);
          return subProductCode === productCode;
        });

        if (productSubscriptions.length > 0) {
          console.log(`⚠️ TRIAL FLOW - Found ${productSubscriptions.length} active/pending subscriptions for product ${productCode}`);
          setErrorMessage(
            `You already have an active subscription for this product. Please check your dashboard.`
          );
          setHasAlreadyUsedTrial(true);
          setHasError(true);
          return false;
        }
      }

      // Create a cart item from trial plan but DON'T add it to the cart
      const trialCartItem = {
        id: trialPlan.plan_code,
        name: trialPlan.name,
        planCode: trialPlan.plan_code,
        price: 0,
        planDuration: "trial" as "trial" | "monthly" | "yearly",
        trialDays: trialPlan.trialDurationInDays || 14,
        quantity: 1,
        image: ""
      };

      // Create subscription directly without adding to cart
      await createSubscription(userId, trialCartItem);

      // Clear the cart to ensure no items remain from previous interactions
      if (clearCart) {
        clearCart();
      }

      // Show success message
      setIsSuccessful(true);
      toast.success(`Trial activated successfully! Enjoy your ${trialPlan.trialDurationInDays || 14}-day trial.`);

      // Clean up stored data
      localStorage.removeItem("trial_form_data");
      localStorage.removeItem("trial_product_slug");
      localStorage.removeItem("trial_plan_code");

      return true;
    } catch (error) {
      console.error("Error activating trial:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to activate trial. Please try again.");
      setHasError(true);
      toast.error("Failed to activate trial. Please try again.");
      return false;
    } finally {
      setIsActivating(false);
    }
  };



  // Handle manual redirect
  const handleManualRedirect = () => {
    // Stop the countdown if it's running
    if (redirectTimerRef.current) {
      clearTimeout(redirectTimerRef.current);
    }
    handleComplete();
  };

  // Define steps for the stepper
  const steps = [
    { label: "Account", description: "Your information" },
    { label: "Review", description: "Trial details" },
    { label: "Confirm", description: "Activate trial" },
  ];

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">
            {trialPlan?.button?.text || "Start Your Free Trial"}
          </DialogTitle>
          <DialogDescription>
            Try {trialPlan?.name || "our product"} with full access for {trialPlan?.trialDurationInDays || 14} days. No credit card required.
          </DialogDescription>
        </DialogHeader>

        {isCheckingUser ? (
          <div className="py-8 text-center">
            <Loader2 className="h-12 w-12 mx-auto animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">Checking your account...</p>
          </div>
        ) : hasAlreadyUsedTrial ? (
          <div className="py-8 text-center space-y-4">
            <XCircle className="h-16 w-16 mx-auto text-destructive" />
            <h3 className="text-xl font-semibold">Trial Already Used</h3>
            <p className="text-muted-foreground max-w-xs mx-auto">
              You have already used this trial. Please consider upgrading to a paid plan.
            </p>
            <Button
              className="mt-4"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        ) : (
          <StepperProvider totalSteps={3}>
            <Stepper>
              <StepperHeader steps={steps} className="mb-8" />

              {/* Step 1: User Information */}
              <Step step={0}>
                <Form {...form}>
                  <form className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your first name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your last name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} disabled={!!userEmail} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />


                  </form>
                </Form>

                <StepperActions
                  onNext={async () => {
                    const valid = await form.trigger();
                    if (!valid) return false;

                    return await onSubmit(form.getValues());
                  }}
                />
              </Step>

              {/* Step 2: Plan Review */}
              <Step step={1}>
                <div className="space-y-6">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-lg font-semibold">{trialPlan?.name || "Trial Plan"}</h3>
                            {trialPlan?.tag && (
                              <span className="inline-block mt-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                                {trialPlan.tag}
                              </span>
                            )}
                          </div>
                          <div className="text-right">
                            <span className="block text-lg font-bold">Free</span>
                            <span className="text-sm text-muted-foreground">
                              For {trialPlan?.trialDurationInDays || 14} days
                            </span>
                          </div>
                        </div>

                        <Separator />

                        <div>
                          <h4 className="font-medium mb-2">Description</h4>
                          <p className="text-sm text-muted-foreground">
                            {trialPlan?.description || "Access all premium features during your trial period. No credit card required."}
                          </p>
                        </div>

                        {trialPlan?.features && trialPlan.features.length > 0 && (
                          <>
                            <Separator />
                            <div>
                              <h4 className="font-medium mb-2">Features included:</h4>
                              <ul className="space-y-2">
                                {trialPlan.features.map((feature) => (
                                  feature.isIncluded && (
                                    <li key={feature.id} className="flex items-center text-sm">
                                      <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                                      <span>{feature.feature}</span>
                                    </li>
                                  )
                                ))}
                              </ul>
                            </div>
                          </>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-1">Your information</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Name:</span>{" "}
                        {form.getValues("firstName")} {form.getValues("lastName")}
                      </div>
                      <div>
                        <span className="text-muted-foreground">Email:</span> {form.getValues("email")}
                      </div>

                    </div>
                  </div>
                </div>

                <StepperActions
                  nextButtonLabel="Start Trial"
                  onNext={async () => {
                    return true;
                  }}
                />
              </Step>

              {/* Step 3: Confirmation */}
              <Step step={2}>
                <div className="text-center space-y-6 py-8">
                  {isActivating ? (
                    <>
                      <Loader2 className="h-16 w-16 mx-auto animate-spin text-primary" />
                      <h3 className="text-xl font-semibold">Activating Your Trial...</h3>
                      <p className="text-muted-foreground max-w-xs mx-auto">
                        Please wait while we set up your trial account. This will only take a moment.
                      </p>
                    </>
                  ) : hasError ? (
                    <>
                      <XCircle className="h-16 w-16 mx-auto text-destructive" />
                      <h3 className="text-xl font-semibold">Trial Activation Failed</h3>
                      <p className="text-muted-foreground max-w-xs mx-auto">
                        {errorMessage || "There was an error activating your trial. Please try again."}
                      </p>
                      <Button
                        className="mt-4"
                        onClick={onClose}
                        variant="outline"
                      >
                        Close
                      </Button>
                      <Button
                        className="mt-4 ml-4"
                        onClick={() => {
                          setHasError(false);
                          setErrorMessage("");
                        }}
                      >
                        Try Again
                      </Button>
                    </>
                  ) : isSuccessful ? (
                    <>
                      <div className="relative mb-8">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="h-32 w-32 rounded-full bg-green-100 dark:bg-green-900/30 animate-pulse" />
                        </div>
                        <CheckCircle className="h-16 w-16 mx-auto text-green-500 relative z-10" />
                      </div>
                      <h3 className="text-2xl font-bold">Trial Successfully Activated!</h3>
                      <p className="text-muted-foreground max-w-sm mx-auto">
                        Your {trialPlan?.trialDurationInDays || 14}-day trial of <span className="font-semibold text-foreground">{trialPlan?.name || "our product"}</span> is now active.
                      </p>
                      <div className="bg-muted/50 rounded-lg p-4 mt-6 max-w-sm mx-auto">
                        <h4 className="font-medium mb-2">What&apos;s included in your trial:</h4>
                        <ul className="text-sm text-left space-y-2">
                          <li className="flex items-start">
                            <CheckCircle className="h-4 w-4 mr-2 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>Full access to all premium features</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="h-4 w-4 mr-2 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>Priority customer support</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="h-4 w-4 mr-2 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>{trialPlan?.trialDurationInDays || 14} days of unrestricted access</span>
                          </li>
                        </ul>
                      </div>
                      <div className="mt-6 flex flex-col items-center">
                        <Button
                          className="px-8 py-6 text-base"
                          onClick={handleManualRedirect}
                          size="lg"
                        >
                          Go to Dashboard <ArrowRight className="ml-2 h-5 w-5" />
                        </Button>
                        <div className="mt-4 flex flex-col items-center">
                          <div className="relative w-40 h-2 bg-muted rounded-full overflow-hidden">
                            <div
                              className="absolute top-0 left-0 h-full bg-primary transition-all duration-1000 rounded-full"
                              style={{ width: `${(redirectCounter / 5) * 100}%` }}
                            />
                          </div>
                          <p className="text-sm text-muted-foreground mt-2 flex items-center">
                            <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                            Redirecting to dashboard in {redirectCounter} seconds
                          </p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <h3 className="text-xl font-semibold">Ready to Start Your Trial?</h3>
                      <p className="text-muted-foreground max-w-xs mx-auto">
                        Click the button below to activate your {trialPlan?.trialDurationInDays || 14}-day free trial.
                      </p>
                      <StepperActions
                        submitButtonLabel="Activate Trial"
                        onNext={activateTrial}
                        onComplete={handleComplete}
                        hideBackOnFirst={false}
                      />
                    </>
                  )}
                </div>
              </Step>
            </Stepper>
          </StepperProvider>
        )}
      </DialogContent>
    </Dialog>
  );
}