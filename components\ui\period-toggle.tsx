"use client"

import * as React from "react"
import { Switch } from "@/components/ui/switch"

interface PeriodToggleProps extends React.HTMLAttributes<HTMLDivElement> {
  onPeriodChange?: (period: "monthly" | "yearly") => void;
  checked?: boolean;
}

export function PeriodToggle({ onPeriodChange, checked = false, ...props }: PeriodToggleProps) {
  const [enabled, setEnabled] = React.useState(checked);

  // Sync with external checked prop when it changes
  React.useEffect(() => {
    setEnabled(checked);
  }, [checked]);

  const handleToggle = (value: boolean) => {
    setEnabled(value);
    if (onPeriodChange) {
      onPeriodChange(value ? "yearly" : "monthly");
    }
  };

  return (
    <div {...props}>
      <Switch checked={enabled} onCheckedChange={handleToggle} />
    </div>
  );
}
