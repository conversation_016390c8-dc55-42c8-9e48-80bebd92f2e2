"use client";

import React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";

// Section Navigation Item component for product pages
export const SectionNavItem = ({
  label,
  sectionId,
  onSectionClick,
}: {
  label: string;
  sectionId: string;
  onSectionClick: (sectionId: string) => void;
}) => {
  return (
    <div
      className="cursor-pointer px-3 py-1 mx-1 hover:text-red-500 transition-colors"
      onClick={() => onSectionClick(sectionId)}
    >
      {label}
    </div>
  );
};

// Product Navigation Menu wrapper
export const ProductNavMenu = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("flex items-center space-x-1 bg-transparent", className)}>
      {children}
    </div>
  );
};

// Mobile menu item for product pages
export const ProductMobileMenuItem = ({
  label,
  sectionId,
  onClick,
}: {
  label: string;
  sectionId: string;
  onClick: (sectionId: string) => void;
}) => {
  return (
    <button
      onClick={() => onClick(sectionId)}
      className="w-full text-left px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-md transition-colors text-base"
    >
      {label}
    </button>
  );
};

// Standard Link Item for the product page (non-section links)
export const ProductNavLink = ({
  href,
  label,
}: {
  href: string;
  label: string;
}) => {
  return (
    <Link
      href={href}
      className="px-3 py-1 mx-1 hover:text-red-500 transition-colors"
    >
      {label}
    </Link>
  );
}; 