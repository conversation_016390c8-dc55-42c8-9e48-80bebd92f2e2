"use client";

import { useLoading } from "@/context/loading-context";
import { LottieLoader } from "./lottie-loader";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

export function PageLoader() {
  const { isLoading } = useLoading();
  const [shouldRender, setShouldRender] = useState(false);
  const [initialLoad, setInitialLoad] = useState(true);
  
  // Handle removal of initial loader
  useEffect(() => {
    if (initialLoad && typeof window !== "undefined") {
      // Mark initial load as complete
      setInitialLoad(false);
      
      // Remove initial loader script when React loader is ready
      if (window.removeInitialLoader) {
        window.removeInitialLoader();
      }
    }
  }, [initialLoad]);
  
  // Respond to loading state changes
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    if (isLoading) {
      // Show loader immediately for better UX 
      setShouldRender(true);
    } else {
      // Small delay before hiding to ensure smooth transition
      timeout = setTimeout(() => {
        setShouldRender(false);
      }, 200);
    }
    
    return () => {
      clearTimeout(timeout);
    };
  }, [isLoading]);
  
  return (
    <AnimatePresence>
      {shouldRender && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }} // Faster transition
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-background/85 backdrop-blur-sm"
        >
          <div className="flex flex-col items-center justify-center gap-4">
            <LottieLoader size="lg" />
            <div className="text-center">
              <h3 className="text-lg font-medium text-foreground">Loading...</h3>
              <p className="text-sm text-muted-foreground">Please wait while we prepare your content</p>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Add type definition for global window object
declare global {
  interface Window {
    removeInitialLoader?: () => void;
  }
}

export default PageLoader; 