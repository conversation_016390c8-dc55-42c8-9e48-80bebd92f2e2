"use client"

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { Flip } from 'gsap/Flip'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(Flip)
}

interface GSAPPageTransitionProps {
  children: React.ReactNode
  className?: string
}

export const GSAPPageTransition = ({ children, className = "" }: GSAPPageTransitionProps) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (typeof window === 'undefined' || !containerRef.current) return

    // Page entrance animation
    const tl = gsap.timeline()
    
    // Set initial state
    gsap.set(containerRef.current, {
      opacity: 0,
      y: 20,
      scale: 0.98
    })

    // Animate in
    tl.to(containerRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "power2.out"
    })

    return () => {
      tl.kill()
    }
  }, [])

  return (
    <div 
      ref={containerRef}
      className={`w-full ${className}`}
    >
      {children}
    </div>
  )
}

// Hook for programmatic page transitions
export const usePageTransition = () => {
  const createExitTransition = (
    element: Element,
    onComplete: () => void,
    direction: 'up' | 'down' | 'left' | 'right' = 'up'
  ) => {
    if (typeof window === 'undefined') return null

    const directionMap = {
      up: { y: -50, x: 0 },
      down: { y: 50, x: 0 },
      left: { y: 0, x: -50 },
      right: { y: 0, x: 50 }
    }

    const { x, y } = directionMap[direction]

    return gsap.to(element, {
      opacity: 0,
      y,
      x,
      scale: 0.95,
      duration: 0.6,
      ease: "power2.in",
      onComplete
    })
  }

  const createEntranceTransition = (element: Element) => {
    if (typeof window === 'undefined') return null

    gsap.set(element, {
      opacity: 0,
      y: 30,
      scale: 0.95
    })

    return gsap.to(element, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "power2.out"
    })
  }

  return {
    createExitTransition,
    createEntranceTransition
  }
}

export default GSAPPageTransition
