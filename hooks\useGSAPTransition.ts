"use client"

import { useRef, useCallback } from 'react'
import { gsap } from 'gsap'
import { Flip } from 'gsap/Flip'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(Flip)
}

interface TransitionOptions {
  duration?: number
  ease?: string
  scale?: boolean
  onComplete?: () => void
}

export const useGSAPTransition = () => {
  const stateRef = useRef<any>(null)

  const captureState = useCallback((targets: string | Element | Element[]) => {
    if (typeof window === 'undefined') return null
    
    try {
      stateRef.current = Flip.getState(targets, {
        props: "backgroundColor,borderRadius,opacity"
      })
      return stateRef.current
    } catch (error) {
      console.warn('GSAP Flip state capture failed:', error)
      return null
    }
  }, [])

  const animateToState = useCallback((options: TransitionOptions = {}) => {
    if (typeof window === 'undefined' || !stateRef.current) return null

    const {
      duration = 0.8,
      ease = "power2.inOut",
      scale = false,
      onComplete
    } = options

    try {
      return Flip.from(stateRef.current, {
        duration,
        ease,
        scale,
        absolute: true,
        onComplete: () => {
          stateRef.current = null
          onComplete?.()
        }
      })
    } catch (error) {
      console.warn('GSAP Flip animation failed:', error)
      onComplete?.()
      return null
    }
  }, [])

  const createCardToPageTransition = useCallback((
    cardElement: Element,
    onNavigate: () => void,
    options: TransitionOptions = {}
  ) => {
    if (typeof window === 'undefined') return

    const {
      duration = 1.2,
      ease = "power2.inOut",
      onComplete
    } = options

    // Capture initial state
    const state = Flip.getState(cardElement, {
      props: "backgroundColor,borderRadius,opacity,transform"
    })

    // Add a temporary overlay to create smooth transition effect
    const overlay = document.createElement('div')
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0);
      z-index: 9999;
      pointer-events: none;
    `
    document.body.appendChild(overlay)

    // Create timeline for complex animation
    const tl = gsap.timeline({
      onComplete: () => {
        document.body.removeChild(overlay)
        onComplete?.()
      }
    })

    // Scale and fade the card
    tl.to(cardElement, {
      scale: 1.05,
      opacity: 0.8,
      duration: 0.3,
      ease: "power2.out"
    })
    
    // Fade overlay in
    .to(overlay, {
      backgroundColor: "rgba(0, 0, 0, 0.3)",
      duration: 0.2
    }, "-=0.1")
    
    // Navigate and complete transition
    .call(() => {
      onNavigate()
    })
    
    // Fade out overlay after navigation
    .to(overlay, {
      backgroundColor: "rgba(0, 0, 0, 0)",
      duration: 0.4,
      ease: "power2.inOut"
    }, "+=0.1")

    return tl
  }, [])

  const createButtonClickAnimation = useCallback((
    buttonElement: Element,
    options: TransitionOptions = {}
  ) => {
    if (typeof window === 'undefined') return null

    const {
      duration = 0.6,
      ease = "back.out(1.7)"
    } = options

    return gsap.timeline()
      .to(buttonElement, {
        scale: 0.95,
        duration: 0.1,
        ease: "power2.out"
      })
      .to(buttonElement, {
        scale: 1.02,
        duration: duration,
        ease: ease
      })
      .to(buttonElement, {
        scale: 1,
        duration: 0.2,
        ease: "power2.inOut"
      })
  }, [])

  return {
    captureState,
    animateToState,
    createCardToPageTransition,
    createButtonClickAnimation
  }
}
