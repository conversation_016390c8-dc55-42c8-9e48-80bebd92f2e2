"use client"

import { useRef, useCallback } from 'react'
import { gsap } from 'gsap'
import { Flip } from 'gsap/Flip'

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(Flip)
}

interface PreloadedPageData {
  url: string
  content: string
  isLoaded: boolean
}

interface TransitionOptions {
  duration?: number
  ease?: string
  scale?: boolean
  onComplete?: () => void
}

export const useGSAPTransition = () => {
  const stateRef = useRef<any>(null)
  const preloadCacheRef = useRef<Map<string, PreloadedPageData>>(new Map())

  // Preload page content for smooth transitions
  const preloadPage = useCallback(async (url: string): Promise<boolean> => {
    if (typeof window === 'undefined') return false

    try {
      // Check if already cached
      if (preloadCacheRef.current.has(url)) {
        return preloadCacheRef.current.get(url)?.isLoaded || false
      }

      // Create a hidden iframe to preload the page
      const iframe = document.createElement('iframe')
      iframe.style.cssText = `
        position: absolute;
        top: -9999px;
        left: -9999px;
        width: 1px;
        height: 1px;
        opacity: 0;
        pointer-events: none;
      `

      return new Promise((resolve) => {
        iframe.onload = () => {
          preloadCacheRef.current.set(url, {
            url,
            content: '',
            isLoaded: true
          })
          document.body.removeChild(iframe)
          resolve(true)
        }

        iframe.onerror = () => {
          document.body.removeChild(iframe)
          resolve(false)
        }

        // Set timeout to prevent hanging
        setTimeout(() => {
          if (document.body.contains(iframe)) {
            document.body.removeChild(iframe)
          }
          resolve(false)
        }, 5000)

        document.body.appendChild(iframe)
        iframe.src = url
      })
    } catch (error) {
      console.warn('Page preload failed:', error)
      return false
    }
  }, [])

  const captureState = useCallback((targets: string | Element | Element[]) => {
    if (typeof window === 'undefined') return null

    try {
      stateRef.current = Flip.getState(targets, {
        props: "backgroundColor,borderRadius,opacity,transform"
      })
      return stateRef.current
    } catch (error) {
      console.warn('GSAP Flip state capture failed:', error)
      return null
    }
  }, [])

  const animateToState = useCallback((options: TransitionOptions = {}) => {
    if (typeof window === 'undefined' || !stateRef.current) return null

    const {
      duration = 0.8,
      ease = "power2.inOut",
      scale = false,
      onComplete
    } = options

    try {
      return Flip.from(stateRef.current, {
        duration,
        ease,
        scale,
        absolute: true,
        onComplete: () => {
          stateRef.current = null
          onComplete?.()
        }
      })
    } catch (error) {
      console.warn('GSAP Flip animation failed:', error)
      onComplete?.()
      return null
    }
  }, [])

  const createCinematicZoomTransition = useCallback(async (
    cardElement: Element,
    destinationUrl: string,
    onNavigate: () => void,
    options: TransitionOptions & {
      productImage?: string,
      productName?: string,
      alignmentSelector?: string
    } = {}
  ) => {
    if (typeof window === 'undefined') return null

    const {
      duration = 1.4,
      ease = "power2.inOut",
      onComplete,
      productImage,
      productName,
      alignmentSelector = '.hero-section'
    } = options

    // Preload the destination page
    const isPreloaded = await preloadPage(destinationUrl)
    if (!isPreloaded) {
      console.warn('Page preload failed, falling back to simple transition')
      return createCardToPageTransition(cardElement, onNavigate, options)
    }

    // Get card's current position and size
    const cardRect = cardElement.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // Create cinematic overlay container
    const cinematicContainer = document.createElement('div')
    cinematicContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 10000;
      pointer-events: none;
      overflow: hidden;
    `

    // Create background overlay
    const backgroundOverlay = document.createElement('div')
    backgroundOverlay.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0);
      transition: background 0.3s ease;
    `

    // Create page preview container
    const pagePreview = document.createElement('div')
    pagePreview.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      width: ${viewportWidth}px;
      height: ${viewportHeight}px;
      transform: translate(-50%, -50%) scale(0.1);
      transform-origin: center center;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      opacity: 0;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    `

    // Create card clone for zoom effect
    const cardClone = cardElement.cloneNode(true) as Element
    cardClone.setAttribute('style', `
      position: absolute;
      top: ${cardRect.top}px;
      left: ${cardRect.left}px;
      width: ${cardRect.width}px;
      height: ${cardRect.height}px;
      transform-origin: center center;
      z-index: 10001;
      pointer-events: none;
    `)

    // Add elements to DOM
    cinematicContainer.appendChild(backgroundOverlay)
    cinematicContainer.appendChild(pagePreview)
    document.body.appendChild(cinematicContainer)
    document.body.appendChild(cardClone)

    // Hide original card
    gsap.set(cardElement, { opacity: 0 })

    // Create master timeline
    const masterTimeline = gsap.timeline({
      onComplete: () => {
        // Cleanup
        document.body.removeChild(cinematicContainer)
        document.body.removeChild(cardClone)
        gsap.set(cardElement, { opacity: 1 })
        onComplete?.()
      }
    })

    // Phase 1: Card zoom in (0.8s)
    masterTimeline
      .to(backgroundOverlay, {
        background: "rgba(0, 0, 0, 0.8)",
        duration: 0.3,
        ease: "power2.out"
      })
      .to(cardClone, {
        scale: 3.5,
        x: viewportWidth / 2 - cardRect.left - cardRect.width / 2,
        y: viewportHeight / 2 - cardRect.top - cardRect.height / 2,
        duration: 0.8,
        ease: "power2.inOut"
      }, "-=0.2")

    // Phase 2: Page emergence (starts at 0.4s, duration 0.6s)
    masterTimeline
      .to(pagePreview, {
        opacity: 1,
        scale: 0.3,
        duration: 0.3,
        ease: "power2.out"
      }, "-=0.4")
      .to(pagePreview, {
        scale: 1,
        duration: 0.6,
        ease: "power2.inOut"
      }, "-=0.1")

    // Phase 3: Card fade out as page reaches full size
    masterTimeline
      .to(cardClone, {
        opacity: 0,
        scale: 4,
        duration: 0.4,
        ease: "power2.in"
      }, "-=0.3")

    // Phase 4: Navigate and complete
    masterTimeline
      .call(() => {
        // Set flag for cinematic transition
        sessionStorage.setItem('cinematicTransition', 'true')
        onNavigate()
      }, [], "-=0.2")
      .to(backgroundOverlay, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.inOut"
      }, "-=0.1")

    return masterTimeline
  }, [preloadPage])

  const createCardToPageTransition = useCallback((
    cardElement: Element,
    onNavigate: () => void,
    options: TransitionOptions = {}
  ) => {
    if (typeof window === 'undefined') return

    const {
      duration = 1.2,
      ease = "power2.inOut",
      onComplete
    } = options

    // Capture initial state
    const state = Flip.getState(cardElement, {
      props: "backgroundColor,borderRadius,opacity,transform"
    })

    // Add a temporary overlay to create smooth transition effect
    const overlay = document.createElement('div')
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0);
      z-index: 9999;
      pointer-events: none;
    `
    document.body.appendChild(overlay)

    // Create timeline for complex animation
    const tl = gsap.timeline({
      onComplete: () => {
        document.body.removeChild(overlay)
        onComplete?.()
      }
    })

    // Scale and fade the card
    tl.to(cardElement, {
      scale: 1.05,
      opacity: 0.8,
      duration: 0.3,
      ease: "power2.out"
    })

    // Fade overlay in
    .to(overlay, {
      backgroundColor: "rgba(0, 0, 0, 0.3)",
      duration: 0.2
    }, "-=0.1")

    // Navigate and complete transition
    .call(() => {
      onNavigate()
    })

    // Fade out overlay after navigation
    .to(overlay, {
      backgroundColor: "rgba(0, 0, 0, 0)",
      duration: 0.4,
      ease: "power2.inOut"
    }, "+=0.1")

    return tl
  }, [])

  const createButtonClickAnimation = useCallback((
    buttonElement: Element,
    options: TransitionOptions = {}
  ) => {
    if (typeof window === 'undefined') return null

    const {
      duration = 0.6,
      ease = "back.out(1.7)"
    } = options

    return gsap.timeline()
      .to(buttonElement, {
        scale: 0.95,
        duration: 0.1,
        ease: "power2.out"
      })
      .to(buttonElement, {
        scale: 1.02,
        duration: duration,
        ease: ease
      })
      .to(buttonElement, {
        scale: 1,
        duration: 0.2,
        ease: "power2.inOut"
      })
  }, [])

  return {
    captureState,
    animateToState,
    createCardToPageTransition,
    createCinematicZoomTransition,
    createButtonClickAnimation,
    preloadPage
  }
}
