"use client";

import { useEffect, useState, useRef, useCallback } from "react";
import { useCart } from "@/context/cart-context";
import { createSubscription } from "@/src/services/subscriptionService";
import { useUser } from "@/context/user-context";
import { getOrCreateUser } from "@/src/services/userService";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import {
  getInvoiceById,
  getLatestInvoiceByCustomerId,
  updateInvoicePaymentStatus
} from "@/src/services/lago/invoiceService";
import { SubscriptionResponse } from "@/src/services/lago/subscriptionService";
import { useRouter } from "next/navigation";
import { AutoPaymentStatusUpdater } from "@/components/payment/auto-payment-status-updater";

// Define a type for NextAuth user with Keycloak integration
interface NextAuthKeycloakUser {
  id: string;  // This is the sub from Keycloak
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

interface PaymentSuccessHandlerProps {
  isSuccess: boolean;
  paymentId?: string; // Payment ID for tracking processed payments
}

export function PaymentSuccessHandler({ isSuccess, paymentId }: PaymentSuccessHandlerProps) {
  const { items } = useCart();
  const { userId, userEmail, isLoading, isKeycloakUser } = useUser();
  const { data: session } = useSession();
  const [hasProcessed, setHasProcessed] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentProcessed, setPaymentProcessed] = useState(false);
  const isFirstRender = useRef(true);
  const router = useRouter();

  // Logger for all steps of the payment process
  const logPaymentStep = (step: string, data?: unknown) => {
    console.log(`🔄 PAYMENT PROCESS [${new Date().toISOString()}] - ${step}`, data || '');
  };

  // On mount, check if this payment has already been processed
  useEffect(() => {
    if (paymentId) {
      const processedPaymentsJson = localStorage.getItem('processed_payments');
      if (processedPaymentsJson) {
        try {
          const processedPayments = JSON.parse(processedPaymentsJson);
          if (Array.isArray(processedPayments) && processedPayments.includes(paymentId)) {
            logPaymentStep(`Payment ${paymentId} has already been processed, skipping`);
            setHasProcessed(true);
            setPaymentProcessed(true);
            return;
          }
        } catch (error) {
          console.error('❌ PAYMENT HANDLER - Error parsing processed payments:', error);
        }
      }

      // Check if payment success timestamp is set
      const paymentSuccessTimestamp = localStorage.getItem('payment_success_timestamp');
      const paymentStatus = localStorage.getItem('payment_status');

      if (paymentSuccessTimestamp && paymentStatus === 'succeeded') {
        logPaymentStep(`Payment success detected from localStorage for ${paymentId}`);
        // This will trigger the main effect to process the payment
        if (!isSuccess) {
          logPaymentStep(`Setting isSuccess to true based on localStorage values`);
          // We can't directly modify the isSuccess prop, but we can set other state
          // that will trigger the payment processing
          setIsProcessing(false);
          setHasProcessed(false);
        }
      }
    }
  }, [paymentId, isSuccess]);

  // Helper to extract the Keycloak ID from available sources
  const extractKeycloakId = useCallback((): string | null => {
    // Priority 1: NextAuth session
    if (session?.user) {
      const user = session.user as unknown as NextAuthKeycloakUser;
      if (user.id) {
        return user.id;
      }
    }

    // Priority 2: User context if it's confirmed Keycloak
    if (userId && isKeycloakUser) {
      return userId;
    }

    // Priority 3: Local storage if marked as Keycloak
    if (localStorage.getItem('is_keycloak_user') === 'true') {
      const storedId = localStorage.getItem('user_id');
      if (storedId) {
        return storedId;
      }
    }

    // Fallback: Try to extract from token
    try {
      const token = localStorage.getItem('keycloak_token') ||
                  localStorage.getItem('token') ||
                  sessionStorage.getItem('keycloak_token') ||
                  sessionStorage.getItem('token');

      if (token) {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          if (payload.sub) {
            return payload.sub;
          }
        }
      }
    } catch (error) {
      console.error("❌ PAYMENT HANDLER - Error extracting ID from token:", error);
    }

    return null;
  }, [session, userId, isKeycloakUser]);

  // Helper to record a processed payment
  const recordProcessedPayment = useCallback(() => {
    if (paymentId) {
      try {
        const processedPayments = JSON.parse(localStorage.getItem('processed_payments') || '[]');
        if (!processedPayments.includes(paymentId)) {
          processedPayments.push(paymentId);
          localStorage.setItem('processed_payments', JSON.stringify(processedPayments));
          logPaymentStep(`Marked payment ${paymentId} as processed`);
        }
      } catch (error) {
        console.error("❌ PAYMENT HANDLER - Error recording processed payment:", error);
      }
    }
  }, [paymentId]);

  // Process the payment and create subscription
  const processPayment = useCallback(async () => {
    if (isProcessing || hasProcessed) {
      return;
    }

    // Mark as processing to prevent duplicate processing
    setIsProcessing(true);

    logPaymentStep('Starting payment processing');

    // Get the Keycloak ID for the user
    const keycloakId = extractKeycloakId();

    if (!keycloakId) {
      console.error("❌ PAYMENT HANDLER - No valid Keycloak ID found");
      toast.error("User identification failed. Please contact support.");
      setIsProcessing(false);
      return;
    }

    logPaymentStep('Using Keycloak ID', keycloakId);

    try {
      // Step 1: Ensure user exists in Lago
      logPaymentStep('Step 1: Ensuring user exists in Lago');
      const userPayload = {
        external_id: keycloakId,
        email: userEmail || undefined,
        name: localStorage.getItem('user_name') || undefined
      };

      const userResult = await getOrCreateUser(userPayload);

      if (!userResult) {
        console.error("❌ PAYMENT HANDLER - Failed to create or verify user in Lago");
        toast.error("User account creation failed. Please contact support.");
        setIsProcessing(false);
        return;
      }

      logPaymentStep('User verified/created in Lago successfully');

      // Step 2: Create subscriptions for all items
      logPaymentStep('Step 2: Creating subscriptions');

      let successCount = 0;
      let latestSubscriptionResult: SubscriptionResponse | null = null;

      for (const item of items) {
        if (!item.planCode) {
          console.error(`❌ PAYMENT HANDLER - Item ${item.name} has no plan code!`);
          toast.error(`Missing plan code for ${item.name}. Please contact support.`);
          continue;
        }

        logPaymentStep(`Creating subscription for ${item.name}`, {
          planCode: item.planCode,
          keycloakId
        });

        try {
          const subscriptionResult = await createSubscription(keycloakId, item);

          if (subscriptionResult && subscriptionResult.subscription) {
            logPaymentStep(`Subscription created successfully`, {
              item: item.name,
              planCode: item.planCode,
              subscriptionId: subscriptionResult.subscription.id
            });

            successCount++;
            latestSubscriptionResult = subscriptionResult;
          } else {
            console.error(`❌ PAYMENT HANDLER - Failed to create subscription for ${item.name}`);
            toast.error(`Failed to create subscription for ${item.name}. Please contact support.`);
          }
        } catch (error) {
          console.error(`❌ PAYMENT HANDLER - Error creating subscription:`, error);
          toast.error(`Error with subscription for ${item.name}. Please contact support.`);
        }
      }

      if (successCount === 0) {
        console.error("❌ PAYMENT HANDLER - All subscriptions failed");
        toast.error("Failed to create subscriptions. Please contact support.");
        setIsProcessing(false);
        return;
      }

      // Step 3: Wait for invoice generation
      logPaymentStep('Step 3: Waiting for invoice generation');

      // Add a delay to allow Lago time to generate the invoice
      const waitTimeMs = 5000; // 5 seconds
      logPaymentStep(`Waiting ${waitTimeMs}ms for Lago to generate invoice...`);
      await new Promise(resolve => setTimeout(resolve, waitTimeMs));

      // Step 4: Get the latest invoice for this customer
      logPaymentStep('Step 4: Getting latest invoice');

      if (!latestSubscriptionResult?.subscription?.created_at) {
        console.error("❌ PAYMENT HANDLER - No creation date found for subscription");
        toast.error("Subscription data incomplete. Please contact support.");
        setIsProcessing(false);
        return;
      }

      const createdAt = latestSubscriptionResult.subscription.created_at;
      const formattedDate = new Date(createdAt).toISOString().split('T')[0];

      let invoice = await getLatestInvoiceByCustomerId(keycloakId, formattedDate);

      if (!invoice) {
        // Try one more time after a delay
        logPaymentStep('No invoice found, waiting and retrying...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        invoice = await getLatestInvoiceByCustomerId(keycloakId, formattedDate);
      }

      if (!invoice) {
        console.error("❌ PAYMENT HANDLER - No invoices found for the customer");
        toast.error("Subscription created but invoice not found. Please contact support.");

        // Even though we couldn't find the invoice, the subscription was created
        // Record this payment as processed to prevent duplicate processing
        recordProcessedPayment();
        setIsProcessing(false);
        setHasProcessed(true);
        setPaymentProcessed(true);
        return;
      }

      logPaymentStep('Found invoice to update', {
        invoiceId: invoice.id,
        lagoId: invoice.lago_id,
        currentStatus: invoice.payment_status
      });

      // Step 5: Update invoice payment status
      logPaymentStep('Step 5: Updating invoice payment status');

      const updateResult = await updateInvoicePaymentStatus(invoice.lago_id);

      if (updateResult) {
        logPaymentStep('Successfully updated invoice payment status to "succeeded"');
      } else {
        console.error("❌ PAYMENT HANDLER - Failed to update invoice payment status");
        toast.error("Payment recording failed. Please contact support.");
      }

      // Step 6: Verify the update was successful
      logPaymentStep('Step 6: Verifying payment status update');

      const verifiedInvoice = await getInvoiceById(invoice.lago_id);

      if (verifiedInvoice && verifiedInvoice.payment_status === 'succeeded') {
        logPaymentStep('Verified invoice payment status is "succeeded"');
        toast.success("Payment completed successfully!");
      } else {
        logPaymentStep('Invoice payment status verification failed', {
          status: verifiedInvoice?.payment_status || 'unknown'
        });
      }

      // Record this payment as processed
      recordProcessedPayment();

      // All steps completed
      logPaymentStep('Payment processing completed successfully');
      setIsProcessing(false);
      setHasProcessed(true);
      setPaymentProcessed(true);

    } catch (error) {
      console.error("❌ PAYMENT HANDLER - Error processing payment:", error);
      toast.error("Error processing payment. Please contact support.");
      setIsProcessing(false);
    }
  }, [isProcessing, hasProcessed, userEmail, items, extractKeycloakId, recordProcessedPayment]);

  // Main effect to process the payment
  useEffect(() => {
    // Skip the first render to prevent processing on mount
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Check for payment success in localStorage
    const paymentSuccessTimestamp = localStorage.getItem('payment_success_timestamp');
    const paymentStatus = localStorage.getItem('payment_status');
    const paymentSuccessInLocalStorage = paymentSuccessTimestamp && paymentStatus === 'succeeded';

    // Only process if all conditions are met
    if ((isSuccess || paymentSuccessInLocalStorage) && !hasProcessed && !isProcessing && !isLoading && userId && items.length > 0) {
      logPaymentStep(`Payment success detected, processing payment ${paymentId || 'unknown'}`);
      processPayment();
    }
  }, [isSuccess, hasProcessed, isProcessing, items, userId, isLoading, paymentId, processPayment]);

  // Effect to navigate to success page after payment is processed
  useEffect(() => {
    if (paymentProcessed) {
      logPaymentStep('Payment fully processed, redirecting to success page');
      setTimeout(() => {
        router.push('/payment/success');
      }, 1000);
    }
  }, [paymentProcessed, router]);

  // Store payment ID for later use
  useEffect(() => {
    if (paymentId) {
      try {
        localStorage.setItem('last_payment_id', paymentId);
      } catch (error) {
        console.error('Error storing payment ID:', error);
      }
    }
  }, [paymentId]);

  // Return the AutoPaymentStatusUpdater component to handle pending invoices
  return (
    <>
      {paymentProcessed && userId && (
        <AutoPaymentStatusUpdater
          dateFrom={undefined}
          enabled={true}
          paymentId={paymentId}
        />
      )}
    </>
  );
}
