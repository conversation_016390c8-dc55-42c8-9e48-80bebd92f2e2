import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_SERVICES_CONTENT_BY_SLUG = gql`
  query ServicesBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      sections {
        ... on ComponentSectionsServicesSection {
          id
          title
          description
          servicesCard {
            tag
            title
            decription
            id
          }
        }
      }
    }
  }
`;

export const getServicesContentBySlug = async (slug: string) => {
  try {
    const { data } = await client.query({
      query: GET_SERVICES_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    const servicesSection = data.products[0]?.sections.find(
      (section: { __typename: string }) => section.__typename === "ComponentSectionsServicesSection"
    );
    return servicesSection || null;
  } catch (error) {
    console.error("Error fetching services section data:", error);
    throw error;
  }
};
