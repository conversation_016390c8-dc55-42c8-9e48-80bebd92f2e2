# Architecture: Onebiz SaaS Platform

## 1. Overview

This document outlines the architecture of the Onebiz SaaS Platform frontend. It is a Next.js 14 application built with TypeScript, utilizing the App Router paradigm for routing and React Server Components (RSC). The platform focuses on a modern user experience with Shadcn UI and Tailwind CSS, robust data fetching strategies, client-side cart management, and integrations with several backend microservices for authentication, billing, payments, and email notifications. Error monitoring is handled via Sentry.

## 2. Tech Stack Summary

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **UI Library**: Shadcn UI (Radix UI primitives)
- **Styling**: Tailwind CSS (with CSS Variables from `app/globals.css` for theming)
- **State Management (Client)**:
    - React Context API (`UserProvider`, `CartProvider`)
    - `localStorage` for cart persistence and temporary state (e.g., pending trial/subscription info)
- **State Management / Data Fetching (Server State & API Interaction)**:
    - `@tanstack/react-query` (Primary for managing server state from RESTful or GraphQL APIs)
    - Apollo Client / GraphQL (For GraphQL API interactions)
    - Axios (For general RESTful API calls)
    - Direct `fetch` calls (especially in server components or client-side services)
- **Forms**: React Hook Form with Zod for validation
- **Authentication**: NextAuth.js with Keycloak provider
- **Billing & Subscription Management**: Lago (via direct client-side API calls and backend proxies)
- **Payment Gateway**: "onepay" microservice (at `onepay.cubeone.in`), likely an aggregator using Razorpay.
- **Email Service**: Integrated with "onepay" (endpoint: `onepay.cubeone.in/api/send-subscription-email`) and also triggered via Lago invoice updates.
- **Error Tracking**: Sentry
- **Linting**: ESLint (`next/core-web-vitals`, `next/typescript`)
- **Key Path Aliases**: `@/*` (project root), `@/components`, `@/components/ui`, `@/lib`, `@/lib/utils`, `@/hooks`
- **Deployment & CI/CD**: Docker (`Dockerfile`), GitLab CI (`.gitlab-ci.yml`), PM2 (`ecosystem.config.json`).

## 3. Frontend Architecture

### 3.1. Core Framework: Next.js App Router
- Utilizes RSCs by default; Client Components (`'use client'`) for interactivity.
- Directory structure within `app/` defines routes.
- Middleware (`middleware.ts`) handles product slug redirects and cookie-based cart clearing logic post-payment. Authentication enforcement in middleware is currently a placeholder.

### 3.2. Component Strategy
- Shadcn UI components (`@/components/ui`) for UI primitives.
- Custom application components in `@/components`.
- `class-variance-authority` (CVA), `clsx`, `tailwind-merge` for styling utilities.

### 3.3. Styling and Theming
- Tailwind CSS utility-first. Configuration in `tailwind.config.ts`.
- CSS Variables in `app/globals.css` for theming (light/dark modes).
- `next-themes` for theme switching.

### 3.4. Client-Side State Management
- **User Context (`UserProvider`)**:
    - Manages user session data obtained from NextAuth.js.
    - On login, extracts Keycloak ID and profile; calls `getOrCreateUser` (`userService.ts`) to synchronize with Lago.
    - Caches some user details (ID, email, name) in `localStorage`.
- **Cart Context (`CartProvider`)**:
    - Manages shopping cart state entirely client-side.
    - Persists cart items in `localStorage`.
    - Handles adding/removing items, updating quantities.
    - Calculates subtotal, a fixed 18% tax (CGST + IGST), and total.
    - Aware of subscription items (requires `planCode`).
- **Temporary State with `localStorage`**:
    - Used by `subscriptionCheckService.ts` to store `pending_subscription_plan` details if a non-authenticated user selects a plan before login.
    - Used by `trial-flow.tsx` to store `trial_form_data` before redirecting an unauthenticated user to login for trial activation.

### 3.5. Custom Hooks
- `hooks/useSubscriptionCheck.ts`, `hooks/usePostLoginSubscription.ts`: Utilize services to check user's current subscription status.
- Other custom hooks in `hooks/` for reusable logic.

## 4. Backend Integration & Microservice APIs

This frontend interacts with several key external and internal services:

### 4.1. Keycloak (Authentication Service)
- **Provider**: Integrated via `next-auth/providers/Keycloak`.
- **Endpoints Used**: Standard OpenID Connect endpoints (authorization, token, userinfo, logout, token revocation) configured via `KEYCLOAK_ISSUER`.
- **Flow**:
    1. User initiates login, redirected to Keycloak.
    2. User authenticates with Keycloak.
    3. Keycloak redirects back to NextAuth.js callback handler (`/api/auth/callback/keycloak`).
    4. NextAuth.js exchanges authorization code for tokens, fetches user profile.
    5. Keycloak User ID (`sub`) is stored in the JWT and session, serving as the primary user identifier across services.
    6. Token refresh is handled by NextAuth.js.
- **Session Management**: Managed by NextAuth.js (JWT strategy). Client uses `useSession()`. Middleware route protection is partially implemented.

### 4.2. Lago (Billing & Subscription Management Service)
- **Integration Type**: Primarily direct client-side API calls from services (`userService.ts`, `subscriptionCheckService.ts`, `lago/invoiceService.ts`, `lago/subscriptionService.ts`) and also proxied via backend API routes (e.g., `/api/lago/invoice`, `/api/lago/subscription`).
- **API Key**: `NEXT_PUBLIC_LAGO_API_KEY` is used for these calls.
    - **Security Note**: Exposing an API key with broad permissions client-side is a security risk. Ideally, all Lago interactions should be proxied via hardened backend API routes.
- **Endpoints Used (Directly or Proxied)**:
    - `CUSTOMER_URL` (`${LAGO_API_URL}/customers`):
        - `GET /customers/{external_id}`: To check if a user (Keycloak ID) exists (`checkUserById`).
        - `POST /customers`: To create a new user (`createUser`).
        - `PUT /customers/{external_id}`: To update user details.
    - `SUBSCRIPTION_URL` (`${LAGO_API_URL}/subscriptions`):
        - `GET /subscriptions?external_customer_id={id}&status[]=active&status[]=pending`: To fetch user's current subscriptions (`checkUserSubscription`).
        - `POST /subscriptions`: To create new subscriptions (`createSubscription` in `lago/subscriptionService.ts`, often called after payment).
        - `DELETE /subscriptions/{id}`: To cancel subscriptions.
    - `INVOICE_URL` (`${LAGO_API_URL}/invoices`):
        - `GET /invoices?external_customer_id={id}&...`: To fetch invoices.
        - `PUT /invoices/{id}`: To update invoice payment status (e.g., to 'succeeded' post-payment, done via `/api/lago/invoice` which then calls Lago).
- **Key Functions**:
    - `getOrCreateUser` (`userService.ts`): Ensures a user authenticated via Keycloak has a corresponding customer record in Lago.
    - `checkUserSubscription` (`subscriptionCheckService.ts`, `subscription-utils.ts`): Checks current subscription status against Lago.
    - `createSubscription` (`lago/subscriptionService.ts`): Creates a subscription in Lago, usually after successful payment.
    - `updateInvoicePaymentStatus` (`lago/invoiceService.ts` via `/api/lago/invoice`): Marks Lago invoices as paid.

### 4.3. "onepay" (Payment Microservice & Email Service)
- **Base URL**: `onepay.cubeone.in`
- **Payment Integration**:
    - **Endpoint**: `https://onepay.cubeone.in/api/checkout`
    - **Flow**:
        1. Frontend (`CheckoutPage`) collects payment details.
        2. `initiatePayment` (`paymentService.ts`) calls the application's backend API `/api/payment`.
        3. Backend `/api/payment` (POST) calls `onepay.cubeone.in/api/checkout` with payload including amount, user details, `keycloakId` as `customer_id`, and `redirect_url` (`/checkout/confirm`). It specifies `selected_gateway: "Razorpay"`.
        4. "onepay" returns a payment URL; frontend redirects user to this URL (Razorpay checkout page).
        5. After payment attempt, user is redirected to `/checkout/confirm` (GET) specified as `redirect_url`.
        6. **Webhook**: Razorpay/onepay also sends a POST request to `/api/checkout/confirm` with payment status. This route then standardizes parameters and redirects the user's browser to `/checkout/confirmed` (GET) with payment details in query parameters.
        7. The `/checkout/confirmed` page (using `PaymentSuccessHandler.tsx`) processes the outcome.
- **Email Service Integration**:
    - **Endpoint**: `https://onepay.cubeone.in/api/send-subscription-email`
    - **Token**: Uses a static token `EMAIL_API_TOKEN = '1231934a-c657-48d3-882e-125e618eaf1b'`. (Security Note: Static, hardcoded tokens are a risk).
    - **Trigger**: `emailService.ts` (`sendInvoicePaymentEmailServer`, `sendInvoicePaymentEmailWithRetry`) calls this endpoint. This service is invoked:
        - After a Lago invoice is marked as paid (via `/api/lago/invoice` PUT handler).
        - Potentially directly for other subscription-related emails.
    - **Deduplication**: `emailService.ts` has logic to prevent sending duplicate emails for the same invoice using an in-memory `Set` and `localStorage`.

### 4.4. Internal Backend APIs (Next.js API Routes)
- **`/api/auth/[...nextauth]`**: NextAuth.js dynamic route for all authentication operations.
- **`/api/payment` (POST)**: Proxies payment initiation requests to "onepay".
- **`/api/checkout/confirm` (POST/GET)**: Handles payment provider webhook (POST) and user redirect (GET) post-payment, then redirects to a client-side confirmation page.
- **`/api/lago/invoice` (GET, PUT)**: Proxies requests to Lago for fetching and updating invoices. The PUT handler also triggers email notifications via `emailService.ts`.
- **`/api/lago/subscription` (POST, GET, DELETE)**: Proxies requests to Lago for creating, fetching, and canceling subscriptions.
- **`/api/email/invoice` (GET, POST)**: Seems to be another endpoint for triggering invoice-related emails, likely calling `emailService.ts`.
- **`/api/pricing/[slug]` (GET)**: Fetches product and plan pricing information, likely from a CMS or DB via `app/api/pricingSection/getPricingContentBySlug`.

### 4.5. Strapi CMS (Content Management)
- **Integration Type**: Content is fetched via GraphQL queries using Apollo Client.
- **GraphQL Endpoint**: Configured via `process.env.NEXT_PUBLIC_GRAPHQL_API_URL` in `lib/apolloClient.ts`.
- **Content Managed**:
    - Product information (details, descriptions, logos, categories - e.g., via `productService.ts`).
    - Dynamic page content for products, including hero sections, feature lists, testimonials, FAQs (e.g., via API handlers in `app/api/heroSection.ts`, `app/api/featuresSection.ts`, etc.).
    - Pricing plan details (tiers, features, prices - via `app/api/pricingSection.ts`, consumed by `planService.ts`).
- **Image Hosting**: Images managed by Strapi appear to be hosted on an S3 bucket (`fstech-saas-strapi.s3.ap-south-1.amazonaws.com`).
- **Query Pattern**: GraphQL queries typically filter content by product `slug` and retrieve specific components/sections structured in Strapi (e.g., `ComponentSectionsPricingSection`).

## 5. Key Operational Flows

### 5.1. User Authentication & Session Management
1.  User navigates to a protected resource or clicks "Login".
2.  Redirected to Keycloak via NextAuth.js.
3.  User authenticates with Keycloak.
4.  Redirected back to NextAuth.js callback. Tokens are obtained, JWT is created containing Keycloak `sub` (user ID), email, name. Session cookie is set.
5.  `UserProvider` on client-side:
    - Detects authenticated session via `useSession()`.
    - Calls `getOrCreateUser` to ensure user exists in Lago, using Keycloak ID.
6.  User access is granted. Session is maintained via JWT and refreshed automatically by NextAuth.js using the refresh token.
7.  Everywhere user identity is needed (e.g., for Lago, onepay), the Keycloak ID from the NextAuth session is used.

### 5.2. Trial Activation Flow
1.  User interacts with a "Start Trial" CTA on a product page (e.g., in `PricingSection` or `TrialFlow` component).
2.  **If Unauthenticated**:
    - Plan details (trial plan code, product slug) and any form data are saved to `localStorage`.
    - User is redirected to Keycloak login (via NextAuth.js `signIn`). Callback URL points back to the product page or a trial continuation URL.
    - After successful login, `UserProvider` initializes. Then `processPendingSubscription` (from `subscriptionCheckService.ts`) or similar logic on the product page retrieves details from `localStorage`.
3.  **If Authenticated (or after post-login processing)**:
    - `checkUserSubscription` is called to ensure the user doesn't already have an active/pending subscription for this product on Lago.
        - If already subscribed, user is informed and redirected to dashboard.
    - If eligible for trial:
        - A trial `CartItem` is typically created (price 0, duration "trial").
        - `createSubscription` (`lago/subscriptionService.ts`) is called directly to create a trial subscription in Lago (no payment involved for $0 trial).
        - Cart might be cleared.
        - User is redirected to a confirmation or dashboard.

### 5.3. Plan Selection & Adding to Cart
1.  User views plans on a product page (`PricingSection`). Plan data (including prices) is fetched via `/api/pricing/[slug]`.
2.  User selects a plan (e.g., "Monthly Standard").
3.  `handleAddPlan` (in `PricingSection`) or `handleSubscription` (in `subscriptionCheckService.ts`) orchestrates:
    - **If Unauthenticated**: Plan choice saved to `localStorage`, user redirected to login (see Trial Flow).
    - **If Authenticated**:
        - `checkUserSubscription`: Verifies user isn't already subscribed to this product on Lago. If so, shows current subscription info.
        - Cart Checks:
            - `isExactPlanInCart`: Checks if the exact same plan is already in the cart.
            - `findDifferentPlanSameProduct`: Checks if a different plan for the same product is in the cart (triggers plan change/upgrade/downgrade dialog).
        - If clear to proceed, `addItem` from `CartProvider` is called. The `CartItem` includes `planCode`, `price`, `planDuration`, etc.
        - Cart is updated on client-side and persisted to `localStorage`.

### 5.4. Checkout & Payment Flow
1.  User proceeds to checkout from the cart (`CheckoutPage`).
2.  Form collects necessary user details (name, email, address). Keycloak ID is retrieved from session.
3.  `onSubmit` in `CheckoutPage`:
    - Totals are taken from `CartProvider`.
    - `paymentData` object is constructed, including all cart items and user details.
    - `initiatePayment(paymentData)` (`paymentService.ts`) is called.
4.  `initiatePayment` calls backend `/api/payment` (POST).
5.  `/api/payment` calls "onepay" service (`https://onepay.cubeone.in/api/checkout`) with `keycloakId` as `customer_id`. Selected gateway is "Razorpay". Redirect URL is `/checkout/confirm`.
6.  "onepay" returns a payment URL. `/api/payment` returns this URL to frontend.
7.  Frontend redirects user to the "onepay"/Razorpay payment page.
8.  User completes (or cancels) payment on Razorpay page.
9.  **Post-Payment Handling**:
    - **User Redirect**: Razorpay redirects user's browser to `/checkout/confirm` (GET) with query parameters.
    - **Webhook**: Razorpay sends a webhook (POST) to `/api/checkout/confirm` with payment details. This route:
        - Sanitizes/validates data (including Keycloak ID).
        - Redirects the user's browser to `/checkout/confirmed` (GET) with all payment details (including Keycloak ID) as query parameters. This ensures the client has the confirmed data.
10. `/checkout/confirmed` page loads, likely using `PaymentSuccessHandler.tsx`:
    - `parsePaymentConfirmation` (`paymentService.ts`) extracts details from URL query parameters.
    - If payment successful (`isSuccess`):
        - `getOrCreateUser` ensures user is in Lago.
        - `createSubscription` (`lago/subscriptionService.ts`) is called for each item in the (now processed) cart to create Lago subscriptions. The Keycloak ID is used as `external_customer_id`.
        - `getLatestInvoiceByCustomerId` (`lago/invoiceService.ts`) fetches the newly generated Lago invoice.
        - `updateInvoicePaymentStatus` (`lago/invoiceService.ts`, which calls `/api/lago/invoice` PUT) marks the Lago invoice as 'succeeded'.
            - The `/api/lago/invoice` PUT handler, after successfully updating Lago, calls `sendInvoicePaymentEmailWithRetry` (`emailService.ts`) to send confirmation. `emailService.ts` calls `https://onepay.cubeone.in/api/send-subscription-email`.
        - Cart is cleared (client-side, potentially aided by middleware cookie `cart_cleared`).
        - User is typically shown a success message and may be redirected to dashboard or subscriptions page.

### 5.5. Pricing Calculations
- Plan base prices (monthly, yearly) are defined in the data source fetched by `/api/pricing/[slug]` (likely a CMS/DB).
- `planService.ts` (`getPlanPrice`) retrieves these base prices.
- `CartProvider` calculates:
    - `subtotal` based on item prices and quantities.
    - A fixed 18% tax (9% CGST + 9% IGST) on the subtotal.
    - `total` (subtotal + tax).
- There's no indication of dynamic pricing or complex tax calculations beyond this fixed percentage in the cart. Lago would handle the actual billing based on the subscribed plan's defined price.

### 5.6. Dashboard Redirection
- `NEXT_PUBLIC_DASHBOARD_URL` environment variable stores the base URL for the dashboard.
- Redirections often target `\${NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`.
- Triggered programmatically via `window.location.href` in components like:
    - Product page (`app/products/[slug]/page.tsx`): If user tries to activate a trial for an already subscribed product.
    - Subscription info dialogs.
- General post-login redirects are handled by NextAuth.js default behavior or its `redirect` callback configuration.

## 6. Error Reporting & Monitoring
- **Sentry**: Extensively integrated via `@sentry/nextjs`.
- Configuration in `next.config.mjs` and `sentry.*.config.ts` files.
- Covers client-side errors, server-side errors (API routes, RSCs).
- `react-error-boundary` likely used for component-level error catching.

## 7. Build, Deployment, and CI/CD
- **Scripts**: `package.json` includes `dev`, `build`, `start`, `lint`.
- **Containerization**: `Dockerfile` for building production image.
- **CI/CD**: `.gitlab-ci.yml` for GitLab CI pipeline.
- **Process Management**: `ecosystem.config.json` suggests PM2 for Node.js process management in production.
- `run.sh`: Likely a utility script for local dev or deployment tasks.

## 8. Areas for Architectural Review / Potential Improvements
- **Security of Lago API Key**: Direct client-side use of `NEXT_PUBLIC_LAGO_API_KEY` is a significant security concern. All Lago API interactions should ideally be proxied through dedicated, authenticated backend API routes to protect this key.
- **Security of Email Service Token**: The static, hardcoded `EMAIL_API_TOKEN` in `emailService.ts` for `onepay.cubeone.in/api/send-subscription-email` is a security risk. This should be a backend-only secret.
- **Tax Calculation**: Current fixed 18% tax in `CartProvider` is simplistic. If diverse tax rules are needed, integration with a dedicated tax service would be required.
- **Centralized Configuration**: URLs and keys for external services (Lago, onepay) are spread between `constants/api.ts` and environment variables. Consistent use of environment variables, accessed server-side where possible, is better.
- **Middleware Authentication**: The authentication logic in `middleware.ts` is currently a placeholder. Fully implementing route protection here using NextAuth.js `getToken` or similar would centralize access control.

---
*This document should be kept up-to-date with any significant architectural changes, new service integrations, or modifications to the core tech stack and operational flows.* 