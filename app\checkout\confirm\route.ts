import { NextRequest, NextResponse } from "next/server";

/**
 * Legacy POST handler for /checkout/confirm
 * This handles cases where payment gateways are still configured to post to /checkout/confirm
 * instead of the correct /api/checkout/confirm endpoint
 */
export async function POST(request: NextRequest) {
  console.log("⚠️ LEGACY CHECKOUT CONFIRM - Received POST request to /checkout/confirm (legacy endpoint)");
  console.log("🔄 LEGACY CHECKOUT CONFIRM - Redirecting to correct API endpoint");
  
  try {
    // Get the origin from the request
    const origin = request.nextUrl.origin || 'http://localhost:3000';
    
    // Forward the request to the correct API endpoint
    const apiUrl = `${origin}/api/checkout/confirm`;
    
    // Forward the entire request (headers, body, etc.) to the API route
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': request.headers.get('Content-Type') || 'application/x-www-form-urlencoded',
        'Accept': request.headers.get('Accept') || '*/*',
      },
      body: await request.text(),
    });
    
    // If the API route returns a redirect, return it
    if (response.redirected || response.status === 303 || response.status === 302) {
      const location = response.headers.get('Location') || response.url;
      console.log(`🔄 LEGACY CHECKOUT CONFIRM - API returned redirect to: ${location}`);
      return NextResponse.redirect(location, { status: 303 });
    }
    
    // For other responses, pass them through
    const responseBody = await response.text();
    return new NextResponse(responseBody, {
      status: response.status,
      headers: response.headers
    });
    
  } catch (error) {
    console.error("❌ LEGACY CHECKOUT CONFIRM - Error forwarding request:", error);
    
    // Fallback: redirect to confirmed page with error status
    const origin = request.nextUrl.origin || 'http://localhost:3000';
    const fallbackUrl = `${origin}/checkout/confirmed?status=error&message=processing_error`;
    console.log(`❌ LEGACY CHECKOUT CONFIRM - Fallback redirect to: ${fallbackUrl}`);
    
    return NextResponse.redirect(fallbackUrl, { status: 303 });
  }
}

/**
 * Handle GET requests by redirecting to the API route as well
 */
export async function GET(request: NextRequest) {
  // Handle legacy GET request to /checkout/confirm by redirecting users to the modern confirmation page
  // while preserving (and sanitising) all existing query parameters.
  try {
    const origin = request.nextUrl.origin || 'http://localhost:3000';

    // Copy existing search parameters and ensure a default success status exists
    const params = new URLSearchParams(request.nextUrl.searchParams);
    if (!params.has('status')) {
      params.append('status', 'success');
    }

    const redirectUrl = `${origin}/checkout/confirmed?${params.toString()}`;
    console.log(`🔄 LEGACY CHECKOUT CONFIRM (GET) - Redirecting to: ${redirectUrl}`);

    // 303 ensures the method is converted to GET on redirect
    return NextResponse.redirect(redirectUrl, { status: 303 });
  } catch (error) {
    console.error('❌ LEGACY CHECKOUT CONFIRM GET - Error:', error);
    const origin = request.nextUrl.origin || 'http://localhost:3000';
    return NextResponse.redirect(
      `${origin}/checkout/confirmed?status=error&message=processing_error`,
      { status: 303 }
    );
  }
} 