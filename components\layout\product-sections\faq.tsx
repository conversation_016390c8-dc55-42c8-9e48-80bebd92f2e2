import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";
import { forwardRef } from "react";

type RichTextNode = {
  type: string;
  text?: string;
  bold?: boolean;
  url?: string;
  children?: RichTextNode[];
};

type FAQSectionProps = {
  title: string;
  faqComponent: { id: string; question: string; answer: RichTextNode[] }[];
  sectionComponent?: string;
  sectionId?: number;
};

const renderRichText = (richText: RichTextNode[]) => {
  return richText.map((node, index) => {
    if (node.type === "paragraph") {
      return <p key={index}>{node.children?.map((child, i) => renderNode(child, i))}</p>;
    } else if (node.type === "list") {
      return (
        <ul key={index}>
          {node.children?.map((child, i) => (
            <li key={i}>{child.children?.map((subChild, j) => renderNode(subChild, j))}</li>
          ))}
        </ul>
      );
    }
    return null;
  });
};

const renderNode = (node: RichTextNode, index: number) => {
  if (node.type === "text") {
    if (node.bold) {
      return <strong key={index}>{node.text}</strong>;
    }
    return node.text;
  } else if (node.type === "link") {
    return (
      <a key={index} href={node.url}>
        {node.children?.map((child, i) => renderNode(child, i))}
      </a>
    );
  }
  return null;
};

export const FAQSection = forwardRef<HTMLDivElement, FAQSectionProps>(
  ({ title, faqComponent, sectionComponent, sectionId }, ref) => {
  return (
    <SectionContainer
      id="faq"
      ref={ref}
      sectionComponent={sectionComponent}
      sectionId={sectionId}
    >
      <SectionHeader subTitle="FAQS" title={title} />
      <div className="max-w-screen-md mx-auto">
        <Accordion type="single" collapsible className="AccordionRoot">
          {faqComponent.map(({ id, question, answer }) => (
            <AccordionItem key={id} value={id}>
              <AccordionTrigger className="text-left">
                {question}
              </AccordionTrigger>
              <AccordionContent>{renderRichText(answer)}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </SectionContainer>
  );
});

FAQSection.displayName = "FAQSection";
