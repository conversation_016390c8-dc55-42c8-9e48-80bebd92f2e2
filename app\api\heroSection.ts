import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_HERO_CONTENT_BY_SLUG = gql`
  query MainBannerBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      sections {
        ... on ComponentSectionsHeroSection {
          id
          title
          subtitle
          description
          buttons {
            text
          }
          heroImage {
            url
          }
        }
      }
    }
  }
`;

export const getHeroContentBySlug = async (slug: string) => {
  try {
    const { data } = await client.query({
      query: GET_HERO_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    return data.products[0]?.sections[0] || null;
  } catch (error) {
    console.error("Error fetching hero section data:", error);
    throw error;
  }
};
