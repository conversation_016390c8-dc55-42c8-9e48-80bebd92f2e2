import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Icon } from "@/components/ui/extras/icon";
import { icons } from "lucide-react";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";
import { forwardRef } from "react";

type FeaturesSectionProps = {
  title: string;
  description: string;
  keyfeatures: Array<{
    description: string;
    icon: string;
    text: string;
    id: string;
  }>;
  sectionComponent?: string;
  sectionId?: number;
};

export const FeaturesSection = forwardRef<HTMLDivElement, FeaturesSectionProps>(
  ({ title, description, keyfeatures = [], sectionComponent, sectionId }, ref) => {
    return (
      <SectionContainer
        id="features"
        ref={ref}
        sectionComponent={sectionComponent}
        sectionId={sectionId}
      >
        <SectionHeader
          subTitle="Features"
          title={title}
          description={description}
        />
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {keyfeatures.map(({ icon, text, description, id }) => (
            <div key={id}>
              <Card className="h-full bg-background border-0 shadow-none text-center">
                <CardHeader className="flex justify-center items-center">
                  <div className="bg-primary/20 p-2 rounded-full ring-8 ring-primary/10 mb-4">
                    <Icon
                      name={icon as keyof typeof icons}
                      size={24}
                      color="hsl(var(--primary))"
                      className="text-primary"
                    />
                  </div>
                  <CardTitle>{text}</CardTitle>
                </CardHeader>

                <CardContent className="text-muted-foreground">
                  {description}
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </SectionContainer>
    );
  }
);

FeaturesSection.displayName = "FeaturesSection";
