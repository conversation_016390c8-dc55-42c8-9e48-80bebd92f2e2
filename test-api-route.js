// Simple script to test the API route
async function testApiRoute() {
  try {
    console.log('Testing API route: /api/lago/customer/update');
    
    const response = await fetch('http://localhost:3000/api/lago/customer/update', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        customer: {
          external_id: 'test-id',
          tax_identification_number: '22AAAAA0000A1Z5',
          legal_name: 'Test Legal Name'
        }
      })
    });
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('Response data:', data);
    } else {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      try {
        const errorJson = JSON.parse(errorText);
        console.error('Error details:', errorJson);
      } catch {
        console.error('Raw error:', errorText);
      }
    }
  } catch (error) {
    console.error('Exception:', error);
  }
}

testApiRoute();
