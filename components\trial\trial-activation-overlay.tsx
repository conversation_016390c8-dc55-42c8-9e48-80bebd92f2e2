"use client";

import { useState, useEffect, useRef } from "react";
import { Loader2, <PERSON><PERSON>ircle, XCircle, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface TrialActivationOverlayProps {
  isVisible: boolean;
  onRedirect?: () => void;
  onStayHere?: () => void;
  redirectTime?: number;
  error?: string;
}

export function TrialActivationOverlay({
  isVisible,
  onRedirect,
  onStayHere = () => {},
  redirectTime = 5,
  error
}: TrialActivationOverlayProps) {
  const [isSuccess, setIsSuccess] = useState(false);
  const [countdown, setCountdown] = useState(redirectTime);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const hasError = !!error;

  useEffect(() => {
    if (!isVisible) {
      // Reset state when overlay is hidden
      setIsSuccess(false);
      setCountdown(redirectTime);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    if (hasError) {
      return; // Don't proceed with success flow if there's an error
    }

    // Set success after 2.5 seconds to simulate trial activation
    const successTimer = setTimeout(() => {
      setIsSuccess(true);

      // Start countdown for redirect
      intervalRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
            }
            if (onRedirect) {
              setTimeout(onRedirect, 500);
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }, 2500);

    return () => {
      clearTimeout(successTimer);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isVisible, onRedirect, redirectTime, hasError]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center">
      <div className="rounded-lg bg-card p-8 shadow-lg max-w-md w-full text-center space-y-6">
        {hasError ? (
          <>
            <XCircle className="h-16 w-16 mx-auto text-destructive" />
            <h2 className="text-2xl font-semibold">Trial Activation Failed</h2>
            <p className="text-muted-foreground">
              {error || "There was an error activating your trial. Please try again."}
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={onRedirect}
            >
              Close
            </Button>
          </>
        ) : !isSuccess ? (
          <>
            <Loader2 className="h-16 w-16 mx-auto animate-spin text-primary" />
            <h2 className="text-2xl font-semibold">Activating Your Trial</h2>
            <p className="text-muted-foreground">
              Please wait while we set up your trial account. This will only take a moment.
            </p>
          </>
        ) : (
          <>
            <div className="relative mb-4">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-32 w-32 rounded-full bg-green-100 dark:bg-green-900/30 animate-pulse" />
              </div>
              <CheckCircle className="h-16 w-16 mx-auto text-green-500 relative z-10" />
            </div>
            <h2 className="text-2xl font-semibold">Trial Activated Successfully!</h2>
            <p className="text-muted-foreground">
              Your trial is now active. You can start exploring all features immediately.
            </p>
            <div className="mt-8">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="px-8 relative overflow-hidden group"
                  onClick={onRedirect}
                >
                  <span className="relative z-10 flex items-center">Go to Dashboard <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" /></span>
                  <div className="absolute inset-0 bg-primary/10 translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="px-8 relative overflow-hidden group"
                  onClick={onStayHere}
                >
                  <span className="relative z-10">Stay Here & Explore More</span>
                  <div className="absolute inset-0 bg-muted/50 translate-y-full group-hover:translate-y-0 transition-transform duration-300" />
                </Button>
              </div>
              <div className="mt-6 flex flex-col items-center">
                <div className="relative w-64 h-2 bg-muted rounded-full overflow-hidden">
                  <div
                    className="absolute top-0 left-0 h-full bg-primary transition-all duration-1000 rounded-full"
                    style={{ width: `${(countdown / redirectTime) * 100}%` }}
                  />
                </div>
                <p className="text-sm text-muted-foreground mt-2 flex items-center justify-center">
                  <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                  Redirecting in {countdown} seconds
                </p>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}