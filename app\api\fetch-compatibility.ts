/**
 * FETCH COMPATIBILITY GUIDE
 * 
 * Important: For Node.js 18+ (including Next.js 14.x environments), the fetch API
 * requires a 'duplex' option when making requests with a body.
 * 
 * Problem: TypeError: RequestInit: duplex option is required when sending a body.
 * 
 * Solution:
 * 
 * 1. For Server Components & Route Handlers:
 *    ```
 *    import { safeFetch, safePost } from "@/lib/fetch-utils";
 *    
 *    // For any fetch request:
 *    const response = await safeFetch(url, options);
 *    
 *    // For POST requests:
 *    const response = await safePost(url, body, otherOptions);
 *    ```
 * 
 * 2. For Client Components:
 *    ```
 *    import { clientFetch, clientPost } from "@/lib/client-fetch-utils";
 *    
 *    // For any fetch request with error handling:
 *    const data = await clientFetch<YourDataType>(url, options);
 *    
 *    // For POST requests with error handling:
 *    const data = await clientPost<YourDataType>(url, body, otherOptions);
 *    ```
 * 
 * 3. If you can't use the utility functions, add the duplex option:
 *    ```
 *    // When using fetch directly with a body:
 *    const requestOptions = Object.assign({}, options, {
 *      body: bodyData,
 *      duplex: 'half'  // Required for Node.js 18+
 *    });
 *    
 *    const response = await fetch(url, requestOptions);
 *    ```
 * 
 * Important Note: NEVER use "use client" directives in files that will be imported
 * by server components or route handlers. Create separate client and server utilities
 * if you need both.
 * 
 * For more information, see:
 * https://nodejs.org/api/globals.html#fetch
 */

// Just a reference file, no actual code here
export {}; 