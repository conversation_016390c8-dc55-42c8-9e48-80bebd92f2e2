# Invoice Payment Status Update Flow

## Overview

This document explains how the invoice payment status is updated after a successful subscription activation. This flow is critical for ensuring that when a customer successfully pays for a subscription, the relevant invoice is marked as paid in the Lago system.

## Flow Sequence

1. User completes payment through the payment gateway
2. Payment success handler is triggered (`components/payment/payment-success-handler.tsx`)
3. Subscription is created in Lago via `/api/lago/subscription`
4. System waits for Lago to generate invoice (5 second delay)
5. System fetches and updates invoice status using one of two methods:
   - **Primary Method (New)**: Fetch latest invoice by customer ID and issuing date
   - **Fallback Method**: Fetch all invoices for the subscription ID
6. Each invoice's payment status is updated to "succeeded" via `/api/lago/invoice`
7. System verifies subscription is active and invoices are properly marked as paid

## Lago API Integration

For updating the invoice payment status, we use Lago's API endpoint:

```
PUT /invoices/{invoice_id}
```

With the following payload:

```json
{
  "invoice": {
    "payment_status": "succeeded"
  }
}
```

This is according to Lago's API documentation at: https://getlago.com/docs/api-reference/invoices/update-payment-status

## Finding Latest Invoices

We now support two methods for finding invoices to update:

### Method 1: Latest Invoice by Customer ID and Date (Primary)

This method uses the Lago API's filtering capabilities to find the latest invoice for a customer:

```
GET /invoices?external_customer_id={customer_id}&issuing_date_from={date}&payment_status=pending&status=finalized
```

This approach is more efficient as it:
- Directly targets the specific invoice we need
- Reduces the number of API calls required
- Works even when subscription ID is not available

### Method 2: Invoices by Subscription ID (Fallback)

If Method 1 fails, we fall back to the original approach:

1. Get the subscription details
2. Get all invoices for the associated customer
3. Filter to only include invoices for this subscription
4. Update each invoice's payment status

## Recent Changes (April 2024)

The invoice payment status update implementation was updated to match Lago's current API specification:

1. **Previously**: The endpoint was incorrectly using `/invoices/{invoice_id}/payment` with a payload containing `payment.payment_status`
2. **Updated**: Now using the correct endpoint `/invoices/{invoice_id}` with a payload containing `invoice.payment_status`
3. **Enhanced**: Added two methods for finding invoices to update (customer-based and subscription-based)
4. **Improved**: Added better error handling and retry logic with exponential backoff

These changes ensure that invoices are properly marked as paid after subscription activation.

## Verification Process

The payment success handler now includes a comprehensive verification process:

1. **Subscription Status Verification**: Check if the subscription status is "active" after creation
2. **Invoice Existence Verification**: Check that invoices are generated for the subscription
3. **Invoice Payment Status Verification**: Check that all invoices have the payment_status "succeeded"
4. **Automatic Correction**: If an invoice is not properly marked as paid, automatically attempt to update it

This multi-step verification ensures the subscription and billing process is completed correctly.

## Testing

To test the invoice payment status update functionality:

1. **Automated Tests**: Run the invoice API tests in `src/tests/api/invoice.test.ts` and payment flow tests in `src/tests/payment-success.test.ts`
2. **Manual Testing**: Use the test script at `src/scripts/test-invoice-update.js` with a real invoice ID

## Error Handling

The implementation includes:

- Retry mechanism for invoice fetching (max 3 attempts)
- Delay between retries with exponential backoff
- Multiple methods for finding invoices to update
- Detailed logging for debugging
- Error handling with proper error messages
- Verification of both subscription status and invoice payment status
- Automatic correction attempts if verification fails

## Related Files

- `app/api/lago/invoice/route.ts` - Backend API for invoice operations
- `src/services/lago/invoiceService.ts` - Service layer for invoice operations
- `components/payment/payment-success-handler.tsx` - Handles the payment success flow
- `src/tests/api/invoice.test.ts` - Tests for the invoice API
- `src/tests/payment-success.test.ts` - Tests for the payment success flow 