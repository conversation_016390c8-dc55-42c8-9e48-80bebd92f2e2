"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

// This is a catch-all route that redirects old product URLs to the new format
// For example: /onesociety -> /products/onesociety
export default function ProductRedirect({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const { slug } = params;

  useEffect(() => {
    // Get the hash fragment if any
    const hash = typeof window !== 'undefined' ? window.location.hash : '';
    
    // Redirect to the new URL format with the hash preserved
    router.replace(`/products/${slug}${hash}`);
  }, [router, slug]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <h2 className="text-xl font-medium">Redirecting to product page...</h2>
      <p className="text-muted-foreground">Please wait while we redirect you to the correct page.</p>
    </div>
  );
}
