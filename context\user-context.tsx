"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useSession } from "next-auth/react";
import { getOrCreateUser } from "@/src/services/userService";

// NextAuth with Keycloak sets the ID to the Keycloak sub field
interface KeycloakUser {
  id: string;  // This is the sub from Keycloak
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

// Define user context type
interface UserContextType {
  userId: string | null;  // This will hold the Keycloak ID
  userEmail: string | null;
  userName: string | null;
  setUserInfo: (userId: string, email?: string, name?: string) => void;
  clearUserInfo: () => void;
  isLoading: boolean;
  isKeycloakUser: boolean;
}

// Create the context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Provider component
export function UserProvider({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();
  const [userId, setUserId] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userName, setUserName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isKeycloakUser, setIsKeycloakUser] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);
  
  // Load user info from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedUserId = localStorage.getItem('user_id');
        const savedUserEmail = localStorage.getItem('user_email');
        const savedUserName = localStorage.getItem('user_name');
        const savedIsKeycloak = localStorage.getItem('is_keycloak_user');
        
        if (savedUserId) setUserId(savedUserId);
        if (savedUserEmail) setUserEmail(savedUserEmail);
        if (savedUserName) setUserName(savedUserName);
        if (savedIsKeycloak) setIsKeycloakUser(savedIsKeycloak === 'true');
        
        console.log('Loaded user data from localStorage:', {
          id: savedUserId,
          email: savedUserEmail,
          name: savedUserName,
          isKeycloakUser: savedIsKeycloak
        });
      } catch (error) {
        console.error('Failed to load user data from localStorage:', error);
      } finally {
        setIsLoading(false);
      }
    }
  }, []);
  
  // When session changes, update user info
  useEffect(() => {
    const initializeUser = async () => {
      if (hasInitialized || status === 'loading') return;

      setIsLoading(true);
      try {
        // Log the full session for debugging
        console.log('🔍 USER CONTEXT - Full NextAuth session:', JSON.stringify(session, null, 2));

        // Get Keycloak ID from session - simplified and more reliable approach
        let keycloakId = null;
        let sessionUserEmail = null;
        let sessionUserName = null;

        if (session?.user) {
          console.log('📊 USER CONTEXT - NextAuth session user object:', session.user);

          // Primary method: Get ID directly from session.user.id (this should now work with our NextAuth fix)
          const keycloakUser = session.user as KeycloakUser;
          if (keycloakUser.id) {
            keycloakId = keycloakUser.id;
            console.log('✅ USER CONTEXT - Found Keycloak ID in session.user.id:', keycloakId);
            setIsKeycloakUser(true);
          }

          // Get email and name from session
          sessionUserEmail = session.user.email || null;
          sessionUserName = session.user.name || null;
        }
        
        // Fallback: If we still don't have a Keycloak ID, try to find it in the access token
        if (!keycloakId && session?.accessToken) {
          try {
            console.log('🔑 USER CONTEXT - Attempting to parse access token as fallback');
            if (typeof session.accessToken === 'string') {
              const tokenParts = session.accessToken.split('.');
              if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload.sub) {
                  keycloakId = payload.sub;
                  console.log('✅ USER CONTEXT - Found Keycloak ID (sub) in access token:', keycloakId);
                  setIsKeycloakUser(true);
                }
              }
            }
          } catch (error) {
            console.error('❌ USER CONTEXT - Error parsing access token:', error);
          }
        }
        
        // Handle the result based on whether we found a Keycloak ID
        if (keycloakId) {
          // We have a Keycloak ID - proceed with user setup
          try {
            console.log(`🔄 USER CONTEXT - Creating or verifying user in Lago with Keycloak ID: ${keycloakId}`);

            // This will either find the existing user or create a new one
            const userInLago = await getOrCreateUser({
              external_id: keycloakId,
              email: sessionUserEmail || undefined,
              name: sessionUserName || undefined
            });

            if (userInLago) {
              console.log(`✅ USER CONTEXT - User exists/created in Lago with Keycloak ID: ${keycloakId}`);
            } else {
              console.warn(`⚠️ USER CONTEXT - Failed to verify/create user in Lago with Keycloak ID: ${keycloakId}`);
            }
          } catch (error) {
            console.error('❌ USER CONTEXT - Error verifying user in Lago:', error);
          }

          // Update state and localStorage with Keycloak ID
          setUserId(keycloakId);
          localStorage.setItem('user_id', keycloakId);
          localStorage.setItem('is_keycloak_user', 'true');

          if (sessionUserEmail) {
            setUserEmail(sessionUserEmail);
            localStorage.setItem('user_email', sessionUserEmail);
          }

          if (sessionUserName) {
            setUserName(sessionUserName);
            localStorage.setItem('user_name', sessionUserName);
          }

          console.log(`✅ USER CONTEXT - Updated user context with Keycloak data: ID=${keycloakId}, Email=${sessionUserEmail}, Name=${sessionUserName}`);
        } else if (session?.user) {
          // We have a session but no Keycloak ID - this is a problem
          console.error('❌ USER CONTEXT - Session exists but no Keycloak ID found. Session user object:', session.user);
          console.error('❌ USER CONTEXT - This will cause cart functionality to fail');
        } else if (status === 'authenticated') {
          // We're authenticated but have no session - this shouldn't happen
          console.error('❌ USER CONTEXT - Status is authenticated but no session found');
        }
        
        setHasInitialized(true);
      } catch (error) {
        console.error('❌ Error initializing user:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeUser();
  }, [session, status, hasInitialized]);
  
  // Simple validation effect - only runs when userId changes
  useEffect(() => {
    if (userId) {
      console.log(`⚙️ USER CONTEXT - Current userId (Keycloak ID): ${userId}`);

      // Verify if userId is a valid Keycloak ID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      const isValidKeycloakId = uuidRegex.test(userId);

      if (isValidKeycloakId) {
        setIsKeycloakUser(true);
        try {
          localStorage.setItem('is_keycloak_user', 'true');
        } catch (e) {
          console.error('Error saving Keycloak flag to localStorage:', e);
        }
      } else {
        console.warn(`⚠️ USER CONTEXT - Current userId doesn't match Keycloak UUID format: ${userId}`);
      }
    }
  }, [userId]);
  
  // Method to manually set user info
  const setUserInfo = (id: string, email?: string, name?: string) => {
    setUserId(id);
    localStorage.setItem('user_id', id);
    
    if (email) {
      setUserEmail(email);
      localStorage.setItem('user_email', email);
    }
    
    if (name) {
      setUserName(name);
      localStorage.setItem('user_name', name);
    }
    
    // Assume manually set IDs might be Keycloak IDs
    setIsKeycloakUser(true);
    localStorage.setItem('is_keycloak_user', 'true');
    
    console.log(`✅ Manually set user info: ID=${id}, Email=${email || 'not provided'}, Name=${name || 'not provided'}`);
  };
  
  // Method to clear user info
  const clearUserInfo = () => {
    setUserId(null);
    setUserEmail(null);
    setUserName(null);
    setIsKeycloakUser(false);
    localStorage.removeItem('user_id');
    localStorage.removeItem('user_email');
    localStorage.removeItem('user_name');
    localStorage.removeItem('is_keycloak_user');
    console.log('User info cleared');
  };
  
  return (
    <UserContext.Provider
      value={{
        userId,
        userEmail,
        userName,
        setUserInfo,
        clearUserInfo,
        isLoading,
        isKeycloakUser
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook for using the user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
} 