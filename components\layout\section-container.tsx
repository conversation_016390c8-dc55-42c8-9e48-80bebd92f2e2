import React, { forwardRef } from "react";
import { cn } from "@/lib/utils";

interface Props {
  children: React.ReactNode;
  id?: string;
  className?: string;
  sectionComponent?: string;
  sectionId?: number;
}

/**
 * Generate a section anchor from a section component name and ID
 */
export const generateSectionAnchor = (component: string, id: number): string => {
  return `${component.replace('sections.', '')}-${id}`;
};

const SectionContainer = forwardRef<HTMLDivElement, Props>(
  ({ id, children, className, sectionComponent, sectionId }, ref) => {
    // Generate anchor ID if component and section ID are provided
    const anchorId = sectionComponent && sectionId
      ? generateSectionAnchor(sectionComponent, sectionId)
      : id;

    return (
      <section
        id={anchorId}
        ref={ref}
        className={cn("pb-20 sm:pb-32", className)}
        data-section-component={sectionComponent}
        data-section-id={sectionId}
      >
        <div className="container">{children}</div>
      </section>
    );
  }
);

SectionContainer.displayName = "SectionContainer";

export default SectionContainer;
