"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { SubscriptionCard } from "@/components/subscription/subscription-card";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertCircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { analyzeCustomerPlans } from "@/src/services/lago/customerService";

export default function PlanViewerPage() {
  const [customerId, setCustomerId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [plans, setPlans] = useState<Array<{
    id: string;
    plan_code: string;
    status: string;
    created_at: string;
    next_billing_date?: string;
    product?: string;
    planType?: string;
    planDuration?: string;
    isTrial: boolean;
    displayName: string;
  }> | null>(null);
  
  const handleLookup = async () => {
    if (!customerId.trim()) {
      setError("Please enter a customer ID");
      return;
    }
    
    setIsLoading(true);
    setError(null);
    setPlans(null);
    
    try {
      const result = await analyzeCustomerPlans(customerId);
      if (result.hasAnyPlan) {
        setPlans(result.activePlans);
      } else {
        setError("No plans found for this customer");
      }
    } catch (err) {
      setError("Error looking up customer plans: " + (err instanceof Error ? err.message : String(err)));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">Subscription Plan Viewer</h1>
      <p className="text-muted-foreground mb-8">
        View details of a customer&apos;s subscription plans
      </p>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Customer Lookup</CardTitle>
          <CardDescription>Enter a customer ID to view their subscriptions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="customerId">Customer ID</Label>
              <Input
                id="customerId"
                value={customerId}
                onChange={(e) => setCustomerId(e.target.value)}
                placeholder="Enter Keycloak ID"
                className="mt-1"
              />
            </div>
            <div className="pt-7">
              <Button onClick={handleLookup} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Looking up...
                  </>
                ) : (
                  "Look up Plans"
                )}
              </Button>
            </div>
          </div>
          
          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
      
      {plans && plans.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">
            Subscription Plans
            <span className="ml-2 text-muted-foreground font-normal">
              ({plans.length} plan{plans.length !== 1 ? 's' : ''})
            </span>
          </h2>
          
          <Tabs defaultValue="cards">
            <TabsList className="mb-4">
              <TabsTrigger value="cards">Cards</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>
            
            <TabsContent value="cards">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {plans.map((plan) => (
                  <SubscriptionCard
                    key={plan.id}
                    planCode={plan.plan_code}
                    status={plan.status}
                    startedAt={plan.created_at}
                    expiresAt={plan.next_billing_date}
                    onManage={() => alert(`Manage plan: ${plan.displayName}`)}
                    onRenew={() => alert(`Renew plan: ${plan.displayName}`)}
                  />
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="details">
              <div className="space-y-4">
                {plans.map((plan) => (
                  <Card key={plan.id}>
                    <CardHeader>
                      <CardTitle>{plan.displayName}</CardTitle>
                      <CardDescription>
                        Plan Code: <code className="bg-muted px-1 py-0.5 rounded">{plan.plan_code}</code>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div className="space-y-4">
                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-2">Basic Information</h3>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="font-medium">Status:</div>
                              <div className="capitalize">{plan.status}</div>
                              
                              <div className="font-medium">Trial Plan:</div>
                              <div>{plan.isTrial ? "Yes" : "No"}</div>
                              
                              <div className="font-medium">ID:</div>
                              <div className="truncate">{plan.id}</div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-2">Extracted Information</h3>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="font-medium">Product:</div>
                              <div>{plan.product || "—"}</div>
                              
                              <div className="font-medium">Plan Type:</div>
                              <div className="capitalize">{plan.planType || "—"}</div>
                              
                              <div className="font-medium">Duration:</div>
                              <div className="capitalize">{plan.planDuration || "—"}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-4">
                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-2">Dates</h3>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="font-medium">Created:</div>
                              <div>{new Date(plan.created_at).toLocaleString()}</div>
                              
                              <div className="font-medium">Next Billing:</div>
                              <div>
                                {plan.next_billing_date 
                                  ? new Date(plan.next_billing_date).toLocaleString() 
                                  : "—"}
                              </div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-2">Actions</h3>
                            <div className="flex gap-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => alert(`Manage plan: ${plan.displayName}`)}
                              >
                                Manage
                              </Button>
                              
                              <Button 
                                size="sm"
                                disabled={plan.status === "active" || plan.status === "pending"}
                                onClick={() => alert(`Renew plan: ${plan.displayName}`)}
                              >
                                Renew
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
} 