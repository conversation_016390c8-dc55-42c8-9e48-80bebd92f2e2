// EMERGENCY SUBSCRIPTION CREATOR
// This can be executed directly in browser console if the UI subscription process fails

/**
 * Emergency script to manually create subscriptions
 * Usage: Just paste this entire script into the browser console and execute it
 * when on the checkout confirmation page
 */
(async function createEmergencySubscription() {
  console.log("🚨 EMERGENCY SUBSCRIPTION - Starting manual subscription creation process");

  // Function to validate Keycloak ID format
  function isValidKeycloakId(id) {
    if (!id) return false;
    // Keycloak uses standard UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  // Function to get the best Keycloak ID available
  function getKeycloakId() {
    // Order of priority:
    // 1. Session user ID
    // 2. Verified localStorage ID
    // 3. From token JWT

    // Method 1: First check if we can get Keycloak ID from session
    try {
      if (window.sessionStorage.getItem('next-auth.session-token')) {
        const token = window.sessionStorage.getItem('next-auth.session-token');
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          if (payload.sub) {
            console.log("✅ EMERGENCY SUBSCRIPTION - Found Keycloak ID in NextAuth token:", payload.sub);
            return payload.sub;
          }
        }
      }
    } catch (error) {
      console.error("❌ EMERGENCY SUBSCRIPTION - Failed to check session token:", error);
    }

    // Method 2: Check localStorage for verified Keycloak ID
    try {
      if (localStorage.getItem('is_keycloak_user') === 'true') {
        const userId = localStorage.getItem('user_id');
        if (userId && isValidKeycloakId(userId)) {
          console.log("✅ EMERGENCY SUBSCRIPTION - Using verified Keycloak ID from localStorage:", userId);
          return userId;
        }
      }
    } catch (error) {
      console.error("❌ EMERGENCY SUBSCRIPTION - Error accessing localStorage:", error);
    }

    // Method 3: Check for Keycloak tokens
    try {
      const token = localStorage.getItem('keycloak_token') ||
                  localStorage.getItem('token') ||
                  sessionStorage.getItem('keycloak_token') ||
                  sessionStorage.getItem('token');

      if (token) {
        try {
          const parts = token.split('.');
          if (parts.length === 3) {
            const payload = JSON.parse(atob(parts[1]));
            if (payload.sub) {
              console.log("✅ EMERGENCY SUBSCRIPTION - Extracted Keycloak ID from token:", payload.sub);
              return payload.sub;
            }
          }
        } catch (error) {
          console.error("❌ EMERGENCY SUBSCRIPTION - Error parsing token:", error);
        }
      }
    } catch (error) {
      console.error("❌ EMERGENCY SUBSCRIPTION - Error checking tokens:", error);
    }

    // Method 4: Last resort: check URL for Keycloak ID
    try {
      const url = new URL(window.location.href);
      const keycloakId = url.searchParams.get('keycloak_id') || url.searchParams.get('sub');
      if (keycloakId && isValidKeycloakId(keycloakId)) {
        console.log("✅ EMERGENCY SUBSCRIPTION - Found Keycloak ID in URL parameters:", keycloakId);
        return keycloakId;
      }
    } catch (error) {
      console.error("❌ EMERGENCY SUBSCRIPTION - Error checking URL parameters:", error);
    }

    // Fallback to localStorage even if not verified
    return localStorage.getItem('user_id');
  }

  // Get the Keycloak ID
  const keycloakId = getKeycloakId();

  // Check if the Keycloak ID is valid
  if (!keycloakId) {
    console.error("❌ EMERGENCY SUBSCRIPTION - No Keycloak ID found");
    alert("Error: Keycloak ID not found. Please contact support.");
    return;
  }

  // Validate Keycloak ID format
  if (!isValidKeycloakId(keycloakId)) {
    console.error("❌ EMERGENCY SUBSCRIPTION - Invalid Keycloak ID format:", keycloakId);
    alert("Error: Invalid Keycloak ID format. Please contact support.");
    return;
  }

  console.log("✅ EMERGENCY SUBSCRIPTION - Using verified Keycloak ID:", keycloakId);

  // Get saved cart items
  const savedCartItemsJson = localStorage.getItem("payment_cart_items");
  if (!savedCartItemsJson) {
    console.error("❌ EMERGENCY SUBSCRIPTION - No cart items found in localStorage");
    alert("Error: No cart items found. Please contact support.");
    return;
  }

  try {
    // Parse saved cart items
    const cartItems = JSON.parse(savedCartItemsJson);
    console.log("📦 EMERGENCY SUBSCRIPTION - Cart items:", cartItems);

    if (!cartItems || cartItems.length === 0) {
      console.error("❌ EMERGENCY SUBSCRIPTION - Cart items array is empty");
      alert("Error: No cart items to process. Please contact support.");
      return;
    }

    // Create a subscription for each item
    let successCount = 0;
    let errorCount = 0;

    for (const item of cartItems) {
      if (!item.planCode) {
        console.error("❌ EMERGENCY SUBSCRIPTION - Item missing plan code:", item);
        errorCount++;
        continue;
      }

      try {
        console.log(`🔄 EMERGENCY SUBSCRIPTION - Creating subscription for ${item.name} using Keycloak ID ${keycloakId}...`);

        // Calculate dates
        const now = new Date();
        // Set time 30 seconds behind actual time for API posting
        const adjustedNow = new Date(now.getTime() - 30 * 1000);
        const subscriptionDate = adjustedNow.toISOString();
        console.log(`🕒 EMERGENCY SUBSCRIPTION - Using adjusted time: ${subscriptionDate} (30 seconds behind actual time: ${now.toISOString()})`);
        const endDate = new Date(now);
        endDate.setFullYear(endDate.getFullYear() + 1);
        const endingDate = endDate.toISOString();

        // Create external ID
        const externalId = `${keycloakId}@${item.planCode}`;

        // Create payload
        const payload = {
          subscription: {
            external_customer_id: keycloakId,  // CRITICAL: This is the Keycloak ID
            plan_code: item.planCode,
            external_id: externalId,
            name: item.name,
            billing_time: "anniversary",
            ending_at: endingDate,
            subscription_at: subscriptionDate
          }
        };

        console.log("📝 EMERGENCY SUBSCRIPTION - Payload:", payload);

        // Verify Keycloak ID in payload
        if (payload.subscription.external_customer_id !== keycloakId) {
          throw new Error("Keycloak ID mismatch in payload");
        }

        // Make direct API call
        const response = await fetch(`${LAGO_API_URL}/subscriptions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${LAGO_API_KEY}`
          },
          body: JSON.stringify(payload)
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ EMERGENCY SUBSCRIPTION - Successfully created subscription for ${item.name} with Keycloak ID ${keycloakId}:`, data);
          successCount++;

          // Save successful subscription
          try {
            const successfulSubscriptions = JSON.parse(localStorage.getItem('emergency_successful_subscriptions') || '[]');
            successfulSubscriptions.push({
              keycloakId,
              planCode: item.planCode,
              timestamp: Date.now()
            });
            localStorage.setItem('emergency_successful_subscriptions', JSON.stringify(successfulSubscriptions));
          } catch (e) {
            console.error("❌ EMERGENCY SUBSCRIPTION - Error storing successful subscription:", e);
          }
        } else {
          const errorText = await response.text();
          console.error(`❌ EMERGENCY SUBSCRIPTION - API error (${response.status}):`, errorText);
          errorCount++;

          // Store error for diagnostics
          try {
            const failedSubscriptions = JSON.parse(localStorage.getItem('emergency_failed_subscriptions') || '[]');
            failedSubscriptions.push({
              keycloakId,
              planCode: item.planCode,
              error: `API error (${response.status}): ${errorText}`,
              timestamp: Date.now()
            });
            localStorage.setItem('emergency_failed_subscriptions', JSON.stringify(failedSubscriptions));
          } catch (e) {
            console.error("❌ EMERGENCY SUBSCRIPTION - Error storing failed subscription:", e);
          }
        }
      } catch (error) {
        console.error(`❌ EMERGENCY SUBSCRIPTION - Error creating subscription for ${item.name}:`, error);
        errorCount++;
      }
    }

    // Show final result
    console.log(`📊 EMERGENCY SUBSCRIPTION - Results: ${successCount} successful, ${errorCount} failed using Keycloak ID ${keycloakId}`);

    if (successCount > 0) {
      alert(`Successfully created ${successCount} subscription(s) with Keycloak ID ${keycloakId}. ${errorCount > 0 ? `${errorCount} failed.` : ''}`);
    } else {
      alert("Failed to create any subscriptions. Please check console for details and contact support.");
    }

  } catch (error) {
    console.error("❌ EMERGENCY SUBSCRIPTION - Fatal error:", error);
    alert("Error processing subscriptions. Please contact support.");
  }
})();