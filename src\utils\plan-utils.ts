/**
 * Plan Utils
 *
 * Utility functions for working with subscription plan codes and related operations.
 */

/**
 * Extracts the product code from a plan code
 * @param planCode The plan code (e.g., 'product_standard_monthly' or 'onesociety_starter_y')
 * @returns The product code or null if invalid format
 */
export function extractProductCode(planCode: string): string | null {
  if (!planCode) return null;

  // Special case handling for known product codes
  if (planCode.startsWith('onesociety_')) return 'onesociety';
  if (planCode.startsWith('onegate_')) return 'onegate';
  if (planCode.startsWith('onepos_')) return 'onepos';
  if (planCode.startsWith('onefooddialer_')) return 'onefooddialer';

  // Default handling for other product codes
  const parts = planCode.split('_');
  if (parts.length < 2) return null;

  return parts[0];
}

/**
 * Extracts the plan type from a plan code
 * @param planCode The plan code (e.g., 'product_standard_monthly')
 * @returns The plan type or null if invalid format
 */
export function extractPlanType(planCode: string): string | null {
  if (!planCode) return null;

  const parts = planCode.split('_');
  if (parts.length < 3) return null;

  return parts[1];
}

/**
 * Extracts the plan duration from a plan code
 * @param planCode The plan code (e.g., 'product_standard_monthly')
 * @returns The plan duration ('monthly', 'yearly', 'trial') or null if invalid format
 */
export function extractPlanDuration(planCode: string): 'monthly' | 'yearly' | 'trial' | null {
  if (!planCode) return null;

  const parts = planCode.split('_');
  if (parts.length < 3) return null;

  const lastPart = parts[parts.length - 1].toLowerCase();

  if (lastPart === 'monthly') return 'monthly';
  if (lastPart === 'yearly' || lastPart === 'annual') return 'yearly';
  if (lastPart === 'trial') return 'trial';

  return null;
}

/**
 * Checks if a plan code is for a trial plan
 * @param planCode The plan code to check
 * @returns Boolean indicating if it's a trial plan
 */
export function isTrialPlan(planCode: string): boolean {
  if (!planCode) return false;
  return planCode.toLowerCase().includes('trial');
}

/**
 * Generates a plan code based on product, plan type, and duration
 * @param product The product identifier
 * @param planType The plan type (e.g., 'standard', 'premium')
 * @param duration The duration ('monthly', 'yearly', 'trial')
 * @returns Formatted plan code
 */
export function generatePlanCode(
  product: string,
  planType: string,
  duration: 'monthly' | 'yearly' | 'trial'
): string {
  return `${product.toLowerCase()}_${planType.toLowerCase()}_${duration.toLowerCase()}`;
}

/**
 * Gets the display name for a product code
 * @param productCode The product code
 * @returns Formatted product display name
 */
export function getProductDisplayName(productCode: string | null): string {
  if (!productCode) return 'Unknown Product';

  // Handle known product codes
  switch (productCode.toLowerCase()) {
    case 'onerestro':
      return 'OneRestro';
    case 'onebiz':
      return 'OneBiz';
    case 'onegate':
      return 'OneGate';
    case 'onepos':
      return 'OnePOS';
    case 'onefooddialer':
      return 'OneFoodDialer';
    case 'onesociety':
      return 'OneSociety';
    default:
      return productCode.charAt(0).toUpperCase() + productCode.slice(1);
  }
}

/**
 * Gets the display name for a plan type
 * @param planType The plan type
 * @returns Formatted plan type display name
 */
export function getPlanTypeDisplayName(planType: string | null): string {
  if (!planType) return 'Standard';

  switch (planType.toLowerCase()) {
    case 'starter':
      return 'Starter';
    case 'standard':
      return 'Standard';
    case 'premium':
      return 'Premium';
    case 'enterprise':
      return 'Enterprise';
    case 'basic':
      return 'Basic';
    default:
      return planType.charAt(0).toUpperCase() + planType.slice(1);
  }
}

/**
 * Gets the display name for a plan duration
 * @param planDuration The plan duration
 * @returns Formatted plan duration display name
 */
export function getPlanDurationDisplayName(planDuration: string | null): string {
  if (!planDuration) return 'Monthly';

  switch (planDuration.toLowerCase()) {
    case 'monthly':
      return 'Monthly';
    case 'yearly':
    case 'annual':
      return 'Annual';
    case 'trial':
      return 'Trial';
    default:
      return planDuration.charAt(0).toUpperCase() + planDuration.slice(1);
  }
}

/**
 * Formats a plan code into a human-readable display name
 * @param planCode The plan code to format
 * @returns Formatted display name
 */
export function formatPlanDisplayName(planCode: string): string {
  if (!planCode) return 'Unknown Plan';

  const productCode = extractProductCode(planCode);
  const planType = extractPlanType(planCode);
  const duration = extractPlanDuration(planCode);

  const product = getProductDisplayName(productCode);
  const type = getPlanTypeDisplayName(planType);
  const durationText = getPlanDurationDisplayName(duration);

  return `${product} ${type} (${durationText})`.trim();
}

/**
 * Checks if two plan codes belong to the same product
 * @param planCode1 First plan code
 * @param planCode2 Second plan code
 * @returns Boolean indicating if the plans belong to the same product
 */
export function arePlansFromSameProduct(planCode1: string, planCode2: string): boolean {
  if (!planCode1 || !planCode2) return false;

  // First check if they have the same prefix before the first underscore
  const prefix1 = planCode1.split('_')[0];
  const prefix2 = planCode2.split('_')[0];

  if (prefix1 === prefix2) return true;

  // If prefixes don't match, check if they're both from the same known product family
  const knownProducts = [
    'onesociety',
    'onegate',
    'onepos',
    'onefooddialer'
  ];

  for (const product of knownProducts) {
    if (planCode1.startsWith(`${product}_`) && planCode2.startsWith(`${product}_`)) {
      return true;
    }
  }

  return false;
}

/**
 * Checks if two plan codes are exactly the same plan
 * @param planCode1 First plan code
 * @param planCode2 Second plan code
 * @returns Boolean indicating if the plans are exactly the same
 */
export function areExactSamePlan(planCode1: string, planCode2: string): boolean {
  if (!planCode1 || !planCode2) return false;
  return planCode1 === planCode2;
}

/**
 * Test function for plan code utilities
 */
export function testPlanCodeUtils(planCode: string): Record<string, string | boolean | null> {
  return {
    originalCode: planCode,
    productCode: extractProductCode(planCode),
    planType: extractPlanType(planCode),
    duration: extractPlanDuration(planCode),
    isTrial: isTrialPlan(planCode),
    displayName: formatPlanDisplayName(planCode)
  };
}