openapi: 3.0.0
info:
  title: OneBiz API
  version: 0.1.0
  description: API for the OneBiz SaaS Marketplace

servers:
  - url: http://localhost:3000
    description: Development server
  - url: https://your-production-domain.com  # Replace with your actual production URL
    description: Production server

components:
  securitySchemes:
    bearerAuth:            # arbitrary name for the security scheme
      type: http
      scheme: bearer
      bearerFormat: JWT  # optional, arbitrary value for documentation purposes
    # Keycloak integration (OAuth2) -  More detailed, but may need adjustments
    Keycloak:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: "${KEYCLOAK_ISSUER}/protocol/openid-connect/auth"  # From your Keycloak realm settings
          tokenUrl: "${KEYCLOAK_ISSUER}/protocol/openid-connect/token"         # From your Keycloak realm settings
          refreshUrl: "${KEYCLOAK_ISSUER}/protocol/openid-connect/token"       # Keycloak's refresh token endpoint
          scopes: {}   # Define scopes based on your Keycloak setup (e.g., openid, profile, email, roles)

  schemas:
    # --- Common Schemas ---
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message.

    Button:
      type: object
      properties:
        text:
          type: string
        id:
          type: string

    Feature:
      type: object
      properties:
        feature:
          type: string
        isIncluded:
          type: boolean
        id:
            type: string


    # --- Section-Specific Schemas ---

    HeroSection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        subtitle:
          type: string
        description:
          type: string
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/Button'
        heroImage:
          type: object
          properties:
            url:
              type: string
              format: url

    BenefitCard:
      type: object
      properties:
        description:
          type: string
        icon:
          type: string
        text:
          type: string
        id:
          type: string

    BenefitsSection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        cards:
          type: array
          items:
            $ref: '#/components/schemas/BenefitCard'

    KeyFeature:
      type: object
      properties:
        description:
          type: string
        icon:
          type: string
        text:
          type: string
        id:
          type: string

    FeaturesSection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        keyfeatures:
          type: array
          items:
            $ref: '#/components/schemas/KeyFeature'
        button:
          $ref: '#/components/schemas/Button'


    ServiceCard:
       type: object
       properties:
         tag:
           type: string
           nullable: true
         title:
            type: string
         decription:
           type: string
         id:
           type: string
    ServicesSection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        description:
          type: string
        servicesCard:
          type: array
          items:
            $ref: '#/components/schemas/ServiceCard'



    FAQComponent:
      type: object
      properties:
        id:
          type: string
        question:
          type: string
        answer:
          type: array
          items:
             $ref: '#/components/schemas/RichTextNode'
    FAQSection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        faqComponent:
          type: array
          items:
            $ref: '#/components/schemas/FAQComponent'

    SubscriptionPlan:
      type: object
      properties:
        monthlyPricing:
          type: number
        yearlyPricing:
          type: number
        tag:
          type: string
          nullable: true
        name:
          type: string
        plan_code_monthly:
          type: string
        plan_code_yearly:
          type: string
        id:
            type: string
        description:
          type: string
        button:
          $ref: '#/components/schemas/Button'
        fetaures:
          type: array
          items:
             $ref: '#/components/schemas/Feature'


    TrialPlan:
      type: object
      properties:
        name:
          type: string
        tag:
          type: string
          nullable: true
        trialDurationInDays:
          type: integer
        plan_code:
          type: string
        description:
          type: string
        button:
            $ref: '#/components/schemas/Button'
        features:
          type: array
          items:
            $ref: '#/components/schemas/Feature'

    EnterprisePlan:
      type: object
      properties:
        name:
          type: string
        tag:
            type: string
        description:
          type: string
        button:
          $ref: '#/components/schemas/Button'
        features:
          type: array
          items:
            $ref: '#/components/schemas/Feature'


    PricingSection:
      type: object
      properties:
        heading:
          type: string
        title:
          type: string
        subscriptionPlan:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionPlan'
        enterprisePlan:
          $ref: '#/components/schemas/EnterprisePlan'
        trialPlan:
            $ref: '#/components/schemas/TrialPlan'

    Testimonial:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        photo:
          type: object
          nullable: true
          properties:
            url:
              type: string
              format: url
        rating:
          type: number
        designation:
          type: string
        description:
          type: string

    TestimonialSection:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        testimonials:
          type: array
          items:
            $ref: '#/components/schemas/Testimonial'
    
    RichTextNode:
      type: object
      properties:
        type:
          type: string
        text:
            type: string
            nullable: true
        bold:
            type: boolean
            nullable: true
        url:
            type: string
            nullable: true
        children:
            type: array
            nullable: true
            items:
                $ref: '#/components/schemas/RichTextNode'

    ProductCardData:
      type: object
      properties:
        productName:
          type: string
        productDescription:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        productLogo:
          type: object
          nullable: true
          properties:
            url:
              type: string
              format: url

    ProductResponse:
      type: object
      properties:
        slug:
          type: string
        ProductCard:
          $ref: '#/components/schemas/ProductCardData'

    GraphQLResponse:  #  For /products endpoint
      type: object
      properties:
        data:
          type: object
          properties:
            products:
              type: array
              items:
                $ref: '#/components/schemas/ProductResponse'


    CartItem:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        price:
          type: number
        planCode:
            type: string
        quantity:
          type: integer
        image:
          type: string
          format: url
        slug:
          type: string
        productName:
          type: string
        productLogo:
            type: string
        planDuration:
          type: string
          enum: [monthly, yearly, trial]
        trialDays:
          type: integer
          nullable: true  # Only for trial plans

    PaymentRequest:
      type: object
      required:
        - fullName
        - email
        - phone
        - address1
        - city
        - state
        - pincode
        - product
        - price
        - items
      properties:
        fullName:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        address1:
          type: string
        address2:
          type: string
        city:
          type: string
        state:
          type: string
        pincode:
          type: string
        gstNumber:
          type: string
        product:
          type: string
        price:
          type: number
          format: float
        items:
          type: array
          items:
            $ref: '#/components/schemas/CartItem'

    PaymentResponse:
        type: object
        properties:
          url:
            type: string
            format: url
            description: URL to redirect the user to for payment processing.



paths:
  /api/heroSection:   # No longer taking slug as input
    get:
      summary: Get Hero Section Content
      description: |
        Retrieves hero section content based on the provided slug.  Now uses a query parameter.
      parameters:
        - in: query
          name: slug
          required: true
          schema:
            type: string
          description: The product slug.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HeroSection'
        '400':
          description: Bad Request (e.g., missing slug)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'


  /api/benefitsSection:
    get:
      summary: Get Benefits Section Content
      parameters:
        - in: query
          name: slug
          required: true
          schema:
            type: string
          description: The product slug.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BenefitsSection'
        '400':
          description: Bad Request (e.g., missing slug)
        '500':
           description: Internal Server Error

  /api/featuresSection:
    get:
      summary: Get Features Section Content
      parameters:
        - in: query
          name: slug
          required: true
          schema:
            type: string
          description: Product Slug
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeaturesSection'
        '400':
           description: bad request
        '500':
          description: Internal Server Error
  /api/servicesSection:
    get:
      summary: Get Services Section Content
      parameters:
         - in: query
           name: slug
           required: true
           schema:
             type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                 $ref: '#/components/schemas/ServicesSection'
        '400':
          description: bad request
        '500':
          description: Internal Server Error
  /api/faqSection:
    get:
      summary: Get FAQ Section Content
      parameters:
        - in: query
          name: slug
          required: true
          schema:
            type: string
          description: The product slug.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FAQSection'
        '400':
            description: bad request
        '500':
            description: Internal Server Error

  /api/pricingSection:
    get:
      summary: Get Pricing Section Content
      parameters:
        - in: query
          name: slug
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PricingSection'
        '400':
          description: Bad Request (e.g. missing slug)
        '500':
            description: Internal server error
  /api/testimonialSection:
    get:
      summary: Get Testimonial Section
      parameters:
        - in: query
          name: slug
          required: true
          schema:
             type: string
      responses:
        '200':
           description: Testimonials
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/TestimonialSection'
        '400':
          description: Bad request
        '500':
          description: Internal Server Error
  /api/payment:
    post:
      summary: Initiate Payment
      description: Initiates a payment process with a third-party payment provider.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRequest'
      responses:
        '200':
          description: Payment initiated successfully. Returns the payment URL.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentResponse'
        '400':
          description: Bad Request (missing required fields)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/products:   # New endpoint for fetching product data
    get:
      summary: Get Products
      description: Retrieves a list of products with their associated data.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GraphQLResponse'
        '500':
          description: Internal server error.


  /api/auth/{...nextauth}/*:
    get:
      summary: NextAuth.js handler (GET)
      description: Handles authentication requests (sign-in, sign-out, session, etc.) via NextAuth.js.  The actual implementation is handled by NextAuth.js and the configured providers (Keycloak, Credentials).
      responses:
        '200':
          description: OK
        '302':
          description: Redirect (e.g., after sign-in or sign-out)
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

    post:
      summary: NextAuth.js handler (POST)
      description: Handles authentication requests (sign-in, sign-out, session, etc.) via NextAuth.js.
      responses:
        '200':
          description: OK
        '302':
          description: Redirect
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /checkout/confirm:  # No changes needed, handles webhook and redirect
    post:
      summary: Payment confirmation webhook.
      description: |
        Receives payment confirmation data from the payment provider (e.g., Razorpay).
        Redirects the user to the /checkout/confirmed page with payment details as query parameters.
      responses:
        '303':
          description: See Other. Redirects to /checkout/confirmed with payment details.
        '500':
          description: Internal server error

    get: #Handles redirect from payment
      summary: Handles Redirect from Payment Processor
      description: Redirect to confirmed page
      responses:
        '302':
          description: Redirection
        '500':
          description: Internal Server Error