/**
 * Service for customer-related operations including interactions with Lago API
 */

import { UserData } from "./userService";
import { LAGO_API_URL } from "../constants/api";

const CUSTOMER_URL = `${LAGO_API_URL}/customers`;

export interface CustomerData extends UserData {
  billing_configuration?: {
    payment_provider?: string;
    provider_customer_id?: string;
    invoice_grace_period?: number;
    payment_provider_code?: string;
    provider_payment_methods?: string[];
    gateway_customer_id?: string;
  };
  billing_address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  };
  tax_identification_number?: string;
  metadata?: Record<string, string>;
  payment_provider?: string;
  provider_customer_id?: string;
}

/**
 * Get or create a customer in the billing system
 * @param userId - The user's external ID (Keycloak ID)
 * @param customerData - Optional customer data to create/update
 * @returns The customer data
 */
export async function getOrCreateCustomer(
  userId: string,
  customerData?: Partial<CustomerData>
): Promise<CustomerData> {
  try {
    // First try to get the existing customer
    const existingCustomer = await getCustomer(userId);
    if (existingCustomer) {
      // If customer exists and we have update data, update the customer
      if (customerData) {
        return await updateCustomer(userId, customerData);
      }
      return existingCustomer;
    }

    // If customer doesn't exist, create a new one
    return await createCustomer({
      external_id: userId,
      ...customerData,
      // Ensure payment provider is set
      payment_provider: customerData?.payment_provider || 'razorpay',
      // Set default billing configuration if not provided
      billing_configuration: {
        payment_provider: 'razorpay',
        payment_provider_code: 'rzp',
        provider_payment_methods: ['card', 'netbanking', 'upi'],
        ...customerData?.billing_configuration
      }
    });
  } catch (error) {
    console.error('Error in getOrCreateCustomer:', error);
    throw new Error('Failed to get or create customer');
  }
}

/**
 * Get a customer by their external ID
 * @param externalId - The customer's external ID (Keycloak ID)
 */
async function getCustomer(externalId: string): Promise<CustomerData | null> {
  try {
    const response = await fetch(`${CUSTOMER_URL}/${externalId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to get customer: ${response.statusText}`);
    }

    const data = await response.json();
    return data.customer;
  } catch (error) {
    console.error('Error getting customer:', error);
    return null;
  }
}

/**
 * Create a new customer
 * @param customerData - The customer data to create
 */
async function createCustomer(customerData: Partial<CustomerData>): Promise<CustomerData> {
  try {
    const response = await fetch(CUSTOMER_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customer: customerData
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create customer: ${response.statusText}`);
    }

    const data = await response.json();
    return data.customer;
  } catch (error) {
    console.error('Error creating customer:', error);
    throw new Error('Failed to create customer');
  }
}

/**
 * Update an existing customer
 * @param externalId - The customer's external ID (Keycloak ID)
 * @param customerData - The customer data to update
 */
async function updateCustomer(
  externalId: string,
  customerData: Partial<CustomerData>
): Promise<CustomerData> {
  try {
    const response = await fetch(`${CUSTOMER_URL}/${externalId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        customer: customerData
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to update customer: ${response.statusText}`);
    }

    const data = await response.json();
    return data.customer;
  } catch (error) {
    console.error('Error updating customer:', error);
    throw new Error('Failed to update customer');
  }
}

/**
 * Delete a customer
 * @param externalId - The customer's external ID (Keycloak ID)
 */
export async function deleteCustomer(externalId: string): Promise<void> {
  try {
    const response = await fetch(`${CUSTOMER_URL}/${externalId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete customer: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error deleting customer:', error);
    throw new Error('Failed to delete customer');
  }
}

/**
 * Get all customers
 * @param page - Page number for pagination
 * @param perPage - Number of items per page
 */
export async function getCustomers(page = 1, perPage = 10): Promise<CustomerData[]> {
  try {
    const response = await fetch(
      `${CUSTOMER_URL}?page=${page}&per_page=${perPage}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to get customers: ${response.statusText}`);
    }

    const data = await response.json();
    return data.customers;
  } catch (error) {
    console.error('Error getting customers:', error);
    throw new Error('Failed to get customers');
  }
}

/**
 * Convert checkout form data to customer data
 * @param formData - The checkout form data
 * @param userId - The user's Keycloak ID
 */
export function convertFormToCustomerData(
  formData: {
    fullName: string;
    email: string;
    phone: string;
    address1: string;
    address2?: string;
    city: string;
    state: string;
    pincode: string;
    gstNumber?: string;
  },
  userId: string
): Partial<CustomerData> {
  return {
    external_id: userId,
    name: formData.fullName,
    email: formData.email,
    phone: formData.phone,
    billing_address: {
      line1: formData.address1,
      line2: formData.address2,
      city: formData.city,
      state: formData.state,
      postal_code: formData.pincode,
      country: 'IN'
    },
    tax_identification_number: formData.gstNumber,
    payment_provider: 'razorpay',
    billing_configuration: {
      payment_provider: 'razorpay',
      payment_provider_code: 'rzp',
      provider_payment_methods: ['card', 'netbanking', 'upi']
    }
  };
}