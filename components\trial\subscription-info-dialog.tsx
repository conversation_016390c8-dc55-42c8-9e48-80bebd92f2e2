"use client";

import { useState, useEffect } from "react";
import { <PERSON>R<PERSON>, CheckCircle, CreditCard, Info, XCircle, Clock, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { getProductRedirectURL, getProductDisplayName } from "@/src/services/productService";

interface SubscriptionInfoDialogProps {
  open: boolean;
  onClose: () => void;
  productName: string;
  // eslint-disable-next-line no-unused-vars
  productSlug?: string;
  currentPlan: {
    isPaidPlan: boolean;
    isTrialPlan: boolean;
    planName?: string;
    planExpiration?: string;
    isActive: boolean;
    isExpired: boolean;
    daysRemaining?: number;
  };
  onViewPricing: () => void;
  onGoToDashboard: () => void;
}

export function SubscriptionInfoDialog({
  open,
  onClose,
  productName,
  // eslint-disable-next-line no-unused-vars
  productSlug,
  currentPlan,
  onViewPricing,
  onGoToDashboard,
}: SubscriptionInfoDialogProps) {
  const [productRedirectURL, setProductRedirectURL] = useState<string | null>(null);
  const [displayName, setDisplayName] = useState<string>(productName);

  // Fetch product information when dialog opens
  useEffect(() => {
    if (open && productSlug) {
      const fetchProductInfo = async () => {
        try {
          // Fetch redirect URL
          const url = await getProductRedirectURL(productSlug);
          setProductRedirectURL(url);

          // Fetch display name
          const name = await getProductDisplayName(productSlug);
          if (name) {
            setDisplayName(name);
          }
        } catch (error) {
          console.error("Error fetching product information:", error);
        }
      };

      fetchProductInfo();
    }
  }, [open, productSlug, productName]);

  const handleClose = () => {
    onClose();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Ongoing";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Calculate days remaining if there's an expiration date
  const getDaysRemaining = (dateString?: string) => {
    if (!dateString) return null;

    const now = new Date();
    const expirationDate = new Date(dateString);
    const diffTime = expirationDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
  };

  // We're keeping this call but not using its result directly
  // as the plan already has daysRemaining
  getDaysRemaining(currentPlan.planExpiration);

  // Render trial plan content with upgrade options
  const renderTrialContent = () => {
    // If trial is active
    if (currentPlan.isActive) {
      return (
        <div className="space-y-4">
          <div className="rounded-lg bg-blue-50 dark:bg-blue-950 p-4 text-blue-800 dark:text-blue-200 border border-blue-100 dark:border-blue-900 shadow-sm">
            <h4 className="font-medium flex items-center text-base">
              <Info className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
              Trial Plan
            </h4>
            <p className="text-sm mt-2 text-blue-700 dark:text-blue-300">
              {currentPlan.daysRemaining && currentPlan.daysRemaining > 1 ? (
                <>Your trial has <span className="font-semibold">{currentPlan.daysRemaining} days</span> remaining.</>
              ) : currentPlan.daysRemaining === 1 ? (
                <span className="font-semibold text-amber-600 dark:text-amber-400">Your trial expires today!</span>
              ) : (
                "Upgrade to continue using all features."
              )}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between items-center gap-3 mt-2">
            <Button
              onClick={onViewPricing}
              className="w-full sm:w-auto font-medium group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-sm hover:shadow transition-all duration-150"
              size="lg"
            >
              Upgrade to paid plan
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button
              variant="outline"
              className="w-full sm:w-auto border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-150"
              size="lg"
              onClick={() => {
                window.open(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`);
                onGoToDashboard();
              }}
            >
              Go to dashboard
            </Button>
          </div>
        </div>
      );
    }

    // If trial is expired
    return (
      <div className="space-y-4">
        <div className="rounded-lg bg-amber-50 dark:bg-amber-950 p-4 text-amber-800 dark:text-amber-200 border border-amber-100 dark:border-amber-900 shadow-sm">
          <h4 className="font-medium flex items-center text-base">
            <Info className="h-5 w-5 mr-2 text-amber-600 dark:text-amber-400" />
            Trial Expired
          </h4>
          <p className="text-sm mt-2 text-amber-700 dark:text-amber-300">
            Upgrade to a paid plan to continue using all features.
          </p>
        </div>

        <div className="flex justify-center mt-2">
          <Button
            onClick={onViewPricing}
            className="w-full sm:w-auto font-medium group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-sm hover:shadow transition-all duration-200"
            size="lg"
          >
            Upgrade Now
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    );
  };

  // Render paid plan content with dashboard option
  const renderPaidContent = () => {
    // If paid subscription is active
    if (currentPlan.isActive) {
      return (
        <div className="space-y-4">
          <div className="rounded-lg bg-green-50 dark:bg-green-950 p-4 text-green-800 dark:text-green-200 border border-green-100 dark:border-green-900 shadow-sm">
            <h4 className="font-medium flex items-center text-base">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600 dark:text-green-400" />
              Active Subscription
            </h4>
            <p className="text-sm mt-2 text-green-700 dark:text-green-300">
              You have full access to all features.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row sm:justify-between items-center gap-3 mt-2">
            <Button
              className="w-full sm:w-auto font-medium group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-sm hover:shadow transition-all duration-200"
              size="lg"
              onClick={() => {
                window.open(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`);
                onGoToDashboard();
              }}
            >
              Go to dashboard
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button
              variant="outline"
              className="w-full sm:w-auto border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-200 flex items-center"
              size="lg"
              onClick={() => {
                if (productRedirectURL) {
                  window.open(productRedirectURL);
                } else {
                  window.open(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`);
                }
                onViewPricing();
              }}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              Go to {displayName}
            </Button>
          </div>
        </div>
      );
    }

    // If paid subscription is expired
    return (
      <div className="space-y-4">
        <div className="rounded-lg bg-red-50 dark:bg-red-950 p-4 text-red-800 dark:text-red-200 border border-red-100 dark:border-red-900 shadow-sm">
          <h4 className="font-medium flex items-center text-base">
            <Info className="h-5 w-5 mr-2 text-red-600 dark:text-red-400" />
            Subscription Expired
          </h4>
          <p className="text-sm mt-2 text-red-700 dark:text-red-300">
            Renew your subscription to regain access to all features.
          </p>
        </div>

        <div className="flex justify-center mt-2">
          <Button
            onClick={onViewPricing}
            className="w-full sm:w-auto font-medium group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-sm hover:shadow transition-all duration-200"
            size="lg"
          >
            Renew Subscription
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    );
  };

  // Choose appropriate content based on subscription status
  const renderSubscriptionContent = () => {
    if (currentPlan.isTrialPlan) {
      return renderTrialContent();
    } else if (currentPlan.isPaidPlan) {
      return renderPaidContent();
    }

    // Default fallback content
    return (
      <div className="space-y-4">
        <div className="rounded-lg bg-muted p-4 border border-muted/50 shadow-sm">
          <h4 className="font-medium text-base flex items-center">
            <Info className="h-5 w-5 mr-2 text-primary/70" />
            Subscription Details
          </h4>
          <p className="text-sm mt-2 text-muted-foreground">
            View your subscription details or explore available plans.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row sm:justify-between items-center gap-3 mt-2">
          <Button
            onClick={onViewPricing}
            className="w-full sm:w-auto font-medium group bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-sm hover:shadow transition-all duration-200"
            size="lg"
          >
            View plans
            <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Button>

          <Button
            variant="outline"
            className="w-full sm:w-auto border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-200"
            size="lg"
            onClick={() => {
              window.open(`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`);
              onGoToDashboard();
            }}
          >
            Go to dashboard
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="px-6 pt-6 pb-2 bg-gradient-to-r from-primary/10 to-primary/5 rounded-t-lg">
          <DialogTitle className="text-2xl flex items-center font-bold">
            <Info className="h-6 w-6 mr-2 text-primary" />
            {currentPlan.isPaidPlan
              ? "You're Already Subscribed"
              : "Your Subscription Information"
            }
          </DialogTitle>
          <DialogDescription className="text-base opacity-90">
            {currentPlan.isPaidPlan
              ? `You already have a subscription to ${displayName}`
              : `You have trial access to ${displayName}`
            }
          </DialogDescription>
        </DialogHeader>

        <div className="p-6 space-y-6">
          <Card className="border-0 shadow-sm overflow-hidden">
            <CardContent className="pt-6 px-6">
              <div className="space-y-5">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-xl font-semibold">{currentPlan.planName || "Your Plan"}</h3>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {currentPlan.isActive ? (
                        <Badge className="bg-green-500/10 text-green-600 hover:bg-green-500/20 py-1 px-2.5">
                          <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                          Active
                        </Badge>
                      ) : (
                        <Badge variant="destructive" className="py-1 px-2.5">
                          <XCircle className="h-3.5 w-3.5 mr-1.5" />
                          Expired
                        </Badge>
                      )}

                      {currentPlan.isTrialPlan && (
                        <Badge className="bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 py-1 px-2.5">
                          <Clock className="h-3.5 w-3.5 mr-1.5" />
                          Trial Plan
                        </Badge>
                      )}

                      {currentPlan.isPaidPlan && currentPlan.isActive && (
                        <Badge className="bg-green-500/10 text-green-600 hover:bg-green-500/20 py-1 px-2.5">
                          <CreditCard className="h-3.5 w-3.5 mr-1.5" />
                          Paid Subscription
                        </Badge>
                      )}
                    </div>
                  </div>

                  {currentPlan.planExpiration && (
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {currentPlan.isActive ? "Expires on" : "Expired on"}
                      </div>
                      <div className="text-muted-foreground text-sm font-medium">{formatDate(currentPlan.planExpiration)}</div>
                      {currentPlan.daysRemaining !== undefined && currentPlan.isActive && (
                        <div className="text-sm mt-1.5 font-medium">
                          {currentPlan.daysRemaining > 1 ? (
                            <span className="text-amber-500 px-2 py-0.5 rounded-full bg-amber-50 dark:bg-amber-950">{currentPlan.daysRemaining} days remaining</span>
                          ) : currentPlan.daysRemaining === 1 ? (
                            <span className="text-red-500 px-2 py-0.5 rounded-full bg-red-50 dark:bg-red-950">1 day remaining</span>
                          ) : (
                            <span className="text-red-500 px-2 py-0.5 rounded-full bg-red-50 dark:bg-red-950">Expires today</span>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <Separator className="my-2" />

                {renderSubscriptionContent()}

                {/* Features summary - Commented out to keep dialog minimalist
                <div className="mt-6 bg-muted/30 p-4 rounded-lg">
                  <h4 className="text-sm font-medium mb-3 flex items-center">
                    <BarChart3 className="h-4 w-4 mr-2 text-primary" />
                    Plan features
                  </h4>
                  <ul className="grid grid-cols-2 gap-3">
                    <li className="text-sm flex items-center text-muted-foreground hover:text-foreground transition-colors">
                      <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />
                      Full access to dashboard
                    </li>
                    <li className="text-sm flex items-center text-muted-foreground hover:text-foreground transition-colors">
                      <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />
                      Customer support
                    </li>
                    <li className="text-sm flex items-center text-muted-foreground hover:text-foreground transition-colors">
                      <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />
                      Regular updates
                    </li>
                    <li className="text-sm flex items-center text-muted-foreground hover:text-foreground transition-colors">
                      <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />
                      Product integrations
                    </li>
                  </ul>
                </div>
                */}
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}