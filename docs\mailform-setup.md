# MailForm Integration Guide

This document provides instructions for setting up and configuring the MailForm service for contact forms in the application.

## Overview

MailForm is a lightweight self-hosted email service designed for contact forms. It provides:

- Access via API or HTML form with redirects
- Configurable CORS and Origin restriction
- ReCaptcha and hCaptcha support
- Custom rate limits for every target
- Optional API keys

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in the root of the project with the following variables:

```
# MailForm Configuration
MAILFORM_URL=https://mailform.cubeone.in

# Optional: Common CAPTCHA Configuration
# CAPTCHA_PROVIDER=recaptcha
# CAPTCHA_SECRET=your-captcha-secret-key
```

Replace `your-api-key-here` with your actual MailForm API key.

### 2. MailForm Server Configuration

The MailForm server needs to be configured with a target for the contact form. Here's an example configuration:

#### Using JSON Configuration

Create a file named `contact.json` in the MailForm targets directory:

```json
{
  "smtp": "smtps://username:<EMAIL>",
  "origin": "your-website-domain.com",
  "recipients": ["<EMAIL>"],
  "from": "<EMAIL>",
  "subjectPrefix": "[Contact Form] ",
  "templateFile": "contact-email-template.html",
  "rateLimit": {
    "timespan": 300,
    "requests": 1
  },
  "captcha": {
    "provider": "recaptcha",
    "secret": "your-recaptcha-secret-key"
  }
}
```

**Note:** The `templateFile` property points to the HTML email template file that should be placed in the MailForm templates directory. We've created this template at `public/templates/contact-email-template.html` in our project.

#### Using Environment Variables

Alternatively, you can configure the target using environment variables:

```
TARGET_CONTACT_SMTP=smtps://username:<EMAIL>
TARGET_CONTACT_ORIGIN=your-website-domain.com
TARGET_CONTACT_RECIPIENTS=<EMAIL>
TARGET_CONTACT_FROM=<EMAIL>
TARGET_CONTACT_SUBJECTPREFIX=[Contact Form]
TARGET_CONTACT_TEMPLATEFILE=contact-email-template.html
TARGET_CONTACT_RATELIMIT_TIMESPAN=300
TARGET_CONTACT_RATELIMIT_REQUESTS=1
TARGET_CONTACT_CAPTCHA_PROVIDER=recaptcha
TARGET_CONTACT_CAPTCHA_SECRET=your-recaptcha-secret-key
```

**Important:** Make sure to place the `contact-email-template.html` file in the MailForm templates directory.

### 3. CAPTCHA Setup (Required)

The contact form uses Google's reCAPTCHA v2 Checkbox for spam protection. To set it up:

1. Sign up for [Google reCAPTCHA](https://www.google.com/recaptcha/admin)
2. Choose reCAPTCHA v2 Checkbox
3. Add your domain(s) to the allowed domains list
4. Get your site key and secret key
5. Add the keys to your environment variables, making sure to keep the secret key server-side only:

```
# Client-side (public) - can be exposed in the browser
NEXT_PUBLIC_RECAPTCHA_SITE_KEY=your-recaptcha-site-key

# Server-side (private) - NEVER expose these on the client
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key
CAPTCHA_PROVIDER=recaptcha
```

**Important Security Note:** Never expose the secret key on the client side. The secret key should only be used on the server.

#### How reCAPTCHA is Implemented

The contact form uses reCAPTCHA v2 Checkbox, which:

1. Requires explicit user interaction (checking the "I'm not a robot" box)
2. Provides a pass/fail verification
3. Is more visible to users and provides clear feedback

The implementation:

- Loads the reCAPTCHA script when the contact form component mounts
- Displays the reCAPTCHA checkbox in the form
- Verifies that the user has completed the reCAPTCHA challenge before submission
- Sends the verification token to the server with the form data
- The server forwards the token to the MailForm service for verification

#### Testing reCAPTCHA

For testing purposes, you can use Google's test keys:

- Site Key (client-side): `6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI`
- Secret Key (server-side only): `6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe`

**Security Warning:**
- These keys will always pass verification but should not be used in production
- Never expose the secret key in client-side code or public repositories
- The secret key should only be used in server-side code and environment variables

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure the `origin` in your MailForm target configuration matches your website's domain.

2. **Email Not Sending**: Check the SMTP configuration and credentials.

3. **Rate Limiting**: The default rate limit is 1 request per 5 minutes (300 seconds). Adjust as needed.

### Debugging

The contact form service includes detailed logging. Check the server logs for any errors or issues.

## Additional Resources

- [MailForm GitHub Repository](https://github.com/Feuerhamster/mailform)
- [Nodemailer Documentation](https://nodemailer.com/about/)
- [ReCAPTCHA Documentation](https://developers.google.com/recaptcha/docs/display)
- [hCAPTCHA Documentation](https://docs.hcaptcha.com/)
