import { NextRequest, NextResponse } from 'next/server';
import { getKeycloakAdminToken, checkExistingUser } from '../register/route';

export async function POST(request: NextRequest) {
  try {
    const { username } = await request.json();

    if (!username) {
      return NextResponse.json({ success: false, message: 'Username is required.' }, { status: 400 });
    }

    const adminToken = await getKeycloakAdminToken();
    if (!adminToken) {
      return NextResponse.json({ success: false, message: 'Could not connect to authentication service.' }, { status: 500 });
    }

    const existingUser = await checkExistingUser(adminToken, { username });

    if (existingUser.exists) {
      return NextResponse.json({
        success: false,
        field: 'username',
        message: 'This username is already taken. Please choose another.',
      }, { status: 409 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('[VALIDATE_STEP2] Error:', error);
    return NextResponse.json({ success: false, message: 'An unexpected error occurred.' }, { status: 500 });
  }
} 