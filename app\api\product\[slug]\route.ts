import { NextRequest, NextResponse } from "next/server";
import { getProductBySlug } from "@/src/services/productService";

/**
 * GET handler for /api/product/[slug]
 * Returns product information for a product identified by slug
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        { error: "Product slug is required" },
        { status: 400 }
      );
    }

    const productData = await getProductBySlug(slug);

    if (!productData) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(productData);
  } catch (error) {
    console.error("Error fetching product data:", error);
    return NextResponse.json(
      { error: "Failed to fetch product data" },
      { status: 500 }
    );
  }
}
