import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_FAQ_CONTENT_BY_SLUG = gql`
  query FaqBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      sections {
        ... on ComponentSectionsFaqSection {
          id
          title
          faqComponent {
            id
            question
            answer
          }
        }
      }
    }
  }
`;

export const getFaqContentBySlug = async (slug: string) => {
  try {
    const { data } = await client.query({
      query: GET_FAQ_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    const faqSection = data.products[0]?.sections.find(
      (section: { __typename: string }) => section.__typename === "ComponentSectionsFaqSection"
    );
    return faqSection || null;
  } catch (error) {
    console.error("Error fetching FAQ section data:", error);
    throw error;
  }
};
