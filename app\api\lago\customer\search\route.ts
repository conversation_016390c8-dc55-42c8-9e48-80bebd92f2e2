import { NextRequest, NextResponse } from "next/server";
import { safeFetch } from "@/lib/fetch-utils";
import { LAGO_API_URL, corsHeaders } from "@/src/constants/api";

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// GET route to search for customers by email
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const email = searchParams.get("email");
    
    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400, headers: corsHeaders }
      );
    }
    
    // Build the URL with the query params
    let url = `${LAGO_API_URL}/customers`;
    if (email) {
      url += `?external_id=${encodeURIComponent(email)}`;
    }
    
    const response = await safeFetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch customers: ${response.status}`);
      return NextResponse.json(
        { error: "Failed to fetch customers" },
        { status: response.status, headers: corsHeaders }
      );
    }
    
    const data = await response.json();
    
    // Find customer with matching email
    if (data && data.customers) {
      const customer = data.customers.find((c: { email: string }) => 
        c.email && c.email.toLowerCase() === email.toLowerCase()
      );
      
      if (customer) {
        return NextResponse.json({ customer }, { headers: corsHeaders });
      } else {
        return NextResponse.json(
          { error: "No customer found with this email" },
          { status: 404, headers: corsHeaders }
        );
      }
    }
    
    return NextResponse.json(
      { error: "No customers found" },
      { status: 404 }
    );
  } catch (error) {
    console.error("Error searching for Lago customer:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500, headers: corsHeaders }
    );
  }
}
