"use client";
import React, { useEffect, useState } from "react";
import Link from 'next/link';
import <PERSON><PERSON>ead<PERSON> from "../section-header";
import SectionContainer from "../section-container";
import { ShiftCardDemo } from "@/components/ui/shift-card-demo";
import { InteractiveHoverButton } from "@/components/ui/interactive-hover-button";
import { Loader2 } from "lucide-react";

// Import Apollo client but with error handling
import { ApolloClient, NormalizedCacheObject, gql } from '@apollo/client';

// Function to get Apollo client dynamically
async function getApolloClient(): Promise<ApolloClient<NormalizedCacheObject> | undefined> {
  try {
    const apolloModule = await import("@/lib/apolloClient");
    return apolloModule.default;
  } catch (error) {
    console.error("Failed to import Apollo client:", error);
    return undefined;
  }
}

// Define the GraphQL query to match the ShiftCardDemo interface
const GET_PRODUCTS_QUERY = `
  query GetProducts {
    products {
      ProductCard {
        productCategory
        productName
        productLogo {
          url
        }
        productRedirectURL
        id
        productDescription
        label
      }
    }
  }
`;

interface Product {
  ProductCard: {
    productCategory: string;
    productName: string;
    productLogo: {
      url: string;
    };
    productRedirectURL: string | null;
    id: string;
    productDescription: string;
    label: string | null;
  };
}

export function ProductsSection() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        // First try to use Apollo Client if available
        const client = await getApolloClient();
        if (client) {
          try {
            const { data } = await client.query({
              query: gql`${GET_PRODUCTS_QUERY}`,
              fetchPolicy: "no-cache",
            });
            setProducts(data.products);
            return; // Exit early if successful
          } catch (apolloError) {
            console.error('Apollo client error:', apolloError);
            // Fall through to fetch API approach
          }
        }

        // Fallback to standard fetch API if Apollo fails or isn't available
        const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_API_URL;
        if (!apiUrl) {
          throw new Error('GraphQL API URL not configured');
        }
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: GET_PRODUCTS_QUERY,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.errors) {
          throw new Error(result.errors[0].message);
        }

        setProducts(result.data.products);
      } catch (error) {
        console.error('Failed to load products:', error);
        setError('Failed to load products. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  if (loading) {
    return (
      <SectionContainer id="products">
        <SectionHeader
          subTitle="Products"
          title="Our Exclusive Products"
          description="Explore our range of innovative products designed to meet your needs and exceed your expectations."
        />
        <div className="flex justify-center items-center min-h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </SectionContainer>
    );
  }

  if (error) {
    return (
      <SectionContainer id="products">
        <SectionHeader
          subTitle="Products"
          title="Our Exclusive Products"
          description="Explore our range of innovative products designed to meet your needs and exceed your expectations."
        />
        <div className="text-center text-red-500 my-8">{error}</div>
      </SectionContainer>
    );
  }

  const displayedProducts = products.slice(0, 4);

  return (
    <SectionContainer id="products">
      <SectionHeader
        subTitle="Products"
        title="Our Exclusive Products"
        description="Explore our range of innovative products designed to meet your needs and exceed your expectations."
      />
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 justify-items-center my-8">
        {displayedProducts.map((product) => (
          <ShiftCardDemo
            key={product.ProductCard.id}
            product={product}
            onViewProduct={() => {
              try {
                // Create slug from product name
                const productSlug = product.ProductCard.productName
                  .toLowerCase()
                  .replace(/[^\w\s-]/g, '') // Remove special characters
                  .replace(/\s+/g, '-'); // Replace spaces with hyphens

                window.location.href = `/products/${productSlug}`;
              } catch (error) {
                console.error("Navigation error:", error);
              }
            }}
            onBuyNow={() => {
              try {
                // Create slug from product name
                const productSlug = product.ProductCard.productName
                  .toLowerCase()
                  .replace(/[^\w\s-]/g, '') // Remove special characters
                  .replace(/\s+/g, '-'); // Replace spaces with hyphens

                window.location.href = `/products/${productSlug}#pricing-section`;
              } catch (error) {
                console.error("Navigation error:", error);
              }
            }}
          />
        ))}
      </div>
      
      {products.length > 3 && (
        <div className="flex justify-center mt-8">
          <Link href="/products">
            <InteractiveHoverButton text="View All Products" />
          </Link>
        </div>
      )}
    </SectionContainer>
  );
}
