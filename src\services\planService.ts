/**
 * Plan Service
 * 
 * Handles all interactions with subscription plans.
 * Provides functions to fetch, filter, and compare plans.
 */
import { getPricingContentBySlug } from "@/app/api/pricingSection";
import { extractProductCode } from '@/src/utils/plan-utils';

// Types
export interface Feature {
  feature: string;
  isIncluded: boolean;
  id?: string;
}

export interface PlanPrice {
  monthly: number;
  yearly: number;
  savings?: number;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  monthlyPricing: number;
  yearlyPricing: number;
  plan_code_monthly: string;
  plan_code_yearly: string;
  description: string;
  tag?: string;
  features: Feature[];
  button?: {
    text: string;
    id?: string;
  };
}

export interface TrialPlan {
  id: string;
  name: string;
  trialDurationInDays: number;
  plan_code: string;
  description?: string;
  tag?: string;
  features: Feature[];
  button?: {
    text: string;
    id?: string;
  };
}

export interface EnterprisePlan {
  id: string;
  name: string;
  description: string;
  tag?: string;
  features: Feature[];
  button?: {
    text: string;
    id?: string;
  };
}

export interface PricingData {
  id: string;
  heading: string;
  title: string;
  subscriptionPlan: SubscriptionPlan[];
  trialPlan?: TrialPlan;
  enterprisePlan?: EnterprisePlan;
  productInfo?: {
    productName: string;
    productLogo?: string;
  };
}

/**
 * Fetches pricing data for a specific product slug
 * @param productSlug The product slug to fetch plans for
 * @returns Product pricing data including all plan types
 */
export async function getProductPlans(productSlug: string): Promise<PricingData | null> {
  if (!productSlug) {
    console.error("❌ PLAN SERVICE - No product slug provided");
    return null;
  }
  
  try {
    const pricingSectionData = await getPricingContentBySlug(productSlug);
    
    if (!pricingSectionData) {
      console.error(`❌ PLAN SERVICE - No pricing data found for product: ${productSlug}`);
      return null;
    }
    
    // Extract and normalize subscription plans
    const normalizedData: PricingData = {
      id: pricingSectionData.id,
      heading: pricingSectionData.heading,
      title: pricingSectionData.title,
      subscriptionPlan: Array.isArray(pricingSectionData.subscriptionPlan)
        ? pricingSectionData.subscriptionPlan.map((plan: { fetaures?: unknown[]; [key: string]: unknown }) => ({
            ...plan,
            features: normalizeFeatures(plan.fetaures || []),
          }))
        : [],
      productInfo: pricingSectionData.productInfo,
    };
    
    // Add trial plan if exists
    if (pricingSectionData.trialPlan) {
      normalizedData.trialPlan = {
        ...pricingSectionData.trialPlan,
        features: normalizeFeatures(pricingSectionData.trialPlan.features || [])
      };
    }
    
    // Add enterprise plan if exists
    if (pricingSectionData.enterprisePlan) {
      normalizedData.enterprisePlan = {
        ...pricingSectionData.enterprisePlan,
        features: normalizeFeatures(pricingSectionData.enterprisePlan.features || [])
      };
    }
    
    return normalizedData;
  } catch (error) {
    console.error(`❌ PLAN SERVICE - Error fetching product plans:`, error);
    return null;
  }
}

/**
 * Helper to normalize features array
 */
function normalizeFeatures(features: unknown[]): Feature[] {
  if (!Array.isArray(features)) return [];

  return features.map(feature => {
    const featureObj = feature as Record<string, unknown>;
    return {
      feature: typeof featureObj.feature === 'string' ? featureObj.feature : '',
      isIncluded: !!featureObj.isIncluded,
      id: typeof featureObj.id === 'string' ? featureObj.id : undefined
    };
  });
}

/**
 * Finds a specific plan by plan code
 * @param productSlug The product slug
 * @param planCode The plan code to find
 * @returns The plan if found, null otherwise
 */
export async function findPlanByCode(
  productSlug: string, 
  planCode: string
): Promise<SubscriptionPlan | TrialPlan | null> {
  if (!productSlug || !planCode) {
    console.error("❌ PLAN SERVICE - Missing parameters for finding plan");
    return null;
  }
  
  try {
    const pricingData = await getProductPlans(productSlug);
    if (!pricingData) return null;
    
    // Check regular subscription plans
    if (pricingData.subscriptionPlan && pricingData.subscriptionPlan.length > 0) {
      // Check monthly plan codes
      const monthlyPlan = pricingData.subscriptionPlan.find(plan => 
        plan.plan_code_monthly === planCode
      );
      if (monthlyPlan) return monthlyPlan;
      
      // Check yearly plan codes
      const yearlyPlan = pricingData.subscriptionPlan.find(plan => 
        plan.plan_code_yearly === planCode
      );
      if (yearlyPlan) return yearlyPlan;
    }
    
    // Check trial plan
    if (pricingData.trialPlan && pricingData.trialPlan.plan_code === planCode) {
      return pricingData.trialPlan;
    }
    
    console.log(`ℹ️ PLAN SERVICE - No plan found with code: ${planCode}`);
    return null;
  } catch (error) {
    console.error(`❌ PLAN SERVICE - Error finding plan:`, error);
    return null;
  }
}

/**
 * Calculates the price of a subscription based on its plan code
 * @param productSlug The product slug
 * @param planCode The plan code
 * @returns The price in cents, or null if plan not found
 */
export async function getPlanPrice(
  productSlug: string,
  planCode: string
): Promise<number | null> {
  try {
    const plan = await findPlanByCode(productSlug, planCode);
    if (!plan) return null;
    
    // Trial plans have zero price
    if ('trialDurationInDays' in plan) {
      return 0;
    }
    
    // Determine if it's a monthly or yearly plan
    if (planCode === plan.plan_code_monthly) {
      return plan.monthlyPricing * 100; // Convert to cents
    } else if (planCode === plan.plan_code_yearly) {
      return plan.yearlyPricing * 100; // Convert to cents
    }
    
    // If plan code doesn't match exact monthly/yearly codes
    // Try to infer from the code format
    if (planCode.includes('_monthly')) {
      return plan.monthlyPricing * 100;
    } else if (planCode.includes('_yearly') || planCode.includes('_annual')) {
      return plan.yearlyPricing * 100;
    }
    
    // Default to monthly if can't determine
    return plan.monthlyPricing * 100;
  } catch (error) {
    console.error(`❌ PLAN SERVICE - Error getting plan price:`, error);
    return null;
  }
}

/**
 * Compares two plans to determine if it's an upgrade, downgrade, or same tier
 * @param currentPlanCode The current plan code
 * @param newPlanCode The new plan code
 * @returns 1 for upgrade, -1 for downgrade, 0 for same tier, null for error
 */
export async function comparePlans(
  productSlug: string,
  currentPlanCode: string,
  newPlanCode: string
): Promise<1 | 0 | -1 | null> {
  if (!productSlug || !currentPlanCode || !newPlanCode) {
    console.error("❌ PLAN SERVICE - Missing parameters for plan comparison");
    return null;
  }
  
  try {
    // If same plan code, it's the same tier
    if (currentPlanCode === newPlanCode) return 0;
    
    // Get prices for both plans
    const currentPrice = await getPlanPrice(productSlug, currentPlanCode);
    const newPrice = await getPlanPrice(productSlug, newPlanCode);
    
    if (currentPrice === null || newPrice === null) {
      console.error(`❌ PLAN SERVICE - Could not determine prices for comparison`);
      return null;
    }
    
    // Trial is always considered lower than paid plans
    const isCurrentTrial = currentPlanCode.includes('_trial');
    const isNewTrial = newPlanCode.includes('_trial');
    
    if (isCurrentTrial && !isNewTrial) return 1; // Moving from trial to paid is upgrade
    if (!isCurrentTrial && isNewTrial) return -1; // Moving from paid to trial is downgrade
    
    // Compare prices
    if (newPrice > currentPrice) return 1; // Upgrade
    if (newPrice < currentPrice) return -1; // Downgrade
    return 0; // Same tier
  } catch (error) {
    console.error(`❌ PLAN SERVICE - Error comparing plans:`, error);
    return null;
  }
}

/**
 * Calculate savings percentage between monthly and yearly pricing
 * @param monthlyPrice Monthly price
 * @param yearlyPrice Yearly price
 * @returns Savings percentage or null if invalid input
 */
export function calculateYearlySavings(
  monthlyPrice: number,
  yearlyPrice: number
): number | null {
  if (monthlyPrice <= 0 || yearlyPrice <= 0) return null;
  
  // Calculate yearly equivalent of monthly price
  const yearlyEquivalent = monthlyPrice * 12;
  
  // Calculate savings
  const savingsAmount = yearlyEquivalent - yearlyPrice;
  const savingsPercentage = Math.round((savingsAmount / yearlyEquivalent) * 100);
  
  return savingsPercentage > 0 ? savingsPercentage : null;
}

/**
 * Gets available upgrade paths for a given plan
 * @param productSlug The product slug
 * @param currentPlanCode The current plan code
 * @returns Array of valid upgrade plans or null if error
 */
export async function getUpgradePaths(
  productSlug: string,
  currentPlanCode: string
): Promise<SubscriptionPlan[] | null> {
  if (!productSlug || !currentPlanCode) {
    console.error("❌ PLAN SERVICE - Missing parameters for upgrade paths");
    return null;
  }
  
  try {
    const pricingData = await getProductPlans(productSlug);
    if (!pricingData || !pricingData.subscriptionPlan) return null;
    
    const currentPrice = await getPlanPrice(productSlug, currentPlanCode);
    if (currentPrice === null) return null;
    
    // Extract product code to ensure we're comparing plans in the same product family
    const productCode = extractProductCode(currentPlanCode);
    
    // Filter plans that are more expensive (upgrades)
    const upgradePlans = pricingData.subscriptionPlan.filter(plan => {
      // Check if this plan belongs to the same product family
      const monthlyProductCode = extractProductCode(plan.plan_code_monthly);
      if (monthlyProductCode !== productCode) return false;
      
      // Check if either monthly or yearly price is higher than current plan
      const monthlyPrice = plan.monthlyPricing * 100;
      const yearlyPrice = plan.yearlyPricing * 100;
      
      // Higher price means it's an upgrade
      return monthlyPrice > currentPrice || yearlyPrice > currentPrice;
    });
    
    return upgradePlans.length > 0 ? upgradePlans : null;
  } catch (error) {
    console.error(`❌ PLAN SERVICE - Error getting upgrade paths:`, error);
    return null;
  }
} 