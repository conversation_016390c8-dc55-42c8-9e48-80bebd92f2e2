import { NextResponse } from "next/server";
import { getHealthStatus, getErrorHealthStatus, HealthCheckResponse } from "@/lib/health";

export async function GET(): Promise<NextResponse<HealthCheckResponse>> {
  try {
    const healthData = getHealthStatus();
    return NextResponse.json(healthData, { 
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  } catch (error) {
    console.error("Health check error:", error);
    
    const errorData = getErrorHealthStatus();
    return NextResponse.json(errorData, { 
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  }
} 