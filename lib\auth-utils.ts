"use client";

import { toast } from "sonner";
import { signIn, useSession } from "next-auth/react";
import { useEffect, useState } from "react";

/**
 * Authentication utility functions for consistent UX across the application
 */

export interface AuthOptions {
  message?: string;
  actionLabel?: string;
  returnUrl?: string;
  showToast?: boolean;
  toastDuration?: number;
  onSuccess?: () => void;
  onCancel?: () => void;
  storeData?: Record<string, string>;
}

/**
 * Default authentication messages for different scenarios
 */
export const AUTH_MESSAGES = {
  CART_CHECKOUT: "Please sign in to proceed with checkout",
  TRIAL_START: "Please sign in to start your free trial",
  SUBSCRIPTION_MANAGE: "Please sign in to manage your subscriptions",
  PROFILE_ACCESS: "Please sign in to access your profile",
  PAYMENT_REQUIRED: "Please sign in to complete your payment",
  GENERAL: "Please sign in to continue"
} as const;

/**
 * Enhanced authentication handler with consistent UX
 */
export function handleAuthRequired(options: AuthOptions = {}) {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    console.warn('handleAuthRequired called on server side');
    return;
  }

  const {
    message = AUTH_MESSAGES.GENERAL,
    actionLabel = "Sign In",
    returnUrl = window.location.href,
    showToast = true,
    toastDuration = 6000,
    onCancel,
    storeData
  } = options;

  // Store any additional data needed after authentication
  if (storeData) {
    Object.entries(storeData).forEach(([key, value]) => {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.warn(`Failed to store ${key} in localStorage:`, error);
      }
    });
  }

  // Store return URL
  try {
    localStorage.setItem("auth_return_url", returnUrl);
  } catch (error) {
    console.warn("Failed to store return URL:", error);
  }

  if (showToast) {
    toast.info(message, {
      duration: toastDuration,
      action: {
        label: actionLabel,
        onClick: () => {
          initiateSignIn(returnUrl);
        }
      },
      onDismiss: () => {
        onCancel?.();
      }
    });
  } else {
    initiateSignIn(returnUrl);
  }
}

/**
 * Initiate sign in with Keycloak
 */
export async function initiateSignIn(returnUrl?: string) {
  try {
    await signIn("keycloak", { 
      callbackUrl: returnUrl || window.location.href,
      redirect: true 
    });
  } catch (error) {
    console.error("Sign in error:", error);
    toast.error("Failed to initiate sign in. Please try again.");
  }
}

/**
 * Handle successful authentication return
 */
export function handleAuthReturn() {
  // Ensure we're on the client side
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    const returnUrl = localStorage.getItem("auth_return_url");
    if (returnUrl && returnUrl !== window.location.href) {
      localStorage.removeItem("auth_return_url");
      window.location.href = returnUrl;
      return true;
    }
  } catch (error) {
    console.warn("Failed to handle auth return:", error);
  }
  return false;
}

/**
 * Clean up authentication-related localStorage data
 */
export function cleanupAuthData(keys?: string[]) {
  const defaultKeys = [
    "auth_return_url",
    "trial_product_slug", 
    "trial_plan_code",
    "trial_form_data",
    "checkoutRedirectUrl"
  ];
  
  const keysToClean = keys || defaultKeys;
  
  keysToClean.forEach(key => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`Failed to remove ${key} from localStorage:`, error);
    }
  });
}

/**
 * Check if user should be redirected after authentication
 */
export function shouldRedirectAfterAuth(): { shouldRedirect: boolean; url?: string } {
  try {
    const returnUrl = localStorage.getItem("auth_return_url");
    const currentUrl = window.location.href;
    
    if (returnUrl && returnUrl !== currentUrl) {
      return { shouldRedirect: true, url: returnUrl };
    }
  } catch (error) {
    console.warn("Failed to check redirect after auth:", error);
  }
  
  return { shouldRedirect: false };
}

/**
 * Enhanced toast notifications for authentication scenarios
 */
export const authToasts = {
  signInRequired: (message?: string, actionLabel?: string) => {
    toast.info(message || AUTH_MESSAGES.GENERAL, {
      duration: 6000,
      action: {
        label: actionLabel || "Sign In",
        onClick: () => initiateSignIn()
      }
    });
  },
  
  signInSuccess: (message?: string) => {
    toast.success(message || "Successfully signed in!", {
      duration: 4000
    });
  },
  
  signInError: (message?: string) => {
    toast.error(message || "Failed to sign in. Please try again.", {
      duration: 6000
    });
  },
  
  sessionExpired: () => {
    toast.warning("Your session has expired. Please sign in again.", {
      duration: 8000,
      action: {
        label: "Sign In",
        onClick: () => initiateSignIn()
      }
    });
  },
  
  authRequired: (feature: string) => {
    toast.info(`Please sign in to access ${feature}`, {
      duration: 6000,
      action: {
        label: "Sign In",
        onClick: () => initiateSignIn()
      }
    });
  }
};

/**
 * Validation helpers
 */
export function isValidReturnUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    const currentOrigin = window.location.origin;

    // Only allow same-origin URLs for security
    return parsedUrl.origin === currentOrigin;
  } catch {
    return false;
  }
}

/**
 * Hook for handling authentication state changes
 */
export function useAuthHandler() {
  const { data: session, status } = useSession();
  const [hasHandledReturn, setHasHandledReturn] = useState(false);

  useEffect(() => {
    if (status === "authenticated" && !hasHandledReturn) {
      const { shouldRedirect, url } = shouldRedirectAfterAuth();
      if (shouldRedirect && url && isValidReturnUrl(url)) {
        setHasHandledReturn(true);
        cleanupAuthData();
        window.location.href = url;
      }
    }
  }, [status, hasHandledReturn]);

  return { session, status, hasHandledReturn };
}
