"use client";

import React, { useEffect, useCallback } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { LogOut, UserCheck } from "lucide-react";

interface LogoutConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  userName?: string;
}

/**
 * LogoutConfirmationDialog - A user-friendly confirmation dialog for logout
 * Prevents accidental logouts and provides clear actions
 */
export function LogoutConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  userName
}: LogoutConfirmationDialogProps) {
  const displayName = userName ? userName.split(' ')[0] : 'there';

  const handleConfirm = useCallback(() => {
    onOpenChange(false);
    onConfirm();
  }, [onOpenChange, onConfirm]);

  const handleCancel = () => {
    onOpenChange(false);
  };

  // Add keyboard shortcuts for better UX
  useEffect(() => {
    if (!open) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        handleConfirm();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open, handleConfirm]);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <LogOut className="h-6 w-6 text-destructive" />
          </div>
          <AlertDialogTitle className="text-center">
            Sign Out Confirmation
          </AlertDialogTitle>
          <AlertDialogDescription className="text-center">
            Hi {displayName}! Are you sure you want to sign out? 
            <br />
            <span className="text-muted-foreground text-sm mt-2 block">
              You&apos;ll need to sign in again to access your account and continue where you left off.
            </span>
            <br />
            <span className="text-muted-foreground text-xs mt-1 block">
              Tip: Press Ctrl+Enter to confirm quickly
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="flex-col sm:flex-row gap-2">
          <AlertDialogCancel 
            onClick={handleCancel}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            <UserCheck className="w-4 h-4 mr-2" />
            Stay Signed In
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="w-full sm:w-auto order-1 sm:order-2 bg-destructive hover:bg-destructive/90"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Yes, Sign Out
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 