import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_BENEFITS_CONTENT_BY_SLUG = gql`
  query BenefitsBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      sections {
        ... on ComponentSectionsBenefitsSection {
          id
          title
          description
          cards {
            description
            icon
            text
            id
          }
        }
      }
    }
  }
`;

export const getBenefitsContentBySlug = async (slug: string) => {
  try {
    const { data } = await client.query({
      query: GET_BENEFITS_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    const benefitsSection = data.products[0]?.sections.find(
      (section: { __typename: string }) => section.__typename === "ComponentSectionsBenefitsSection"
    );
    return benefitsSection || null;
  } catch (error) {
    console.error("Error fetching benefits section data:", error);
    throw error;
  }
};
