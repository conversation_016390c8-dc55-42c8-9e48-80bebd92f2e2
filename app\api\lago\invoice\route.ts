import { NextRequest, NextResponse } from 'next/server';
import { safeFetch } from "@/lib/fetch-utils";
import { LAGO_API_URL, LAGO_API_KEY, corsHeaders } from "@/src/constants/api";
import { sendInvoicePaymentEmailWithRetry } from "@/src/services/emailService";

// Constants for Lago API
const INVOICE_URL = `${LAGO_API_URL}/invoices`;

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

/**
 * Updates an invoice payment status to success
 *
 * This API endpoint is used to update invoice payment status to 'succeeded' after
 * a subscription is successfully activated.
 *
 * Workflow:
 * 1. After payment is successful, subscription is created in Lago
 * 2. Lago automatically generates an invoice for the subscription
 * 3. We use this API to mark the invoice as paid (succeeded)
 *
 * According to Lago API docs, the payment status update is done through a PUT request
 * to /invoices/:id with a payload containing the invoice.payment_status field.
 *
 * @see https://getlago.com/docs/api-reference/invoices/update-payment-status
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Check for required fields
    if (!body.invoice_id) {
      console.error("LAGO INVOICE API - Missing required fields:", {
        hasInvoiceId: !!body.invoice_id
      });
      return NextResponse.json(
        { error: "Missing required field: invoice_id is required" },
        { status: 400, headers: corsHeaders }
      );
    }

    console.log(`🔄 LAGO INVOICE API - Updating payment status to "succeeded" for invoice ID: ${body.invoice_id}`);

    // Generate idempotency key to prevent duplicate requests
    const idempotencyKey = `invoice_payment_${body.invoice_id}_${Date.now()}`;

    // Create payload to mark invoice as paid
    // According to Lago API docs, "succeeded" is the correct status for successful payments
    const payload = {
      invoice: {
        payment_status: "succeeded"
      }
    };

    console.log(`🔄 LAGO INVOICE API - Making request to Lago API:`, {
      url: `${INVOICE_URL}/${body.invoice_id}`,
      method: 'PUT',
      payload
    });

    // Make API call to Lago's invoice endpoint
    const response = await safeFetch(`${INVOICE_URL}/${body.invoice_id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`,
        'Idempotency-Key': idempotencyKey
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => response.text());
      console.error("LAGO INVOICE API - Failed to update invoice payment status:", {
        invoiceId: body.invoice_id,
        status: response.status,
        error: errorData
      });
      return NextResponse.json(
        { error: "Failed to update invoice payment status", details: errorData },
        { status: response.status, headers: corsHeaders }
      );
    }

    const responseData = await response.json();

    console.log("✅ LAGO INVOICE API - Successfully updated invoice payment status:", {
      invoice_id: body.invoice_id,
      status: "succeeded",
      response: responseData.invoice?.payment_status
    });

    // Send invoice payment confirmation email with retry mechanism
    const trackingId = request.headers.get('X-Tracking-ID') || `invoice_${Date.now()}`;
    console.log(`📧 LAGO INVOICE API [${trackingId}] - Processing invoice: ${body.invoice_id}`);
    console.log(`📧 LAGO INVOICE API [${trackingId}] - Using tracking ID for both invoice update and email`);

    // Store the lago_id from the response for email notification
    // CRITICAL: This is the ID that must be used for the email API
    const lagoInvoiceId = responseData.invoice?.lago_id || body.invoice_id;
    console.log(`📧 LAGO INVOICE API [${trackingId}] - Using lago_invoice_id: ${lagoInvoiceId} for email notification`);

    // Check if email sending should be skipped
    const skipEmail = request.headers.get('X-Skip-Email') === 'true';

    // Check if this is a duplicate request by looking for a custom header
    const isRetry = request.headers.get('X-Is-Retry') === 'true';

    // Generate a unique key for this invoice update
    const invoiceUpdateKey = `invoice_update_${lagoInvoiceId}`;

    // Check if we've already processed this invoice update in this session
    const alreadyProcessed = global.processedInvoiceUpdates && global.processedInvoiceUpdates.has(invoiceUpdateKey);

    // Initialize the global set if it doesn't exist
    if (!global.processedInvoiceUpdates) {
      global.processedInvoiceUpdates = new Set<string>();
    }

    // Mark this invoice as processed to prevent duplicate processing
    global.processedInvoiceUpdates.add(invoiceUpdateKey);

    if (skipEmail) {
      console.log(`📧 LAGO INVOICE API [${trackingId}] - Skipping email notification as requested`);
      responseData.email_notification_sent = false;
    } else if (alreadyProcessed && !isRetry) {
      console.log(`📧 LAGO INVOICE API [${trackingId}] - Invoice ${lagoInvoiceId} already processed, skipping duplicate email`);
      responseData.email_notification_sent = true;
      responseData.email_deduplicated = true;
    } else {
      try {
        // Use direct server-side email sending with retry mechanism
        // IMPORTANT: Use the same lago_invoice_id for both invoice update and email
        console.log(`📧 LAGO INVOICE API [${trackingId}] - Sending email notification with lago_invoice_id: ${lagoInvoiceId}`);

        // Use the server-side function directly to avoid additional API calls
        // This ensures the email is sent server-side and only once
        const emailResult = await sendInvoicePaymentEmailWithRetry(lagoInvoiceId, 3, trackingId);

        if (emailResult.success) {
          if (emailResult.message === 'Email already sent (deduplicated)') {
            console.log(`📧 LAGO INVOICE API [${trackingId}] - Email already sent for invoice: ${lagoInvoiceId}, skipping duplicate`);
            responseData.email_notification_sent = true;
            responseData.email_deduplicated = true;
          } else {
            console.log(`✅ LAGO INVOICE API [${trackingId}] - Successfully sent invoice payment email: ${lagoInvoiceId}`);
            responseData.email_notification_sent = true;
          }
        } else {
          console.error(`⚠️ LAGO INVOICE API [${trackingId}] - Failed to send invoice payment email after retries: ${lagoInvoiceId}`, emailResult.error);
          // Continue despite email failure - don't fail the whole request
          responseData.email_notification_sent = false;
          responseData.email_error = emailResult.error;
        }
      } catch (emailError) {
        console.error(`⚠️ LAGO INVOICE API [${trackingId}] - Exception sending invoice payment email: ${lagoInvoiceId}`, emailError);
        // Continue despite email failure - don't fail the whole request
        responseData.email_notification_sent = false;
        responseData.email_error = emailError instanceof Error ? emailError.message : String(emailError);
      }
    }

    // Add the lago_invoice_id to the response for reference
    responseData.lago_invoice_id = lagoInvoiceId;

    return NextResponse.json(responseData, { status: 200, headers: corsHeaders });
  } catch (error) {
    console.error("❌ LAGO INVOICE API - Unexpected error:", error);
    return NextResponse.json(
      { error: "Failed to process invoice update request", message: error instanceof Error ? error.message : String(error) },
      { status: 500, headers: corsHeaders }
    );
  }
}

/**
 * Get invoice by ID
 */
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const invoiceId = url.searchParams.get('id');
  const customerId = url.searchParams.get('customerId');
  const externalCustomerId = url.searchParams.get('external_customer_id');
  const issuingDateFrom = url.searchParams.get('issuing_date_from');
  const paymentStatus = url.searchParams.get('payment_status');
  const status = url.searchParams.get('status');

  // Check if we have the minimum parameters needed
  if (!invoiceId && !customerId && !externalCustomerId) {
    return NextResponse.json({ error: 'Missing required parameters: either id, customerId or external_customer_id must be provided' }, {
      status: 400,
      headers: corsHeaders
    });
  }

  try {
    // If invoiceId is provided, get details for that invoice
    if (invoiceId) {
      const response = await safeFetch(`${INVOICE_URL}/${invoiceId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json(data, { headers: corsHeaders });
      } else {
        const error = await response.text();
        console.error(`❌ API - Error fetching invoice: ${response.status}`, error);
        return NextResponse.json({ error: 'Error fetching invoice' }, {
          status: response.status,
          headers: corsHeaders
        });
      }
    }

    // If we have external_customer_id, issuing_date_from, payment_status, and status,
    // use them to fetch filtered invoices
    if (externalCustomerId && issuingDateFrom && paymentStatus && status) {
      console.log(`🔄 API - Fetching invoices with filters:`, {
        external_customer_id: externalCustomerId,
        issuing_date_from: issuingDateFrom,
        payment_status: paymentStatus,
        status: status
      });

      const queryParams = new URLSearchParams({
        external_customer_id: externalCustomerId,
        issuing_date_from: issuingDateFrom,
        payment_status: paymentStatus,
        status: status
      });

      const response = await safeFetch(`${INVOICE_URL}?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log(`✅ API - Successfully fetched filtered invoices: ${data.invoices?.length || 0} results`);
        return NextResponse.json(data, { headers: corsHeaders });
      } else {
        const error = await response.text();
        console.error(`❌ API - Error fetching filtered invoices: ${response.status}`, error);
        return NextResponse.json({ error: 'Error fetching filtered invoices' }, {
          status: response.status,
          headers: corsHeaders
        });
      }
    }

    // If customerId is provided, get all invoices for customer
    if (customerId) {
      const response = await safeFetch(`${INVOICE_URL}?external_customer_id=${customerId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json(data, { headers: corsHeaders });
      } else {
        const error = await response.text();
        console.error(`❌ API - Error fetching customer invoices: ${response.status}`, error);
        return NextResponse.json({ error: 'Error fetching customer invoices' }, {
          status: response.status,
          headers: corsHeaders
        });
      }
    }
  } catch (error) {
    console.error('❌ API - Exception in GET /api/lago/invoice:', error);
    return NextResponse.json({ error: 'Server error' }, {
      status: 500,
      headers: corsHeaders
    });
  }
}