/**
 * Contact API Route
 *
 * Handles contact form submissions and forwards them to the MailForm service.
 * Supports POST method for form submissions.
 */

import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { z } from "zod";

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Log levels for detailed logging
const LOG_LEVELS = {
  INFO: '📝 CONTACT API',
  SUCCESS: '✅ CONTACT API',
  WARNING: '⚠️ CONTACT API',
  ERROR: '❌ CONTACT API'
};

// MailForm service URL - should come from environment variables
const MAILFORM_URL = process.env.NEXT_PUBLIC_MAILFORM_API_URL || 'http://localhost:3003/onebiz';

// Validation schema for contact form data
const contactFormSchema = z.object({
  firstName: z.string().min(2).max(255),
  lastName: z.string().min(2).max(255),
  email: z.string().email(),
  subject: z.string().min(2).max(255),
  message: z.string().min(1),
  'g-recaptcha-response': z.string().optional()
});

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// We only support POST for form submissions

/**
 * POST handler for contact form submissions
 *
 * Handles multipart/form-data submissions with:
 * - firstName: First name (required)
 * - lastName: Last name (required)
 * - email: Email address (required)
 * - subject: Subject (required)
 * - message: Message (required)
 * - g-recaptcha-response: reCAPTCHA token (optional)
 */
export async function POST(request: NextRequest) {
  const trackingId = `post_${Date.now()}`;
  console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Processing contact form submission`);

  try {
    // Get form data from the request
    const formData = await request.formData();

    // Extract form fields
    const data = {
      firstName: formData.get('firstName') as string || '',
      lastName: formData.get('lastName') as string || '',
      email: formData.get('email') as string || '',
      subject: formData.get('subject') as string || '',
      message: formData.get('message') as string || '',
      'g-recaptcha-response': formData.get('g-recaptcha-response') as string || ''
    };

    // Log the received data
    console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Form data:`, {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      subject: data.subject,
      messageLength: data.message.length,
      hasRecaptchaToken: !!data['g-recaptcha-response']
    });

    // Validate form data
    try {
      contactFormSchema.parse(data);
    } catch (validationError) {
      console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - Validation error:`, validationError);
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid form data',
          error: 'VALIDATION_ERROR',
          details: validationError instanceof z.ZodError ? validationError.errors : undefined
        },
        { status: 400, headers: corsHeaders }
      );
    }

    // Create a new FormData object to send to the MailForm service
    const mailFormData = new FormData();
    mailFormData.append('from', data.email);
    // Combine firstName and lastName into name field
    mailFormData.append('name', `${data.firstName} ${data.lastName}`);
    mailFormData.append('subject', data.subject);
    mailFormData.append('body', data.message);

    // Add current year for the email template
    mailFormData.append('year', new Date().getFullYear().toString());

    // Add product field (empty for now, can be populated later)
    mailFormData.append('product', 'Website Contact Form');

    // Add reCAPTCHA token if available
    if (data['g-recaptcha-response']) {
      mailFormData.append('g-recaptcha-response', data['g-recaptcha-response']);
    }

    try {
      // For development/testing, we'll simulate a successful response
      // In production, this would call the actual MailForm API
      if (process.env.NODE_ENV === 'development') {
        console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Development mode: Simulating successful response`);

        // Simulate a delay to mimic network latency
        await new Promise(resolve => setTimeout(resolve, 1000));

        return NextResponse.json(
          {
            success: true,
            message: 'Contact form submitted successfully (development mode)'
          },
          { status: 200, headers: corsHeaders }
        );
      }

      // In production, send to the MailForm service
      console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Sending to MailForm service: ${MAILFORM_URL}`);

      // Log all form data being sent
      console.log(`${LOG_LEVELS.INFO} [${trackingId}] - Form data being sent:`,
        Object.fromEntries(mailFormData.entries()));

      const response = await axios.post(
        MAILFORM_URL,
        mailFormData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      console.log(`${LOG_LEVELS.SUCCESS} [${trackingId}] - MailForm response:`, response.status);

      return NextResponse.json(
        {
          success: true,
          message: 'Contact form submitted successfully'
        },
        { status: 200, headers: corsHeaders }
      );
    } catch (apiError: unknown) {
      const error = apiError as { response?: { data?: { message?: string }; status?: number }; message?: string };
      console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - MailForm API error:`,
        error.response?.data || error.message);

      return NextResponse.json(
        {
          success: false,
          message: error.response?.data?.message || 'Failed to send contact form',
          error: `API_ERROR_${error.response?.status || 'UNKNOWN'}`
        },
        { status: error.response?.status || 500, headers: corsHeaders }
      );
    }
  } catch (error) {
    console.error(`${LOG_LEVELS.ERROR} [${trackingId}] - Exception in POST /api/contact:`, error);
    return NextResponse.json(
      {
        success: false,
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
