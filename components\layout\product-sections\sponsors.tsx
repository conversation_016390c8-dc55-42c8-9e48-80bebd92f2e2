import { sponsors } from "@/@data/sponsors";
import { Marquee } from "@devnomic/marquee";
import Image from "next/image";

export const SponsorsSection = () => {
  return (
    <section className="pb-12 lg:pb-20">
      <div className="container">
        <Marquee
          className="gap-[3rem] lg:gap-[5rem]"
          fade
          innerClassName="gap-[3rem] lg:gap-[5rem]"
          pauseOnHover
        >
          {sponsors.map(({ imageSrc, name }) => (
            <div
              key={name}
              className="flex items-center text-xl md:text-2xl font-medium"
            >
              <Image
                src={imageSrc}
                alt={name}
                width={28}
                height={28}
                className="mr-3"
              />
              {name}
            </div>
          ))}
        </Marquee>
      </div>
    </section>
  );
};
