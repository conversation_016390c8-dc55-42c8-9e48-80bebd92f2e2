"use client";

import Link from "next/link";
import {
  <PERSON>,
  Card<PERSON>es<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function TestsPage() {
  const tests = [
    {
      title: "Plan Utilities Tests",
      description: "Test the plan code extraction and generation functions",
      path: "/dev/tests/plan-utils",
    },
    // Add more test pages here as they are developed
  ];

  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">Development Tests</h1>
      <p className="text-muted-foreground mb-8">
        These pages help verify the functionality of various utilities and components.
      </p>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {tests.map((test, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle>{test.title}</CardTitle>
              <CardDescription>{test.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button asChild>
                <Link href={test.path}>Run Tests</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
} 