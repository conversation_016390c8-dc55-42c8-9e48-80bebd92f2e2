import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import Logo from "../logo";
import { Button } from "@/components/ui/button";

export const FooterSection = () => {
  return (
    <footer
      id="footer"
      className="container pb-8 lg:pb-16"
    >
      <div className="p-10 bg-muted border rounded-2xl">
        <div className="grid grid-cols-2 md:grid-cols-4 xl:grid-cols-6 gap-x-12 gap-y-8">
          <div className="col-span-full xl:col-span-2">
            <Logo />
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="font-bold text-lg mb-2">
              Contact
            </h3>
            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Github
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Twitter
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Instagram
              </Link>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="font-bold text-lg mb-2">
              Platforms
            </h3>
            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                iOS
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Android
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Web
              </Link>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="font-bold text-lg mb-2">
              Help
            </h3>
            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Contact Us
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                FAQ
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Feedback
              </Link>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <h3 className="font-bold text-lg mb-2">
              Socials
            </h3>
            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Twitch
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Discord
              </Link>
            </div>

            <div>
              <Link
                href="#"
                className="opacity-60 hover:opacity-100"
              >
                Dribbble
              </Link>
            </div>
          </div>
        </div>

        <Separator className="my-6" />

        <div className="text-sm text-muted-foreground">
          &copy; 2025 | It is under the roof of{" "}
          <Button
            variant="link"
            className="p-0 h-auto"
            asChild
          >
            <Link
              target="_blank"
              href="https://www.futurescapetech.com/"
            >
              Futurescape Technology
            </Link>
          </Button>
          .
        </div>
      </div>
    </footer>
  );
};
