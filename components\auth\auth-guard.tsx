"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState, ReactNode } from "react";
import { toast } from "sonner";
import { AuthModal } from "./auth-modal";
import { LoadingSpinner } from "../ui/loading-spinner";

export interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  fallback?: ReactNode;
  redirectTo?: string;
  showModal?: boolean;
  onAuthRequired?: () => void;
  loadingComponent?: ReactNode;
  message?: string;
  actionLabel?: string;
}

/**
 * AuthGuard - A comprehensive authentication guard component
 * Provides consistent UX across all authentication scenarios
 */
export function AuthGuard({
  children,
  requireAuth = false,
  fallback,
  redirectTo,
  showModal = false,
  onAuthRequired,
  loadingComponent,
  message = "Please sign in to continue",
  actionLabel = "Sign In"
}: AuthGuardProps) {
  const { status } = useSession();
  const router = useRouter();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  useEffect(() => {
    if (status === "loading") return;

    setHasCheckedAuth(true);

    if (requireAuth && status === "unauthenticated") {
      if (onAuthRequired) {
        onAuthRequired();
        return;
      }

      if (showModal) {
        setShowAuthModal(true);
        return;
      }

      if (redirectTo) {
        // Store current URL for return after auth
        const currentUrl = window.location.pathname + window.location.search;
        localStorage.setItem("auth_return_url", currentUrl);
        router.push(redirectTo);
        return;
      }

      // Default behavior - show toast with action
      toast.info(message, {
        duration: 6000,
        action: {
          label: actionLabel,
          onClick: () => setShowAuthModal(true)
        }
      });
    }
  }, [status, requireAuth, onAuthRequired, showModal, redirectTo, message, actionLabel, router]);

  // Show loading state
  if (status === "loading" || (requireAuth && !hasCheckedAuth)) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-muted-foreground">Checking authentication...</span>
      </div>
    );
  }

  // Show fallback if not authenticated and auth is required
  if (requireAuth && status === "unauthenticated" && fallback) {
    return <>{fallback}</>;
  }

  // Show auth modal if needed
  if (showAuthModal) {
    return (
      <>
        {children}
        <AuthModal
          open={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          message={message}
          returnUrl={window.location.pathname + window.location.search}
        />
      </>
    );
  }

  return <>{children}</>;
}

/**
 * Hook for programmatic authentication checks
 */
export function useAuthGuard() {
  const { data: session, status } = useSession();
  const [showAuthModal, setShowAuthModal] = useState(false);

  const requireAuth = (options?: {
    message?: string;
    onSuccess?: () => void;
    onCancel?: () => void;
    showModal?: boolean;
    redirectTo?: string;
  }) => {
    if (status === "loading") {
      toast.info("Please wait while we check your authentication...");
      return false;
    }

    if (status === "authenticated" && session) {
      options?.onSuccess?.();
      return true;
    }

    // User is not authenticated
    const message = options?.message || "Please sign in to continue";
    
    if (options?.showModal !== false) {
      setShowAuthModal(true);
    } else if (options?.redirectTo) {
      const currentUrl = window.location.pathname + window.location.search;
      localStorage.setItem("auth_return_url", currentUrl);
      window.location.href = options.redirectTo;
    } else {
      toast.info(message, {
        duration: 6000,
        action: {
          label: "Sign In",
          onClick: () => setShowAuthModal(true)
        }
      });
    }

    return false;
  };

  const AuthModalComponent = () => (
    <AuthModal
      open={showAuthModal}
      onClose={() => {
        setShowAuthModal(false);
      }}
      message="Please sign in to continue"
      returnUrl={window.location.pathname + window.location.search}
    />
  );

  return {
    requireAuth,
    isAuthenticated: status === "authenticated" && !!session,
    isLoading: status === "loading",
    session,
    AuthModalComponent
  };
}
