import { redirect } from 'next/navigation';

/**
 * Payment confirmation page that redirects to the final confirmation page
 * This handles GET requests - POST requests are handled by the API route
 */
export default function CheckoutConfirmPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  try {
    // Convert search params to URL string with proper null/undefined handling
    const params = new URLSearchParams();

    // Safely process search parameters
    if (searchParams && typeof searchParams === 'object') {
      Object.entries(searchParams).forEach(([key, value]) => {
        // Skip null, undefined, empty string values
        if (value != null && value !== '' && value !== 'null' && value !== 'undefined') {
          if (Array.isArray(value)) {
            // Handle array values
            value.forEach(v => {
              if (v != null && v !== '' && v !== 'null' && v !== 'undefined') {
                params.append(key, String(v));
              }
            });
          } else {
            // Handle single values - ensure they're strings
            params.append(key, String(value));
          }
        }
      });
    }

    // Add success status if not present
    if (!params.has('status')) {
      params.append('status', 'success');
    }

    // Construct the redirect URL safely
    const redirectUrl = `/checkout/confirmed?${params.toString()}`;
    
    // Log for debugging (remove in production if needed)
    console.log('🔄 CHECKOUT CONFIRM - Redirecting to:', redirectUrl);
    
    // Redirect to confirmed page with all parameters
    redirect(redirectUrl);
  } catch (error) {
    // Handle any errors gracefully
    console.error('❌ CHECKOUT CONFIRM - Error processing confirmation:', error);
    
    // Fallback redirect with minimal parameters
    redirect('/checkout/confirmed?status=error&message=processing_error');
  }
}
