/**
 * Logger utility for consistent logging across the application
 * Provides different log levels and standardized formatting
 */

type LogLevel = 'info' | 'warn' | 'error' | 'debug';

interface LogOptions {
  context?: string;
  data?: any;
}

class Logger {
  /**
   * Log informational messages
   */
  static info(message: string, options?: LogOptions): void {
    Logger.log('info', message, options);
  }

  /**
   * Log warning messages
   */
  static warn(message: string, options?: LogOptions): void {
    Logger.log('warn', message, options);
  }

  /**
   * Log error messages
   */
  static error(message: string, options?: LogOptions): void {
    Logger.log('error', message, options);
  }

  /**
   * Log debug messages (only in development)
   */
  static debug(message: string, options?: LogOptions): void {
    if (process.env.NODE_ENV !== 'production') {
      Logger.log('debug', message, options);
    }
  }

  /**
   * Internal logging method with standardized formatting
   */
  private static log(level: LogLevel, message: string, options?: LogOptions): void {
    const timestamp = new Date().toISOString();
    const context = options?.context ? `[${options.context}]` : '';
    const formattedMessage = `${timestamp} ${level.toUpperCase()} ${context} ${message}`;
    
    switch (level) {
      case 'info':
        console.info(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
      case 'debug':
        console.debug(formattedMessage);
        break;
    }

    // Log additional data if provided
    if (options?.data) {
      console[level === 'error' ? 'error' : 'log'](options.data);
    }
  }
}

export default Logger;
