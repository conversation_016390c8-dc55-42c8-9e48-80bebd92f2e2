"use client";

import { useState } from "react";
import { <PERSON>ton, ButtonProps } from "@/components/ui/button";
import { validateProductSwitch } from "@/src/services/subscriptionValidationService";
import { PlanChangeBlockedDialog } from "./plan-change-blocked-dialog";
import { useUser } from "@/context/user-context";
import { toast } from "sonner";

interface ProductSwitchButtonProps extends ButtonProps {
  currentProductSlug: string;
  targetProductSlug: string;
  currentPlanCode: string;
}

export function ProductSwitchButton({
  currentProductSlug,
  targetProductSlug,
  currentPlanCode,
  children,
  ...props
}: ProductSwitchButtonProps) {
  const { userId } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [showBlockedDialog, setShowBlockedDialog] = useState(false);
  const [blockedMessage, setBlockedMessage] = useState<string>("");

  const handleSwitchClick = async () => {
    if (!userId) {
      toast.error("You must be logged in to switch products");
      return;
    }

    setIsLoading(true);

    try {
      // Validate if product switching is allowed
      const validationResult = await validateProductSwitch({
        userId,
        productSlug: currentProductSlug,
        planCode: currentPlanCode
      });

      // Product switching is not allowed according to business rules
      if (!validationResult.isValid) {
        setBlockedMessage(validationResult.message);
        setShowBlockedDialog(true);
        return;
      }

      // If we get here, switching would be allowed (but we don't implement it)
      // This should never happen with current business rules, but we'll handle it anyway
      setBlockedMessage("Each product has its own isolated plans. You cannot switch between products.");
      setShowBlockedDialog(true);
    } catch (error) {
      console.error("Error validating product switch:", error);
      toast.error("An error occurred while processing your request");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        onClick={handleSwitchClick}
        disabled={isLoading}
        {...props}
      >
        {isLoading ? "Please wait..." : children || `Switch to ${targetProductSlug}`}
      </Button>

      <PlanChangeBlockedDialog
        open={showBlockedDialog}
        onClose={() => setShowBlockedDialog(false)}
        message={blockedMessage}
        actionType="switch"
      />
    </>
  );
}
