import React, { useState, createContext, useContext, ReactNode } from "react";
import { Check, ChevronsRight } from "lucide-react";
import { cn } from "@/lib/utils";

interface StepperContextType {
  activeStep: number;
  totalSteps: number;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  goToStep: (step: number) => void;
  isLastStep: boolean;
  isFirstStep: boolean;
}

const StepperContext = createContext<StepperContextType | undefined>(undefined);

export function useStepper() {
  const context = useContext(StepperContext);
  if (!context) {
    throw new Error("useStepper must be used within a StepperProvider");
  }
  return context;
}

interface StepperProviderProps {
  children: ReactNode;
  initialStep?: number;
  totalSteps: number;
}

export function StepperProvider({
  children,
  initialStep = 0,
  totalSteps,
}: StepperProviderProps) {
  const [activeStep, setActiveStep] = useState(initialStep);

  const goToNextStep = () => {
    if (activeStep < totalSteps - 1) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const goToPreviousStep = () => {
    if (activeStep > 0) {
      setActiveStep((prev) => prev - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setActiveStep(step);
    }
  };

  const value = {
    activeStep,
    totalSteps,
    goToNextStep,
    goToPreviousStep,
    goToStep,
    isLastStep: activeStep === totalSteps - 1,
    isFirstStep: activeStep === 0,
  };

  return (
    <StepperContext.Provider value={value}>{children}</StepperContext.Provider>
  );
}

interface StepProps {
  children: ReactNode;
  step: number;
}

export function Step({ children, step }: StepProps) {
  const { activeStep } = useStepper();
  
  if (activeStep !== step) {
    return null;
  }
  
  return <div className="w-full">{children}</div>;
}

interface StepperProps {
  children: ReactNode;
  className?: string;
}

export function Stepper({ children, className }: StepperProps) {
  return (
    <div className={cn("w-full flex flex-col space-y-6", className)}>
      {children}
    </div>
  );
}

interface StepperHeaderProps {
  steps: { label: string; description?: string }[];
  className?: string;
}

export function StepperHeader({ steps, className }: StepperHeaderProps) {
  const { activeStep } = useStepper();
  
  return (
    <div className={cn("w-full flex justify-between", className)}>
      {steps.map((step, index) => (
        <div key={index} className="flex items-center">
          {/* Step Circle */}
          <div
            className={cn(
              "flex items-center justify-center h-10 w-10 rounded-full border-2 transition-all duration-300",
              activeStep > index
                ? "bg-primary border-primary text-white"
                : activeStep === index
                ? "border-primary text-primary"
                : "border-muted-foreground/40 text-muted-foreground/40"
            )}
          >
            {activeStep > index ? (
              <Check className="h-5 w-5" />
            ) : (
              <span>{index + 1}</span>
            )}
          </div>
          
          {/* Step Label */}
          <div className="ml-3 hidden md:block">
            <p
              className={cn(
                "text-sm font-medium",
                activeStep >= index
                  ? "text-foreground"
                  : "text-muted-foreground/40"
              )}
            >
              {step.label}
            </p>
            {step.description && (
              <p className="text-xs text-muted-foreground">
                {step.description}
              </p>
            )}
          </div>
          
          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div
              className={cn(
                "hidden md:flex items-center w-16 mx-4",
                activeStep > index
                  ? "text-primary"
                  : "text-muted-foreground/30"
              )}
            >
              <div className="h-0.5 w-full bg-current" />
              <ChevronsRight className="h-4 w-4 flex-shrink-0" />
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

interface StepperActionsProps {
  className?: string;
  nextButtonLabel?: string;
  backButtonLabel?: string;
  onNext?: () => Promise<boolean> | boolean;
  onBack?: () => void;
  onComplete?: () => void;
  submitButtonLabel?: string;
  isNextDisabled?: boolean;
  isBackDisabled?: boolean;
  hideBackOnFirst?: boolean;
  nextClassName?: string;
  backClassName?: string;
}

export function StepperActions({
  className,
  nextButtonLabel = "Next",
  backButtonLabel = "Back",
  submitButtonLabel = "Complete",
  onNext,
  onBack,
  onComplete,
  isNextDisabled = false,
  isBackDisabled = false,
  hideBackOnFirst = true,
  nextClassName,
  backClassName,
}: StepperActionsProps) {
  const { goToNextStep, goToPreviousStep, isLastStep, isFirstStep } = useStepper();
  const [isLoading, setIsLoading] = useState(false);

  const handleNext = async () => {
    if (isNextDisabled || isLoading) return;
    
    if (onNext) {
      setIsLoading(true);
      try {
        const canProceed = await onNext();
        if (canProceed) {
          if (isLastStep && onComplete) {
            onComplete();
          } else {
            goToNextStep();
          }
        }
      } catch (error) {
        console.error("Error in next step handler:", error);
      } finally {
        setIsLoading(false);
      }
    } else {
      if (isLastStep && onComplete) {
        onComplete();
      } else {
        goToNextStep();
      }
    }
  };

  const handleBack = () => {
    if (isBackDisabled) return;
    
    if (onBack) {
      onBack();
    } else {
      goToPreviousStep();
    }
  };

  return (
    <div
      className={cn(
        "flex items-center justify-between mt-8 pt-4 border-t",
        className
      )}
    >
      {(!isFirstStep || !hideBackOnFirst) && (
        <button
          type="button"
          onClick={handleBack}
          disabled={isBackDisabled || isLoading}
          className={cn(
            "px-4 py-2 text-sm font-medium rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground",
            isBackDisabled && "opacity-50 cursor-not-allowed",
            backClassName
          )}
        >
          {backButtonLabel}
        </button>
      )}
      <div className={isFirstStep && hideBackOnFirst ? "ml-auto" : ""}>
        <button
          type="button"
          onClick={handleNext}
          disabled={isNextDisabled || isLoading}
          className={cn(
            "px-4 py-2 text-sm font-medium rounded-md bg-primary text-primary-foreground hover:bg-primary/90",
            isNextDisabled && "opacity-50 cursor-not-allowed",
            isLoading && "opacity-70 cursor-wait",
            nextClassName
          )}
        >
          {isLoading
            ? "Loading..."
            : isLastStep
            ? submitButtonLabel
            : nextButtonLabel}
        </button>
      </div>
    </div>
  );
} 