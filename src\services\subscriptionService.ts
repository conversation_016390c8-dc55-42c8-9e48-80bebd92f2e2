/**
 * Subscription Service
 *
 * Provides a centralized interface for subscription operations.
 * <PERSON>les interaction with Lago API for subscription management.
 */
import { CartItem } from "@/context/cart-context";
import { SubscriptionResponse } from "./lago/subscriptionService";
import { Subscription } from "./trialService";
import { validateTrialEligibility, validatePaidSubscription } from "./subscriptionValidationService";
import { extractProductCode } from "@/src/utils/plan-utils";

// Constants
const LAGO_API_URL = process.env.NEXT_PUBLIC_LAGO_API_URL;
const LAGO_API_KEY = process.env.NEXT_PUBLIC_LAGO_API_KEY;

// Types
export interface SubscriptionPayload {
  subscription: {
    external_customer_id: string; // Keycloak ID
    plan_code: string;
    external_id: string;
    name: string;
    billing_time: string;
    ending_at?: string;
    subscription_at?: string;
  };
}

export interface PlanChangeOptions {
  preserveCredits?: boolean;
  cancelImmediately?: boolean;
  calculateProration?: boolean;
  effectiveDate?: string; // ISO 8601 date string
}

export interface ProrationResult {
  remainingDays: number;
  totalDays: number;
  remainingPercentage: number;
  creditAmountCents: number;
  currentPlanMonthlyPrice?: number;
  newPlanMonthlyPrice?: number;
  netDifference?: number;
}

export interface Customer {
  id: string;
  external_id: string;
  name?: string;
  email?: string;
  created_at: string;
  country?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  zipcode?: string;
}

/**
 * Helper function to get customer by ID
 * Fetches customer data from Lago API
 */
async function getCustomerById(customerId: string): Promise<Customer | null> {
  if (!customerId) {
    console.error("❌ SUBSCRIPTION - Cannot get customer: No customer ID provided");
    return null;
  }

  try {
    const response = await fetch(`${LAGO_API_URL}/customers/${customerId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.log(`ℹ️ SUBSCRIPTION - Customer not found: ${customerId}`);
        return null;
      }

      const errorText = await response.text();
      console.error(`❌ SUBSCRIPTION - Error fetching customer (${response.status}):`, errorText);
      return null;
    }

    const data = await response.json();
    return data.customer || null;
  } catch (error) {
    console.error(`❌ SUBSCRIPTION - Error fetching customer:`, error);
    return null;
  }
}

/**
 * Validates that the ID is a valid Keycloak UUID
 * @param id The ID to validate
 * @returns boolean indicating if it's a valid Keycloak UUID format
 */
function isValidKeycloakId(id: string): boolean {
  if (!id) return false;
  // Keycloak uses standard UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * Creates a subscription in the Lago billing system
 * @param keycloakId The Keycloak ID of the user subscribing to the plan
 * @param cartItem The cart item containing plan details
 * @returns The subscription API response
 */
export async function createSubscription(
  keycloakId: string | null,
  cartItem: CartItem,
): Promise<SubscriptionResponse> {
  console.log(`🚀 SUBSCRIPTION - Starting subscription creation for user: ${keycloakId?.substring(0, 8)}...`);

  const startTime = performance.now();

  // Basic validation
  if (!keycloakId) {
    const error = new Error("Cannot create subscription: Keycloak ID is required");
    console.error("❌ SUBSCRIPTION - No Keycloak ID provided for subscription");
    throw error;
  }

  if (typeof keycloakId !== 'string') {
    const error = new Error(`Invalid Keycloak ID type: ${typeof keycloakId}`);
    console.error("❌ SUBSCRIPTION - Invalid Keycloak ID type:", typeof keycloakId);
    throw error;
  }

  if (!isValidKeycloakId(keycloakId)) {
    const error = new Error(`ID is not a valid Keycloak UUID: ${keycloakId}`);
    console.error("❌ SUBSCRIPTION - Invalid Keycloak ID format:", keycloakId);
    throw error;
  }

  if (!cartItem.planCode) {
    const error = new Error(`Cannot create subscription: Plan code is required for item ${cartItem.name}`);
    console.error("❌ SUBSCRIPTION - No plan code provided in cart item");
    throw error;
  }

  // Extract product slug from plan code
  const productCode = extractProductCode(cartItem.planCode);
  if (!productCode) {
    const error = new Error(`Cannot create subscription: Invalid plan code format ${cartItem.planCode}`);
    console.error("❌ SUBSCRIPTION - Invalid plan code format");
    throw error;
  }

  // Business rule validation
  try {
    // For trial plans
    if (cartItem.planDuration === 'trial') {
      const validationResult = await validateTrialEligibility({
        userId: keycloakId,
        productSlug: productCode,
        planCode: cartItem.planCode
      });

      if (!validationResult.isValid) {
        const error = new Error(`Trial subscription not allowed: ${validationResult.message}`);
        console.error("❌ SUBSCRIPTION - Trial validation failed:", validationResult);
        throw error;
      }
    }
    // For paid plans
    else {
      const validationResult = await validatePaidSubscription({
        userId: keycloakId,
        productSlug: productCode,
        planCode: cartItem.planCode
      });

      if (!validationResult.isValid) {
        const error = new Error(`Paid subscription not allowed: ${validationResult.message}`);
        console.error("❌ SUBSCRIPTION - Paid plan validation failed:", validationResult);
        throw error;
      }
    }
  } catch (validationError) {
    console.error("❌ SUBSCRIPTION - Validation error:", validationError);
    throw validationError;
  }

  // Log the plan code for debugging
  console.log(`✅ SUBSCRIPTION - Using plan code: ${cartItem.planCode} for plan duration: ${cartItem.planDuration || 'unknown'}`);

  try {
    // Calculate subscription and ending dates
    const now = new Date();
    // Set time 30 seconds behind actual time for API posting
    const adjustedNow = new Date(now.getTime() - 30 * 1000);
    const subscriptionDate = adjustedNow.toISOString();
    console.log(`🕒 SUBSCRIPTION - Using adjusted time: ${subscriptionDate} (30 seconds behind actual time: ${now.toISOString()})`);

    // Default to one year from now
    let endDate = new Date(now);
    endDate.setFullYear(endDate.getFullYear() + 1);

    // Adjust end date based on plan duration if available
    if (cartItem.planDuration === 'monthly') {
      endDate.setFullYear(now.getFullYear());
      endDate.setMonth(now.getMonth() + 1);
    } else if (cartItem.planDuration === 'trial' && cartItem.trialDays) {
      const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
      const trialDuration = cartItem.trialDays * 24 * 60 * 60 * 1000; // trial days in milliseconds
      endDate = new Date(now.getTime() + trialDuration + istOffset);
    }

    const endingDate = endDate.toISOString();

    // Create a unique external ID combining the Keycloak ID and plan code
    const externalId = `${keycloakId}@${cartItem.planCode}`;

    // Format the plan duration text
    let durationText = "Yearly";
    if (cartItem.planDuration === "monthly") {
      durationText = "Monthly";
    } else if (cartItem.planDuration === "trial") {
      durationText = `Trial (${cartItem.trialDays || 14} days)`;
    }

    // Prepare subscription payload
    const payload: SubscriptionPayload = {
      subscription: {
        external_customer_id: keycloakId,
        plan_code: cartItem.planCode,
        external_id: externalId,
        name: `${cartItem.productName || ""} - ${cartItem.name} - ${durationText}`,
        billing_time: "anniversary",
        ending_at: endingDate,
        subscription_at: subscriptionDate
      }
    };

    // Log the payload for debugging
    console.log("📤 SUBSCRIPTION - Sending payload:", {
      customerId: keycloakId.substring(0, 8) + "...",
      planCode: payload.subscription.plan_code,
      name: payload.subscription.name,
      billingTime: payload.subscription.billing_time,
      endingAt: payload.subscription.ending_at,
      subscriptionAt: payload.subscription.subscription_at
    });

    // Make API call to Lago
    const response = await fetch(`${LAGO_API_URL}/subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      },
      body: JSON.stringify(payload)
    });

    // Performance tracking
    const responseTime = Math.round(performance.now() - startTime);
    console.log(`⏱️ SUBSCRIPTION - API response time: ${responseTime}ms`);

    // Handle error responses
    if (!response.ok) {
      let errorDetails = '';
      try {
        const errorText = await response.text();
        errorDetails = errorText;

        try {
          const errorJson = JSON.parse(errorText);
          errorDetails = JSON.stringify(errorJson);
        } catch {
          // Use the text if not JSON
        }
      } catch (parseError) {
        errorDetails = `Failed to parse error: ${parseError}`;
      }

      const error = new Error(`Lago API error (${response.status}): ${errorDetails}`);
      console.error(`❌ SUBSCRIPTION - API call failed with status ${response.status}:`, errorDetails);

      throw error;
    }

    // Parse successful response
    const data = await response.json();
    console.log(`✅ SUBSCRIPTION - Successfully created subscription with Keycloak ID:`, {
      id: data.subscription?.id,
      customer_id: data.subscription?.external_customer_id,
      plan_code: data.subscription?.plan_code
    });

    return data;
  } catch (error) {
    console.error("❌ SUBSCRIPTION - Error creating subscription:", error);
    throw error;
  }
}

/**
 * Calculates prorated credits for a subscription change
 * @param currentSubscription The current subscription
 * @param newPlanCode The new plan code to upgrade/downgrade to
 * @param newPlanPrice The price of the new plan in cents
 */
export async function calculateProration(
  currentSubscription: Subscription,
  newPlanCode: string,
  newPlanPrice: number
): Promise<ProrationResult> {
  if (!currentSubscription || !newPlanCode) {
    throw new Error("Missing required parameters for proration calculation");
  }

  // Get current date and next billing date
  const now = new Date();
  const nextBillingDate = currentSubscription.next_billing_at
    ? new Date(currentSubscription.next_billing_at)
    : null;

  if (!nextBillingDate) {
    throw new Error("Cannot calculate proration: missing next billing date");
  }

  // Calculate remaining days in current billing period
  const totalDays = getDaysInCurrentBillingPeriod(currentSubscription);
  const remainingDays = Math.max(0, Math.ceil((nextBillingDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
  const remainingPercentage = totalDays > 0 ? remainingDays / totalDays : 0;

  // Calculate credit amount based on remaining time
  const currentPlanMonthlyPrice = currentSubscription.amount_cents || 0;
  const creditAmountCents = Math.round(currentPlanMonthlyPrice * remainingPercentage);

  // Calculate net difference (positive means customer pays more, negative means refund)
  const netDifference = newPlanPrice - creditAmountCents;

  return {
    remainingDays,
    totalDays,
    remainingPercentage,
    creditAmountCents,
    currentPlanMonthlyPrice,
    newPlanMonthlyPrice: newPlanPrice,
    netDifference
  };
}

/**
 * Helper function to determine the days in current billing period
 */
function getDaysInCurrentBillingPeriod(subscription: Subscription): number {
  if (!subscription.next_billing_at || !subscription.started_at) {
    return 30; // Default to 30 days if we don't have precise dates
  }

  const startDate = new Date(subscription.started_at);
  const nextBillingDate = new Date(subscription.next_billing_at);

  // Calculate total milliseconds and convert to days
  const totalDays = Math.ceil((nextBillingDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  return Math.max(1, totalDays); // Ensure we never return less than 1 day
}

/**
 * Changes a subscription from one plan to another
 * @param keycloakId User's Keycloak ID
 * @param currentSubscriptionId The current subscription ID
 * @param newPlanCode The new plan code to change to
 * @param options Options for the plan change
 */
export async function changePlan(
  keycloakId: string,
  currentSubscriptionId: string,
  newPlanCode: string,
  options: PlanChangeOptions = {}
): Promise<SubscriptionResponse> {
  // Validate inputs
  if (!keycloakId || !isValidKeycloakId(keycloakId)) {
    throw new Error("Invalid Keycloak ID provided");
  }

  if (!currentSubscriptionId) {
    throw new Error("Current subscription ID is required");
  }

  if (!newPlanCode) {
    throw new Error("New plan code is required");
  }

  console.log(`🔄 SUBSCRIPTION - Changing plan for user: ${keycloakId.substring(0, 8)}...`, {
    fromSubscription: currentSubscriptionId,
    toPlan: newPlanCode,
    options
  });

  try {
    // First, ensure the customer exists
    const customer = await getCustomerById(keycloakId);
    if (!customer) {
      throw new Error(`Customer not found for Keycloak ID: ${keycloakId}`);
    }

    // Fetch current subscription details
    const currentSubscription = await getSubscriptionById(currentSubscriptionId);
    if (!currentSubscription) {
      throw new Error(`Subscription not found: ${currentSubscriptionId}`);
    }

    // Log the current plan code for comparison
    console.log(`✅ SUBSCRIPTION - Changing from plan code: ${currentSubscription.plan_code} to: ${newPlanCode}`);

    // 1. Cancel the current subscription
    const cancelImmediate = options.cancelImmediately || false;

    if (cancelImmediate) {
      // For immediate cancellations, terminate the subscription
      const terminateResult = await terminateSubscription(currentSubscriptionId);
      if (!terminateResult) {
        throw new Error(`Failed to terminate current subscription: ${currentSubscriptionId}`);
      }
      console.log(`✅ SUBSCRIPTION - Successfully terminated current subscription: ${currentSubscriptionId}`);
    } else {
      // For end-of-period cancellations
      const cancelResult = await cancelSubscription(currentSubscriptionId);
      if (!cancelResult) {
        throw new Error(`Failed to cancel current subscription: ${currentSubscriptionId}`);
      }
      console.log(`✅ SUBSCRIPTION - Successfully cancelled current subscription: ${currentSubscriptionId}`);
    }

    // 2. Create the new subscription
    // Generate a new external ID for the subscription
    const externalId = `${keycloakId}@${newPlanCode}`;

    // Prepare the new subscription payload
    const payload: SubscriptionPayload = {
      subscription: {
        external_customer_id: keycloakId,
        plan_code: newPlanCode,
        external_id: externalId,
        name: `Changed Plan - ${newPlanCode}`,
        billing_time: "anniversary"
      }
    };

    // Set effective date if provided
    if (options.effectiveDate) {
      payload.subscription.subscription_at = options.effectiveDate;
    } else {
      // If no effective date provided, use current time minus 30 seconds
      const now = new Date();
      const adjustedNow = new Date(now.getTime() - 30 * 1000);
      payload.subscription.subscription_at = adjustedNow.toISOString();
      console.log(`🕒 SUBSCRIPTION - Using adjusted time for plan change: ${payload.subscription.subscription_at} (30 seconds behind actual time: ${now.toISOString()})`);
    }

    // Log the payload for debugging
    console.log("📤 SUBSCRIPTION - Creating new subscription with payload:", {
      customerId: keycloakId.substring(0, 8) + "...",
      planCode: newPlanCode,
      externalId: externalId,
      effectiveDate: options.effectiveDate || 'immediate'
    });

    // Create the new subscription
    const response = await fetch(`${LAGO_API_URL}/subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to create new subscription: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    console.log(`✅ SUBSCRIPTION - Successfully changed plan from ${currentSubscription.plan_code} to ${newPlanCode}`);

    return result;
  } catch (error) {
    console.error("❌ SUBSCRIPTION - Error changing plan:", error);
    throw error;
  }
}

/**
 * Gets a subscription by its ID
 */
export async function getSubscriptionById(subscriptionId: string): Promise<Subscription | null> {
  if (!subscriptionId) {
    console.error("❌ SUBSCRIPTION - No subscription ID provided");
    return null;
  }

  try {
    const response = await fetch(`${LAGO_API_URL}/subscriptions/${subscriptionId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.log(`ℹ️ SUBSCRIPTION - Subscription not found: ${subscriptionId}`);
        return null;
      }

      const errorText = await response.text();
      console.error(`❌ SUBSCRIPTION - API error (${response.status}):`, errorText);
      throw new Error(`Error fetching subscription: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    return data.subscription as Subscription;
  } catch (error) {
    console.error("❌ SUBSCRIPTION - Error fetching subscription:", error);
    return null;
  }
}

/**
 * Cancels a subscription at the end of the current billing period
 */
export async function cancelSubscription(subscriptionId: string): Promise<boolean> {
  if (!subscriptionId) {
    console.error("❌ SUBSCRIPTION - No subscription ID provided for cancellation");
    return false;
  }

  try {
    const response = await fetch(`${LAGO_API_URL}/subscriptions/${subscriptionId}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ SUBSCRIPTION - API error cancelling subscription (${response.status}):`, errorText);
      return false;
    }

    console.log(`✅ SUBSCRIPTION - Successfully cancelled subscription: ${subscriptionId}`);
    return true;
  } catch (error) {
    console.error(`❌ SUBSCRIPTION - Error cancelling subscription:`, error);
    return false;
  }
}

/**
 * Terminates a subscription immediately
 */
export async function terminateSubscription(subscriptionId: string): Promise<boolean> {
  if (!subscriptionId) {
    console.error("❌ SUBSCRIPTION - No subscription ID provided for termination");
    return false;
  }

  try {
    const response = await fetch(`${LAGO_API_URL}/subscriptions/${subscriptionId}/terminate`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ SUBSCRIPTION - API error terminating subscription (${response.status}):`, errorText);
      return false;
    }

    console.log(`✅ SUBSCRIPTION - Successfully terminated subscription: ${subscriptionId}`);
    return true;
  } catch (error) {
    console.error(`❌ SUBSCRIPTION - Error terminating subscription:`, error);
    return false;
  }
}
