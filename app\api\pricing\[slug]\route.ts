import { NextRequest, NextResponse } from "next/server";
import { getPricingContentBySlug } from "@/app/api/pricingSection";

/**
 * GET handler for /api/pricing/[slug]
 * Returns pricing information including trial plan for a product identified by slug
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        { error: "Product slug is required" },
        { status: 400 }
      );
    }

    const pricingSectionData = await getPricingContentBySlug(slug);

    if (!pricingSectionData) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Return relevant pricing information
    return NextResponse.json({
      productInfo: pricingSectionData.productInfo,
      trialPlan: pricingSectionData.trialPlan,
      subscriptionPlan: pricingSectionData.subscriptionPlan,
      enterprisePlan: pricingSectionData.enterprisePlan
    });
  } catch (error) {
    console.error("Error in pricing API:", error);
    return NextResponse.json(
      { error: "Failed to retrieve pricing information" },
      { status: 500 }
    );
  }
} 