/**
 * The above functions handle HTTP requests to manage subscriptions and customers in a Lago API,
 * including fetching, creating, and canceling subscriptions.
 * @param {string} id - The `id` parameter in the context of the provided code refers to the identifier
 * of a subscription. This identifier is used to uniquely identify a subscription within the system. It
 * is typically used in DELETE requests to cancel a specific subscription by providing its ID as a
 * parameter in the URL.
 * @returns The code snippet provided contains functions to handle different types of HTTP requests for
 * managing subscriptions in a system. Here is a summary of what each function returns:
 */
import { NextRequest, NextResponse } from 'next/server';
import { safeFetch } from "@/lib/fetch-utils";
import { LAGO_API_URL, LAGO_API_KEY, corsHeaders } from "@/src/constants/api";
import { validateAndNormalizePlanCode } from "@/src/services/lago/subscriptionService";
import { extractProductCode } from "@/src/utils/plan-utils";

// Constants for Lago API
const SUBSCRIPTION_URL = `${LAGO_API_URL}/subscriptions`;
const CUSTOMER_URL = `${LAGO_API_URL}/customers`;

// Helper function to sanitize Keycloak ID
function sanitizeKeycloakId(id: string): string | null {
  if (!id) return null;

  // Already valid UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (uuidRegex.test(id)) return id;

  // Try to extract UUID pattern
  const uuidPattern = /([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/i;
  const match = id.match(uuidPattern);
  if (match) return match[1];

  // Try UUID without dashes (32 chars)
  if (id.length === 32) {
    try {
      const withDashes = `${id.substring(0, 8)}-${id.substring(8, 12)}-${id.substring(12, 16)}-${id.substring(16, 20)}-${id.substring(20)}`;
      if (uuidRegex.test(withDashes)) return withDashes;
    } catch {}
  }

  // Remove customer- prefix if present
  if (id.startsWith('customer-')) {
    const withoutPrefix = id.substring(9);
    if (uuidRegex.test(withoutPrefix)) return withoutPrefix;
  }

  return id; // Return original if no sanitization possible
}

// Helper function to check if a customer exists
async function checkIfCustomerExists(customerId: string): Promise<boolean> {
  try {
    const response = await safeFetch(`${CUSTOMER_URL}/${customerId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });
    return response.ok;
  } catch (error) {
    console.error('Error checking if customer exists:', error);
    return false;
  }
}

// Helper function to create a minimal customer entry
async function createMinimalCustomer(customerId: string): Promise<Response> {
  const customerData = {
    customer: {
      external_id: customerId,
      name: `Customer ${customerId.substring(0, 8)}...`,
      country: "IN",
      currency: "INR",
      customer_type: "individual"
    }
  };

  return await safeFetch(CUSTOMER_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${LAGO_API_KEY}`,
      'Idempotency-Key': `create_${customerId}_${Date.now()}`
    },
    body: JSON.stringify(customerData)
  });
}

// Helper function to get customer subscriptions
async function getCustomerSubscriptions(customerId: string): Promise<{
  subscriptions?: Array<{
    id: string;
    external_id: string;
    plan_code: string;
    status: string;
    customer_id: string;
    created_at: string;
  }>;
} | null> {
  try {
    const response = await safeFetch(`${CUSTOMER_URL}/${customerId}/subscriptions`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (response.ok) {
      return await response.json();
    }
    return null;
  } catch (error) {
    console.error('Error getting customer subscriptions:', error);
    return null;
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: corsHeaders,
  });
}

// Handle GET requests - Get customer subscriptions
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const customerId = url.searchParams.get('customerId');
  const subscriptionId = url.searchParams.get('subscriptionId');

  if (!customerId && !subscriptionId) {
    return NextResponse.json({ error: 'Missing customerId or subscriptionId parameter' }, {
      status: 400,
      headers: corsHeaders
    });
  }

  try {
    // If subscriptionId is provided, get details for that subscription
    if (subscriptionId) {
      const response = await safeFetch(`${SUBSCRIPTION_URL}/${subscriptionId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json(data, { headers: corsHeaders });
      } else {
        const error = await response.text();
        console.error(`❌ API - Error fetching subscription: ${response.status}`, error);
        return NextResponse.json({ error: 'Error fetching subscription' }, {
          status: response.status,
          headers: corsHeaders
        });
      }
    }

    // If customerId is provided, get all subscriptions for customer
    if (customerId) {
      const sanitizedId = sanitizeKeycloakId(customerId) || customerId;
      const response = await safeFetch(`${CUSTOMER_URL}/${sanitizedId}/subscriptions`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json(data, { headers: corsHeaders });
      } else {
        const error = await response.text();
        console.error(`❌ API - Error fetching customer subscriptions: ${response.status}`, error);
        return NextResponse.json({ error: 'Error fetching customer subscriptions' }, {
          status: response.status,
          headers: corsHeaders
        });
      }
    }
  } catch (error) {
    console.error('❌ API - Exception in GET /api/lago/subscription:', error);
    return NextResponse.json({ error: 'Server error' }, {
      status: 500,
      headers: corsHeaders
    });
  }
}

// Handle POST requests - Create subscription
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("Subscription API - POST request body:", body);

    // Check for required fields
    if (!body.external_customer_id || !body.plan_code) {
      console.error("Subscription API - Missing required fields:", {
        hasCustomerId: !!body.external_customer_id,
        hasPlanCode: !!body.plan_code,
      });
      return NextResponse.json(
        { error: "Missing required fields: external_customer_id and plan_code are required" },
        { status: 400, headers: corsHeaders }
      );
    }

    const productCode = extractProductCode(body.plan_code);
    console.log(`Subscription API - Extracted product code: ${productCode} from plan: ${body.plan_code}`);

    // Define known product prefixes that should skip normalization
    const knownProductPrefixes = ['onesociety_', 'onegate_', 'onepos_', 'onefooddialer_'];
    const shouldSkipNormalization = knownProductPrefixes.some(prefix => body.plan_code.startsWith(prefix));

    if (shouldSkipNormalization) {
      // Skip normalization for known product codes to preserve original format
      console.log(`Subscription API - Using original plan code for known product: ${body.plan_code}`);
    } else {
      // Normalize only for unknown product codes
      const normalizedPlanCode = validateAndNormalizePlanCode(
        body.plan_code,
        {
          productName: productCode || undefined,
          isTrial: body.is_trial || false
        }
      );

      if (normalizedPlanCode !== body.plan_code) {
        console.log(`Subscription API - Plan code normalized: ${body.plan_code} → ${normalizedPlanCode}`);
        body.plan_code = normalizedPlanCode;
      }
    }

    // Generate external_id if not provided (format: userId@planCode)
    if (!body.external_id) {
      body.external_id = `${body.external_customer_id}@${body.plan_code}`;
      console.log(`Subscription API - Generated external_id: ${body.external_id}`);
    }

    // Check if customer exists
    const customerExists = await checkIfCustomerExists(body.external_customer_id);
    console.log(`Subscription API - Customer exists check: ${customerExists}`);

    if (!customerExists) {
      // Create a minimal customer entry if it doesn't exist
      console.log(`Subscription API - Creating customer: ${body.external_customer_id}`);
      const createCustomerResponse = await createMinimalCustomer(body.external_customer_id);

      if (!createCustomerResponse.ok) {
        const errorData = await createCustomerResponse.json();
        console.error("Subscription API - Failed to create customer:", errorData);
        return NextResponse.json(
          { error: "Failed to create customer", details: errorData },
          { status: 500, headers: corsHeaders }
        );
      }
    }

    // Check if subscription already exists to avoid duplicates
    const existingSubscriptions = await getCustomerSubscriptions(body.external_customer_id);
    const existingSubscription = existingSubscriptions?.subscriptions?.find(
      (sub: { plan_code: string; status: string }) => sub.plan_code === body.plan_code && sub.status !== "terminated"
    );

    if (existingSubscription) {
      console.log(`Subscription API - Subscription already exists: ${existingSubscription.external_id}`);
      return NextResponse.json(
        { message: "Subscription already exists", subscription: existingSubscription },
        { status: 200, headers: corsHeaders }
      );
    }

    // Generate idempotency key to prevent duplicate requests
    const idempotencyKey = `${body.external_customer_id}_${body.plan_code}_${Date.now()}`;
    console.log(`Subscription API - Generated idempotency key: ${idempotencyKey}`);

    // If subscription_at is not provided, set it to 30 seconds behind current time
    if (!body.subscription_at) {
      const now = new Date();
      const adjustedNow = new Date(now.getTime() - 30 * 1000);
      body.subscription_at = adjustedNow.toISOString();
      console.log(`🕒 Subscription API - Using adjusted time: ${body.subscription_at} (30 seconds behind actual time: ${now.toISOString()})`);
    }

    // Create subscription
    const response = await safeFetch(SUBSCRIPTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`,
        'Idempotency-Key': idempotencyKey
      },
      body: JSON.stringify(body)
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("Subscription API - Failed to create subscription:", responseData);
      return NextResponse.json(
        { error: "Failed to create subscription", details: responseData },
        { status: response.status, headers: corsHeaders }
      );
    }

    console.log("Subscription API - Successfully created subscription:", {
      external_id: responseData.subscription.external_id,
      plan_code: responseData.subscription.plan_code,
      status: responseData.subscription.status,
    });

    return NextResponse.json(responseData, { status: 200, headers: corsHeaders });
  } catch (error) {
    console.error("Subscription API - Unexpected error:", error);
    return NextResponse.json(
      { error: "Failed to process subscription request", message: error instanceof Error ? error.message : String(error) },
      { status: 500, headers: corsHeaders }
    );
  }
}

// Handle DELETE requests - Cancel subscription
export async function DELETE(request: NextRequest) {
  const url = new URL(request.url);
  const subscriptionId = url.searchParams.get('id');

  if (!subscriptionId) {
    return NextResponse.json({ error: 'Missing subscription id parameter' }, { status: 400, headers: corsHeaders });
  }

  try {
    const response = await safeFetch(`${SUBSCRIPTION_URL}/${subscriptionId}`, {
      method: 'DELETE',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    console.log(`🔄 API - Subscription cancellation response: ${response.status}`);

    if (response.ok) {
      console.log(`✅ API - Subscription cancelled successfully: ${subscriptionId}`);
      return NextResponse.json({ success: true }, { headers: corsHeaders });
    } else {
      const error = await response.text();
      console.error(`❌ API - Error cancelling subscription: ${response.status}`, error);
      return NextResponse.json({ error: 'Error cancelling subscription' }, {
        status: response.status,
        headers: corsHeaders
      });
    }
  } catch (error) {
    console.error('❌ API - Exception in DELETE /api/lago/subscription:', error);
    return NextResponse.json({ error: 'Server error' }, {
      status: 500,
      headers: corsHeaders
    });
  }
}