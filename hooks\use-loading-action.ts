"use client";

import { useState, useCallback } from 'react';
import { useLoading } from '@/context/loading-context';

/**
 * Custom hook for managing loading states during async operations 
 * such as API calls, data fetching, or other async actions
 * 
 * @param showPageLoader Whether to trigger the full-page loader
 * @returns Loading state and wrapped action function
 */
export function useLoadingAction(showPageLoader = false) {
  const [isLoading, setIsLoading] = useState(false);
  const { startLoading, stopLoading } = useLoading();

  /**
   * Wraps an async function with loading state management
   * 
   * @param asyncFn The async function to execute
   * @returns A function that executes the provided function with loading state management
   */
  const withLoading = useCallback(<T, Args extends any[]>(
    asyncFn: (...args: Args) => Promise<T>
  ) => {
    return async (...args: Args): Promise<T> => {
      try {
        setIsLoading(true);
        if (showPageLoader) startLoading();
        
        const result = await asyncFn(...args);
        
        return result;
      } finally {
        setIsLoading(false);
        if (showPageLoader) stopLoading();
      }
    };
  }, [showPageLoader, startLoading, stopLoading]);

  return {
    isLoading,
    withLoading,
  };
}

/**
 * Creates a wrapped version of fetch that shows the page loader
 * Use this for data fetching operations that should show the full page loader
 */
export function usePageLoadingFetch() {
  const { startLoading, stopLoading } = useLoading();

  const fetchWithPageLoading = useCallback(async (
    input: RequestInfo | URL, 
    init?: RequestInit
  ) => {
    try {
      startLoading();
      return await fetch(input, init);
    } finally {
      stopLoading();
    }
  }, [startLoading, stopLoading]);

  return fetchWithPageLoading;
}

export default useLoadingAction; 