/**
 * Shared types for the application
 */

/**
 * Trial plan information from CMS
 */
export interface TrialPlan {
  name: string;
  tag?: string;
  plan_code: string;
  trialDurationInDays: number;
  features?: { feature: string; isIncluded: boolean; id: string }[];
  description?: string;
  button?: { text: string; id?: string };
}

/**
 * Subscription plan information from CMS
 */
export interface SubscriptionPlan {
  monthlyPricing: number;
  yearlyPricing: number;
  tag?: string;
  name: string;
  plan_code_monthly: string;
  plan_code_yearly: string;
  id: string;
  description?: string;
  button?: { text: string; id?: string };
  fetaures?: { feature: string; isIncluded: boolean; id: string }[];
}

/**
 * Enterprise plan information from CMS
 */
export interface EnterprisePlan {
  name: string;
  tag?: string;
  description?: string;
  id: string;
  button?: { text: string; id?: string };
  features?: { feature: string; isIncluded: boolean; id?: string }[];
}

/**
 * Product information from CMS
 */
export interface ProductInfo {
  productName: string;
  productLogo?: string | null;
}

/**
 * Complete pricing information for a product
 */
export interface PricingInfo {
  productInfo: ProductInfo;
  trialPlan?: TrialPlan;
  subscriptionPlan?: SubscriptionPlan[];
  enterprisePlan?: EnterprisePlan;
}

/**
 * Status response for trial eligibility check
 */
export interface TrialEligibilityResult {
  hasUsedTrial: boolean;
  isPending: boolean;
  trialPlanCode?: string;
  message?: string;
  relatedToProductFamily?: boolean;
  hasPaidPlanInFamily?: boolean;
}

/**
 * Information about a product family
 */
export interface ProductFamilyInfo {
  hasAnyPlan: boolean;
  hasPaidPlan: boolean;
  hasTrialPlan: boolean;
  hasActivePlan: boolean;
  hasExpiredPlan: boolean;
  plans: unknown[]; // Subscription[]
}

/**
 * Subscription status information
 */
export interface SubscriptionStatus {
  isLoading: boolean;
  hasActiveSubscription: boolean;
  hasExpiredSubscription: boolean;
  hasPendingSubscription?: boolean;
  hasUsedTrial: boolean;
  trialStatus?: string;
  subscriptionDetails?: {
    planName: string;
    isPaidPlan: boolean;
    isTrialPlan: boolean;
    isPending?: boolean;
    daysRemaining?: number;
    expirationDate?: string;
  };
} 