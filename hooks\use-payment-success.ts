"use client";

import { useEffect, useState } from 'react';
import { useCart } from '@/context/cart-context';
import { useCookies } from '@/hooks/use-cookies';
import { toast } from 'sonner';

export function usePaymentSuccess() {
  const { clearCart, items } = useCart();
  const [alreadyCleared, setAlreadyCleared] = useState(false);
  const cookies = useCookies();

  useEffect(() => {
    // First time component mounts, check if we need to clear cart
    if (!alreadyCleared && items.length > 0) {
      checkForPaymentSuccess();
    }
  }, [items, alreadyCleared]);
  
  // Check both cookies and localStorage for payment success
  const checkForPaymentSuccess = () => {
    try {
      // Method 1: Check cookies first (set by middleware)
      const cookieSuccessTimestamp = cookies.get('payment_success_timestamp');
      const cookieCartCleared = cookies.get('cart_cleared');
      
      // Method 2: Check localStorage (set by client)
      const localSuccessTimestamp = localStorage.getItem('payment_success_timestamp');
      const localCartCleared = localStorage.getItem('cart_cleared');
      
      // Log debug info
      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 PAYMENT SUCCESS - Debug Info:", {
          cookieSuccessTimestamp,
          cookieCartCleared,
          localSuccessTimestamp,
          localCartCleared,
          hasItems: items.length > 0
        });
      }
      
      // Check if we have success timestamps from either source
      const shouldClearFromCookies = cookieSuccessTimestamp && cookieCartCleared === 'true';
      const shouldClearFromLocal = localSuccessTimestamp && localCartCleared !== 'true';
      
      if (shouldClearFromCookies || shouldClearFromLocal) {
        // Verify timestamp is recent (within the last 24 hours)
        const timestampString = shouldClearFromCookies ? cookieSuccessTimestamp : localSuccessTimestamp;
        const timestamp = parseInt(timestampString || '0', 10);
        const now = Date.now();
        const oneDayInMs = 24 * 60 * 60 * 1000;
        
        if (!isNaN(timestamp) && (now - timestamp) < oneDayInMs) {
          console.log("✅ PAYMENT SUCCESS - Detected successful payment, clearing cart");
          clearCart();
          setAlreadyCleared(true);
          
          // Mark cart as cleared in localStorage to prevent repeated clearing
          localStorage.setItem('cart_cleared', 'true');
          
          // Optional notification
          toast.success("Your cart has been cleared after successful payment");
          
          // Clear cookies using our custom hook
          if (shouldClearFromCookies) {
            cookies.remove('payment_success_timestamp');
            cookies.remove('cart_cleared');
          }
        }
      }
    } catch (error) {
      console.error("❌ PAYMENT SUCCESS - Error checking payment success:", error);
    }
  };

  // Add storage event listener to detect changes from other tabs
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'payment_success_timestamp' && !alreadyCleared && items.length > 0) {
        console.log("🔄 PAYMENT SUCCESS - Storage change detected, checking for payment success");
        checkForPaymentSuccess();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [alreadyCleared, items]);

  return null;
}
