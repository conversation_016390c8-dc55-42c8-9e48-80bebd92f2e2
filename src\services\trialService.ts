/**
 * Service for trial-related operations
 */

import { LAGO_API_URL, LAGO_API_KEY } from '@/src/constants/api';
import { TrialEligibilityResult } from '@/src/constants/types';

/**
 * Enhanced subscription type with better typing
 */
export interface Subscription {
  id: string;
  plan_code: string;
  plan_name?: string;
  status: string;
  external_id: string;
  amount_cents: number;
  amount_currency: string;
  created_at: string;
  started_at?: string;
  canceled_at?: string;
  ending_at?: string;
  subscription_at?: string;
  terminated_at?: string;
  next_billing_at?: string;
}

/**
 * Result type for subscription check
 */
export interface SubscriptionCheckResult {
  hasPlan: boolean;
  isPaidPlan: boolean;
  isTrialPlan: boolean;
  isExpired: boolean;
  planName?: string;
  planCode?: string;
  planExpiration?: string;
  isActive: boolean;
  isPending: boolean;
  daysRemaining?: number;
  subscription?: Subscription;
  status?: string;
}

/**
 * Check if a user has already used a trial for a specific plan or has a pending trial
 * @param userId Keycloak ID of the user
 * @param planCode The trial plan code to check
 * @returns Object with information about trial status { hasUsed: boolean, isPending: boolean, status: string }
 */
export async function hasUserUsedTrial(
  userId: string,
  planCode: string
): Promise<{ hasUsed: boolean; isPending: boolean; status?: string }> {
  if (!userId || !planCode) {
    console.error("❌ TRIAL - Invalid parameters for trial check:", { userId, planCode });
    return { hasUsed: false, isPending: false };
  }

  try {
    console.log(`🔍 TRIAL - Checking trial status for user ${userId} and plan ${planCode}`);

    // Use the recommended endpoint with query parameters for active/pending subscriptions
    const activeUrl = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&plan_code=${encodeURIComponent(planCode)}&status[]=active&status[]=pending`;

    const activeResponse = await fetch(activeUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (activeResponse.ok) {
      const activeData = await activeResponse.json();
      const activeSubscriptions = activeData.subscriptions || [];

      if (activeSubscriptions.length > 0) {
        const subscription = activeSubscriptions[0];
        const isPending = isSubscriptionPending(subscription);

        console.log(`✅ TRIAL - Found active/pending subscription for ${planCode} with status: ${subscription.status}`);

        return {
          hasUsed: true,
          isPending,
          status: subscription.status
        };
      }
    } else {
      console.error(`❌ TRIAL - Error checking active subscriptions: ${activeResponse.status}`);
    }

    // Check for any subscriptions with this plan code (including expired ones)
    const allUrl = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&plan_code=${encodeURIComponent(planCode)}`;

    const allResponse = await fetch(allUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!allResponse.ok) {
      const errorText = await allResponse.text();
      console.error(`❌ TRIAL - Error checking all subscriptions (${allResponse.status}):`, errorText);

      // If we get a 404, it means the customer doesn't exist or has no subscriptions
      if (allResponse.status === 404) {
        return { hasUsed: false, isPending: false };
      }

      // For other errors, assume the verification failed
      throw new Error(`Error checking trial status: ${allResponse.status} ${errorText}`);
    }

    const allData = await allResponse.json();
    const subscriptions = allData.subscriptions || [];

    console.log(`✅ TRIAL - Found ${subscriptions.length} total subscriptions for plan ${planCode}`);

    if (subscriptions.length > 0) {
      // User has used this trial before (but it's not active or pending)
      return {
        hasUsed: true,
        isPending: false,
        status: 'expired'
      };
    }

    return { hasUsed: false, isPending: false };
  } catch (error) {
    console.error("❌ TRIAL - Exception checking trial status:", error);
    // In case of error, we'll assume the user hasn't used the trial
    return { hasUsed: false, isPending: false };
  }
}

/**
 * Get all active subscriptions for a user with better typing
 * @param userId Keycloak ID of the user
 * @returns Array of subscription objects
 */
export async function getUserSubscriptions(userId: string): Promise<Subscription[]> {
  if (!userId) {
    console.error("❌ TRIAL - Cannot get subscriptions: No user ID provided");
    return [];
  }

  try {
    console.log(`🔍 TRIAL - Fetching subscriptions for user ${userId}`);

    const response = await fetch(`${LAGO_API_URL}/customers/${userId}/subscriptions`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ TRIAL - Error fetching subscriptions (${response.status}):`, errorText);

      if (response.status === 404) {
        return [];
      }

      throw new Error(`Error fetching subscriptions: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    return data.subscriptions || [];
  } catch (error) {
    console.error("❌ TRIAL - Exception fetching subscriptions:", error);
    return [];
  }
}

/**
 * Enhanced product code extraction
 * @param planCode The full plan code
 * @returns The extracted product code or the original if no pattern is found
 */
export function extractProductCode(planCode: string): string {
  if (!planCode) return '';

  // Normalize the plan code to lowercase
  const normalizedPlanCode = planCode.toLowerCase();

  // Handle common naming convention: productname_plantype_duration
  // Examples:
  // 1. onerestro_starter_m -> onerestro   (monthly)
  // 2. onerestro_pro_y -> onerestro       (yearly)
  // 3. onebiz_trial -> onebiz             (trial)
  // 4. oneportal_enterprise_q -> oneportal (quarterly)

  // Pattern to match: productname_plantype_[m|y|q|w] or productname_plantype
  const productPlanMatch = normalizedPlanCode.match(/^([a-z0-9_]+?)_(starter|basic|standard|premium|pro|enterprise|trial)(?:_[myqw])?$/i);
  if (productPlanMatch && productPlanMatch[1]) {
    return productPlanMatch[1];
  }

  // Fallback to original patterns
  // Handle underscore separators with suffix patterns
  const underscoreMatch = normalizedPlanCode.match(/^([a-z0-9_]+?)_(monthly|yearly|quarterly|weekly|trial|standard|premium|basic|pro|enterprise)/i);
  if (underscoreMatch && underscoreMatch[1]) {
    return underscoreMatch[1];
  }

  // Handle dash separators
  const dashMatch = normalizedPlanCode.match(/^([a-z0-9\-]+?)\-(monthly|yearly|quarterly|weekly|trial|standard|premium|basic|pro|enterprise)/i);
  if (dashMatch && dashMatch[1]) {
    return dashMatch[1];
  }

  // Handle simple product_trial pattern
  const trialMatch = normalizedPlanCode.match(/^([a-z0-9_\-]+?)[\-_]trial$/i);
  if (trialMatch && trialMatch[1]) {
    return trialMatch[1];
  }

  // If no pattern found, just return the original code
  return normalizedPlanCode;
}

/**
 * Check if a user has any existing subscription for a specific product
 * @param userId Keycloak ID of the user
 * @param productCode The product code to check
 * @returns Object containing subscription info if found, null otherwise
 */
export async function checkUserProductSubscription(
  userId: string,
  productCode: string
): Promise<SubscriptionCheckResult | null> {
  if (!userId || !productCode) {
    console.error("❌ SUBSCRIPTION CHECK - Invalid parameters:", { userId, productCode });
    return null;
  }

  try {
    console.log(`🔍 SUBSCRIPTION CHECK - Checking subscriptions for user ${userId} and product ${productCode}`);

    // Normalize the product code to lowercase for case-insensitive comparison
    const normalizedProductCode = productCode.toLowerCase();

    // Get all user subscriptions
    const subscriptions = await getUserSubscriptions(userId);

    if (!subscriptions || subscriptions.length === 0) {
      console.log(`ℹ️ SUBSCRIPTION CHECK - No subscriptions found for user ${userId}`);
      return null;
    }

    // Find subscriptions for this product by comparing product codes
    const productSubscriptions = subscriptions.filter((sub) => {
      if (!sub.plan_code) return false;

      // Extract product code from plan code and compare
      const subProductCode = extractProductCode(sub.plan_code);
      return subProductCode === normalizedProductCode;
    });

    if (productSubscriptions.length === 0) {
      console.log(`ℹ️ SUBSCRIPTION CHECK - No subscription for product ${productCode} found`);
      return null;
    }

    // Sort subscriptions by status and date to find the most relevant one:
    // 1. Active, non-trial subscriptions
    // 2. Active trial subscriptions
    // 3. Expired subscriptions, most recent first
    const sortedSubscriptions = productSubscriptions.sort((a, b) => {
      // Check if one is active and the other isn't
      const aIsActive = isSubscriptionActive(a);
      const bIsActive = isSubscriptionActive(b);

      if (aIsActive && !bIsActive) return -1;
      if (!aIsActive && bIsActive) return 1;

      // Both are active or both inactive, check if one is trial
      const aIsTrial = isSubscriptionTrial(a);
      const bIsTrial = isSubscriptionTrial(b);

      // Prioritize non-trial over trial if both active
      if (aIsActive && bIsActive) {
        if (!aIsTrial && bIsTrial) return -1;
        if (aIsTrial && !bIsTrial) return 1;
      }

      // If all else equal, compare by ending date (most distant first for active, most recent first for expired)
      const aEndDate = a.ending_at ? new Date(a.ending_at).getTime() : 0;
      const bEndDate = b.ending_at ? new Date(b.ending_at).getTime() : 0;

      return aIsActive ? bEndDate - aEndDate : aEndDate - bEndDate;
    });

    // Take the most relevant subscription
    const subscription = sortedSubscriptions[0];

    // Check if it's active
    const isActive = isSubscriptionActive(subscription);

    // Determine if it's a trial plan based on naming convention or price
    const isTrialPlan = isSubscriptionTrial(subscription);

    // Get days remaining
    const { daysRemaining, isExpired } = getSubscriptionTimeRemaining(subscription);

    console.log(`✅ SUBSCRIPTION CHECK - Found ${isTrialPlan ? 'trial' : 'paid'} subscription for product ${productCode}`, {
      plan: subscription.plan_code,
      active: isActive,
      expires: subscription.ending_at,
      daysRemaining
    });

    return {
      hasPlan: true,
      isPaidPlan: !isTrialPlan,
      isTrialPlan,
      isExpired,
      planName: subscription.plan_name || subscription.plan_code,
      planCode: subscription.plan_code,
      planExpiration: subscription.ending_at,
      isActive,
      isPending: isSubscriptionPending(subscription),
      daysRemaining,
      subscription
    };
  } catch (error) {
    console.error(`❌ SUBSCRIPTION CHECK - Error checking product subscription:`, error);
    return null;
  }
}

/**
 * Helper to check if a subscription is active
 */
function isSubscriptionActive(subscription: Subscription): boolean {
  if (!subscription) return false;

  // Check for a terminated_at or canceled_at date in the past
  const now = new Date();
  const isTerminated = subscription.terminated_at && new Date(subscription.terminated_at) <= now;
  const isCanceled = subscription.canceled_at && new Date(subscription.canceled_at) <= now;

  // Check if it has expired
  const isExpired = subscription.ending_at && new Date(subscription.ending_at) <= now;

  // Check for active status
  const hasActiveStatus = subscription.status === 'active';

  return hasActiveStatus && !isTerminated && !isCanceled && !isExpired;
}

/**
 * Helper to check if a subscription is pending activation
 */
function isSubscriptionPending(subscription: Subscription): boolean {
  if (!subscription) return false;

  // Check for pending status
  return subscription.status === 'pending';
}

/**
 * Helper to check if a subscription is a trial
 */
function isSubscriptionTrial(subscription: Subscription): boolean {
  if (!subscription) return false;

  // Check if it's a trial based on plan code
  const isTrialByCode = subscription.plan_code &&
    subscription.plan_code.toLowerCase().includes('trial');

  // Check if it's a free plan (amount is 0)
  const isFree = subscription.amount_cents === 0;

  // Check for short duration (less than 31 days)
  const isShortDuration = isShortSubscriptionDuration(subscription);

  return isTrialByCode || (isFree && isShortDuration);
}

/**
 * Helper to check if a subscription has a short duration (typical for trials)
 */
function isShortSubscriptionDuration(subscription: Subscription): boolean {
  if (!subscription || !subscription.started_at || !subscription.ending_at) {
    return false;
  }

  const startDate = new Date(subscription.started_at);
  const endDate = new Date(subscription.ending_at);
  const durationMs = endDate.getTime() - startDate.getTime();
  const durationDays = durationMs / (1000 * 60 * 60 * 24);

  // Most trials are <= 30 days
  return durationDays <= 30;
}

/**
 * Helper to calculate days remaining and check if expired
 */
function getSubscriptionTimeRemaining(subscription: Subscription): {
  daysRemaining: number | undefined;
  isExpired: boolean
} {
  if (!subscription || !subscription.ending_at) {
    return { daysRemaining: undefined, isExpired: false };
  }

  const now = new Date();
  const endDate = new Date(subscription.ending_at);

  // Check if expired
  const isExpired = endDate <= now;

  if (isExpired) {
    return { daysRemaining: 0, isExpired: true };
  }

  // Calculate days remaining
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return { daysRemaining: diffDays, isExpired: false };
}

/**
 * Check if a user has subscribed to a specific plan directly
 * @param userId Keycloak ID of the user
 * @param planCode The specific plan code to check
 * @returns Subscription details if found, null otherwise
 */
export async function checkSpecificSubscription(
  userId: string,
  planCode: string
): Promise<Subscription | null> {
  if (!userId || !planCode) {
    console.error("❌ SUBSCRIPTION CHECK - Invalid parameters:", { userId, planCode });
    return null;
  }

  try {
    // Use the recommended endpoint with query parameters
    const url = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&plan_code=${encodeURIComponent(planCode)}&status[]=active&status[]=pending`;

    console.log(`🔍 SUBSCRIPTION CHECK - Checking specific subscription for plan: ${planCode}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ SUBSCRIPTION CHECK - API error (${response.status}):`, errorText);
      return null;
    }

    const data = await response.json();
    const subscriptions = data.subscriptions || [];

    if (subscriptions.length === 0) {
      console.log(`ℹ️ SUBSCRIPTION CHECK - No active/pending subscription found for plan ${planCode}`);
      return null;
    }

    console.log(`✅ SUBSCRIPTION CHECK - Found subscription for plan ${planCode}`);
    return subscriptions[0] as Subscription;
  } catch (error) {
    console.error(`❌ SUBSCRIPTION CHECK - Error checking specific subscription:`, error);
    return null;
  }
}

/**
 * Check if a user has a specific subscription for a product and get the details
 * @param userId Keycloak ID of the user
 * @param productSlug The product slug from CMS
 * @param planCode The plan code to check for this product
 * @returns Detailed subscription result with status information
 */
export async function checkProductSubscription(
  userId: string,
  productSlug: string,
  planCode: string
): Promise<SubscriptionCheckResult | null> {
  if (!userId || !productSlug) {
    console.error("❌ SUBSCRIPTION CHECK - Invalid parameters:", { userId, productSlug });
    return null;
  }

  try {
    console.log(`🔍 SUBSCRIPTION CHECK - Checking subscriptions for: ${productSlug} (${planCode})`);

    // First, try direct check for the specific plan using the query parameter endpoint
    if (planCode) {
      const url = `${LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(userId)}&plan_code=${encodeURIComponent(planCode)}&status[]=active&status[]=pending`;

      console.log(`🔍 SUBSCRIPTION CHECK - Checking specific plan: ${planCode}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const subscriptions = data.subscriptions || [];

        if (subscriptions.length > 0) {
          // We found a direct match for this plan
          const directSubscription = subscriptions[0];
          const isTrialPlan = isSubscriptionTrial(directSubscription);
          const isActive = isSubscriptionActive(directSubscription);
          const { daysRemaining, isExpired } = getSubscriptionTimeRemaining(directSubscription);

          console.log(`✅ SUBSCRIPTION CHECK - Found active/pending subscription for plan ${planCode}`);

          return {
            hasPlan: true,
            isPaidPlan: !isTrialPlan,
            isTrialPlan,
            isExpired,
            planName: directSubscription.plan_name || planCode,
            planCode: directSubscription.plan_code,
            planExpiration: directSubscription.ending_at,
            isActive,
            isPending: isSubscriptionPending(directSubscription),
            daysRemaining,
            subscription: directSubscription
          };
        }
      } else {
        console.error(`❌ SUBSCRIPTION CHECK - Error checking specific plan: ${response.status}`);
      }
    }

    // If no direct match found, check if user has any plans for this product
    // First normalize the product slug to a format that might match plan codes
    // e.g. product-name -> product_name
    const normalizedSlug = productSlug.toLowerCase().replace(/-/g, '_');

    // Extract product code from the plan code or use normalized slug
    const productCode = planCode ? extractProductCode(planCode) : normalizedSlug;

    console.log(`🔍 SUBSCRIPTION CHECK - Checking product family: ${productCode} for slug: ${productSlug}`);

    // Get all user's subscriptions and find matches for this product
    return await checkUserProductSubscription(userId, productCode);
  } catch (error) {
    console.error("❌ SUBSCRIPTION CHECK - Error in product subscription check:", error);
    return null;
  }
}

/**
 * Check if a user exists in Lago and create them if they don't
 * @param userId Keycloak ID of the user
 * @param email User's email address
 * @param name User's full name
 * @returns Boolean indicating if the user exists or was created successfully
 */
export async function ensureUserExists(
  userId: string,
  email?: string,
  name?: string
): Promise<boolean> {
  if (!userId) {
    console.error("❌ USER CHECK - Invalid user ID");
    return false;
  }

  try {
    // First, check if the user exists
    console.log(`🔍 USER CHECK - Checking if user ${userId} exists in Lago`);

    const checkResponse = await fetch(`${LAGO_API_URL}/customers/${userId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      }
    });

    if (checkResponse.ok) {
      console.log(`✅ USER CHECK - User ${userId} already exists in Lago`);
      return true;
    }

    // If user doesn't exist (404) or other error, try to create them
    console.log(`ℹ️ USER CHECK - Creating new user ${userId} in Lago`);

    const createResponse = await fetch(`${LAGO_API_URL}/customers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${LAGO_API_KEY}`
      },
      body: JSON.stringify({
        customer: {
          external_id: userId,
          email: email || undefined,
          name: name || undefined
        }
      })
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.error(`❌ USER CHECK - Failed to create user (${createResponse.status}):`, errorText);
      return false;
    }

    console.log(`✅ USER CHECK - Successfully created user ${userId} in Lago`);
    return true;
  } catch (error) {
    console.error("❌ USER CHECK - Exception checking/creating user:", error);
    return false;
  }
}

/**
 * Check if a user has any plans (trial or paid) for a product family
 * @param userId Keycloak ID of the user
 * @param productCode The product code/name to check
 * @returns Results indicating if user has any plans for this product
 */
export async function checkUserProductFamily(
  userId: string,
  productCode: string
): Promise<{
  hasAnyPlan: boolean;
  hasPaidPlan: boolean;
  hasTrialPlan: boolean;
  hasActivePlan: boolean;
  hasExpiredPlan: boolean;
  plans: Subscription[];
}> {
  if (!userId || !productCode) {
    console.error("❌ PRODUCT FAMILY CHECK - Invalid parameters:", { userId, productCode });
    return {
      hasAnyPlan: false,
      hasPaidPlan: false,
      hasTrialPlan: false,
      hasActivePlan: false,
      hasExpiredPlan: false,
      plans: []
    };
  }

  try {
    console.log(`🔍 PRODUCT FAMILY CHECK - Checking plans for user ${userId} and product family: ${productCode}`);

    // Normalize the product code to lowercase for case-insensitive comparison
    const normalizedProductCode = productCode.toLowerCase();

    // Get all user subscriptions
    const subscriptions = await getUserSubscriptions(userId);

    if (!subscriptions || subscriptions.length === 0) {
      console.log(`ℹ️ PRODUCT FAMILY CHECK - No subscriptions found for user ${userId}`);
      return {
        hasAnyPlan: false,
        hasPaidPlan: false,
        hasTrialPlan: false,
        hasActivePlan: false,
        hasExpiredPlan: false,
        plans: []
      };
    }

    // Find all subscriptions for this product family
    const productSubscriptions = subscriptions.filter((sub) => {
      if (!sub.plan_code) return false;

      // Extract product code from plan code and compare
      const subProductCode = extractProductCode(sub.plan_code);
      return subProductCode === normalizedProductCode;
    });

    if (productSubscriptions.length === 0) {
      console.log(`ℹ️ PRODUCT FAMILY CHECK - No plans for product family ${productCode} found`);
      return {
        hasAnyPlan: false,
        hasPaidPlan: false,
        hasTrialPlan: false,
        hasActivePlan: false,
        hasExpiredPlan: false,
        plans: []
      };
    }

    // Analyze the found plans
    let hasPaidPlan = false;
    let hasTrialPlan = false;
    let hasActivePlan = false;
    let hasExpiredPlan = false;

    for (const sub of productSubscriptions) {
      const isActive = isSubscriptionActive(sub);
      const isTrial = isSubscriptionTrial(sub);
      const isExpired = sub.ending_at ? new Date(sub.ending_at) <= new Date() : false;

      if (isActive) hasActivePlan = true;
      if (isExpired) hasExpiredPlan = true;
      if (isTrial) hasTrialPlan = true;
      if (!isTrial) hasPaidPlan = true;
    }

    console.log(`✅ PRODUCT FAMILY CHECK - Found ${productSubscriptions.length} plans for product family ${productCode}:`, {
      hasPaidPlan,
      hasTrialPlan,
      hasActivePlan,
      hasExpiredPlan
    });

    return {
      hasAnyPlan: true,
      hasPaidPlan,
      hasTrialPlan,
      hasActivePlan,
      hasExpiredPlan,
      plans: productSubscriptions
    };
  } catch (error) {
    console.error(`❌ PRODUCT FAMILY CHECK - Error checking product family:`, error);
    return {
      hasAnyPlan: false,
      hasPaidPlan: false,
      hasTrialPlan: false,
      hasActivePlan: false,
      hasExpiredPlan: false,
      plans: []
    };
  }
}

/**
 * Check if a user has already used a trial for a specific product identified by slug
 * @param userId Keycloak ID of the user
 * @param productSlug The product slug to check for trial usage
 * @returns Promise resolving to an object with trial usage status and details
 */
export async function hasUserUsedTrialForProduct(
  userId: string,
  productSlug: string
): Promise<TrialEligibilityResult> {
  if (!userId || !productSlug) {
    console.error("❌ TRIAL CHECK - Invalid parameters:", { userId, productSlug });
    return { hasUsedTrial: false, isPending: false };
  }

  try {
    console.log(`🔍 TRIAL CHECK - Checking if user ${userId} has used trial for product: ${productSlug}`);

    // First, fetch the product's trial plan information using the pricing API
    const apiUrl = `/api/pricing/${encodeURIComponent(productSlug)}`;
    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error(`❌ TRIAL CHECK - Error fetching product information (${response.status})`);
      return {
        hasUsedTrial: false,
        isPending: false,
        message: "Could not verify trial eligibility. Please try again later."
      };
    }

    const productData = await response.json();

    // Extract trial plan code from the product data
    const trialPlanCode = productData?.trialPlan?.plan_code;

    if (!trialPlanCode) {
      console.log(`ℹ️ TRIAL CHECK - No trial plan found for product: ${productSlug}`);
      return {
        hasUsedTrial: false,
        isPending: false,
        message: "No trial plan is available for this product."
      };
    }

    // Extract product code from the trial plan code
    const productCode = extractProductCode(trialPlanCode);
    console.log(`✅ TRIAL CHECK - Found trial plan (${trialPlanCode}) for product: ${productSlug}, product family: ${productCode}`);

    // First, check if user has any plans from the same product family
    // This will catch cases where user might have a paid plan or different trial plan from the same product
    const productFamilyCheck = await checkUserProductFamily(userId, productCode);

    if (productFamilyCheck.hasAnyPlan) {
      // User already has some plan from this product family

      if (productFamilyCheck.hasActivePlan) {
        // User has active plan (paid or trial) from same product family
        return {
          hasUsedTrial: true,
          isPending: false,
          trialPlanCode,
          relatedToProductFamily: true,
          hasPaidPlanInFamily: productFamilyCheck.hasPaidPlan,
          message: productFamilyCheck.hasPaidPlan
            ? "You already have a paid plan for this product family."
            : "You already have an active trial for this product family."
        };
      }

      if (productFamilyCheck.hasTrialPlan) {
        // User already had a trial from same product family (expired or pending)
        // Check if any of their plans from this family are in pending state
        const hasPendingPlan = productFamilyCheck.plans.some(plan =>
          isSubscriptionPending(plan) && extractProductCode(plan.plan_code) === productCode
        );

        return {
          hasUsedTrial: true,
          isPending: hasPendingPlan,
          trialPlanCode,
          relatedToProductFamily: true,
          message: hasPendingPlan
            ? "You have a pending trial for this product family."
            : "You have already used a trial for this product family."
        };
      }

      // If user had only paid plans and they're all expired, they might be eligible for a trial
      // Business rule: If all plans in the family are expired paid plans (not trials),
      // we'll allow a trial if business policy permits it
      if (productFamilyCheck.hasPaidPlan && !productFamilyCheck.hasTrialPlan &&
          productFamilyCheck.hasExpiredPlan && !productFamilyCheck.hasActivePlan) {
        // This is a special case - user had paid plans but they're all expired
        // We'll let them take a trial if company policy allows
        const allowTrialAfterExpiredPaidPlans = false; // Set based on your business policy

        if (!allowTrialAfterExpiredPaidPlans) {
          return {
            hasUsedTrial: true,
            isPending: false,
            relatedToProductFamily: true,
            hasPaidPlanInFamily: true,
            trialPlanCode,
            message: "You've previously had a paid subscription in this product family. Please renew your subscription instead of starting a trial."
          };
        }
      }
    }

    // If no plans found from product family, check specifically for this trial plan
    const trialStatus = await hasUserUsedTrial(userId, trialPlanCode);

    return {
      hasUsedTrial: trialStatus.hasUsed,
      isPending: trialStatus.isPending,
      trialPlanCode,
      message: trialStatus.hasUsed
        ? (trialStatus.isPending
            ? "You have a pending trial for this product."
            : "You have already used the trial for this product.")
        : undefined
    };
  } catch (error) {
    console.error("❌ TRIAL CHECK - Exception checking trial usage for product:", error);
    return {
      hasUsedTrial: false,
      isPending: false,
      message: "Error checking trial eligibility. Please try again."
    };
  }
}