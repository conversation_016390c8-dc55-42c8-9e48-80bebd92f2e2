"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import useLoadingAction, { usePageLoadingFetch } from "@/hooks/use-loading-action";
import { useLoading } from "@/context/loading-context";

export function LoadingExample() {
  const [data, setData] = useState<string | null>(null);
  const { isLoading, withLoading } = useLoadingAction(false);
  const fetchWithPageLoading = usePageLoadingFetch();
  const { startLoading, stopLoading } = useLoading();

  // Example 1: Local component loading state
  const handleLocalLoading = withLoading(async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setData("Data loaded with local loading state!");
  });

  // Example 2: Page loading state
  const handlePageLoading = async () => {
    try {
      // Start page loading manually
      startLoading();
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      setData("Data loaded with page loading state!");
    } finally {
      // Always stop loading
      stopLoading();
    }
  };

  // Example 3: Using fetchWithPageLoading
  const handleFetchWithLoading = async () => {
    try {
      // This will automatically show the page loader
      const response = await fetchWithPageLoading('https://jsonplaceholder.typicode.com/todos/1');
      const result = await response.json();
      setData(`Fetched: ${result.title}`);
    } catch (error) {
      console.error("Fetch error:", error);
      setData("Error fetching data");
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4 border rounded-lg">
      <h2 className="text-xl font-bold">Loading States Example</h2>
      
      <div className="flex flex-col gap-2 sm:flex-row">
        <Button 
          onClick={handleLocalLoading} 
          disabled={isLoading}
          className="relative"
        >
          {isLoading ? (
            <>
              <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground"></span>
              Loading...
            </>
          ) : 'Local Loading'}
        </Button>
        
        <Button 
          onClick={handlePageLoading} 
          variant="secondary"
        >
          Page Loading
        </Button>
        
        <Button 
          onClick={handleFetchWithLoading} 
          variant="outline"
        >
          Fetch with Loading
        </Button>
      </div>
      
      {data && (
        <div className="p-4 mt-4 border rounded bg-muted">
          <p>{data}</p>
        </div>
      )}
    </div>
  );
}

export default LoadingExample; 