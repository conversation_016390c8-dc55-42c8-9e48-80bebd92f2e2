import client from "@/lib/apolloClient";
import { gql } from "@apollo/client";

const GET_FEATURES_CONTENT_BY_SLUG = gql`
  query FeaturesBySlug($slug: String!) {
    products(filters: { slug: { eq: $slug } }) {
      sections {
        ... on ComponentSectionsKeyFeatureSection {
          id
          title
          description
          keyfeatures {
            description
            icon
            text
            id
          }
          button {
            text
            id
          }
        }
      }
    }
  }
`;

export const getFeaturesContentBySlug = async (slug: string) => {
  try {
    const { data } = await client.query({
      query: GET_FEATURES_CONTENT_BY_SLUG,
      variables: { slug },
      fetchPolicy: "no-cache",
    });
    return data.products[0]?.sections.find(
      (section: { __typename: string }) => section.__typename === "ComponentSectionsKeyFeatureSection"
    ) || null;
  } catch (error) {
    console.error("Error fetching features section data:", error);
    throw error;
  }
};
