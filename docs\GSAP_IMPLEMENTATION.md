# GSAP Animation Implementation

## Overview

This implementation adds modern GSAP (GreenSock Animation Platform) animations to the product cards and page transitions, following the latest 2025 best practices for smooth, performant animations.

## Features Implemented

### 1. Card Arrow Icon
- Added diagonal arrow (↗) in the top-right corner of each shift-card
- Smooth hover animations with opacity and scale transitions
- Positioned absolutely to not affect card layout

### 2. GSAP Flip Animations
- Implemented GSAP Flip plugin for seamless card-to-page transitions
- Modern animation technique that captures element states and smoothly transitions between them
- Handles complex DOM changes without jarring jumps

### 3. Button Click Animations
- Enhanced button interactions with GSAP timeline animations
- Scale and bounce effects on click for better user feedback
- Smooth transitions that complement the overall design

### 4. Page Transition System
- Created reusable GSAPPageTransition component
- Entrance animations for product pages
- Smooth fade and scale effects

## Files Modified/Created

### New Files:
1. `hooks/useGSAPTransition.ts` - Custom hook for GSAP animations
2. `components/ui/gsap-page-transition.tsx` - Page transition component
3. `docs/GSAP_IMPLEMENTATION.md` - This documentation

### Modified Files:
1. `components/ui/shift-card.tsx` - Added arrow icon
2. `components/ui/shift-card-demo.tsx` - Integrated GSAP animations
3. `app/products/[slug]/page.tsx` - Added page transitions
4. `package.json` - Added GSAP dependency

## How It Works

### Card to Page Transition Flow:

1. **State Capture**: When user clicks "View Product" or "Buy Now", GSAP captures the current state of the card
2. **Button Animation**: Button scales down then bounces back with smooth easing
3. **Card Animation**: Card scales up slightly and fades to create transition effect
4. **Overlay Effect**: Temporary overlay provides smooth visual continuity
5. **Navigation**: Page navigation occurs during the animation
6. **Page Entrance**: New page animates in with fade and scale effects

### Technical Implementation:

```typescript
// Example usage in shift-card-demo.tsx
const handleViewProduct = () => {
  if (cardRef.current && viewButtonRef.current) {
    // Animate button click
    createButtonClickAnimation(viewButtonRef.current)
    
    // Create card to page transition
    createCardToPageTransition(
      cardRef.current,
      () => onViewProduct(),
      {
        duration: 1.0,
        ease: "power2.inOut"
      }
    )
  }
}
```

## Animation Specifications

### Timing:
- Button click animation: 0.6s with back.out(1.7) easing
- Card transition: 1.0s with power2.inOut easing
- Page entrance: 0.8s with power2.out easing

### Effects:
- Scale transformations for depth
- Opacity changes for smooth fades
- Overlay effects for visual continuity
- Responsive to user interactions

## Browser Compatibility

- Modern browsers supporting ES6+
- GSAP handles cross-browser compatibility
- Graceful fallbacks for unsupported features
- SSR-safe implementation with proper checks

## Performance Considerations

- GSAP uses hardware acceleration when possible
- Animations are optimized for 60fps
- Minimal DOM manipulation during transitions
- Proper cleanup to prevent memory leaks

## Usage Examples

### Basic Card Animation:
```tsx
<ShiftCardDemo
  product={product}
  onViewProduct={() => {
    // GSAP animation automatically triggers
    router.push(`/products/${slug}`)
  }}
/>
```

### Custom Page Transition:
```tsx
<GSAPPageTransition>
  <YourPageContent />
</GSAPPageTransition>
```

## Future Enhancements

1. **Scroll-triggered animations** using GSAP ScrollTrigger
2. **Stagger animations** for multiple cards
3. **Morphing transitions** between different layouts
4. **3D transforms** for more dynamic effects
5. **Physics-based animations** for natural movement

## Troubleshooting

### Common Issues:

1. **Animations not working**: Check if GSAP is properly imported and registered
2. **Jerky transitions**: Ensure `box-sizing: border-box` is set on elements
3. **SSR errors**: Verify window checks are in place for server-side rendering

### Debug Mode:
Add `GSDevTools.create()` to enable GSAP's debugging tools in development.

## References

- [GSAP Flip Plugin Documentation](https://gsap.com/docs/v3/Plugins/Flip/)
- [Modern Animation Best Practices](https://web.dev/animations/)
- [GSAP Performance Tips](https://gsap.com/docs/v3/GSAP/gsap.set())
