# GSAP Animation Implementation

## Overview

This implementation adds modern GSAP (GreenSock Animation Platform) animations to the product cards and page transitions, following the latest 2025 best practices for smooth, performant animations.

## Features Implemented

### 1. Card Arrow Icon
- Added diagonal arrow (↗) in the top-right corner of each shift-card
- Smooth hover animations with opacity and scale transitions
- Positioned absolutely to not affect card layout

### 2. Cinematic Zoom Transitions
- **Card Zoom Effect**: Product cards scale up significantly (3.5x) while maintaining position
- **Background Page Transition**: New product page emerges from behind the zoomed card
- **Layered Animation Sequence**:
  - Card scales up (zoom in effect) - duration ~0.8s
  - Product page starts appearing from behind at ~0.4s into the card animation
  - Page scales from small (0.1x) to full size - duration ~0.6s
  - Card fades out as page reaches full size
- **Visual Continuity**: Seamless transition where users feel they've "zoomed into" the card

### 3. Preloading Strategy
- **Page Preloading**: Destination product pages are preloaded before animation starts
- **Hover Preloading**: Pages preload when users hover over cards (300ms delay)
- **Cache Management**: Intelligent caching system prevents duplicate preloads
- **Fallback Handling**: Graceful degradation if preloading fails

### 4. GSAP Flip Animations
- Implemented GSAP Flip plugin for seamless card-to-page transitions
- Modern animation technique that captures element states and smoothly transitions between them
- Handles complex DOM changes without jarring jumps

### 5. Interactive Hover Buttons (Magic UI Style)
- **Black Theme**: Consistent black styling as requested
- **Shimmer Effects**: Animated shimmer overlay on hover
- **Border Glow**: Subtle glow effect during hover
- **Arrow Animation**: Right arrow that translates on hover
- **Ripple Effect**: Click feedback with ripple animation
- **Applied to**: "View All Products" buttons throughout the application

### 6. Button Click Animations
- Enhanced button interactions with GSAP timeline animations
- Scale and bounce effects on click for better user feedback
- Smooth transitions that complement the overall design

### 7. Page Transition System
- Created reusable GSAPPageTransition component
- Entrance animations for product pages
- Smooth fade and scale effects
- Cinematic transition detection via session storage

## Files Modified/Created

### New Files:
1. `hooks/useGSAPTransition.ts` - Custom hook for GSAP animations with cinematic transitions
2. `components/ui/gsap-page-transition.tsx` - Page transition component with cinematic support
3. `components/ui/interactive-hover-button.tsx` - Magic UI style interactive button
4. `components/ui/cinematic-transition-overlay.tsx` - Overlay component for cinematic effects
5. `components/ui/page-preloader.tsx` - Page preloading utilities and components
6. `docs/GSAP_IMPLEMENTATION.md` - This documentation

### Modified Files:
1. `components/ui/shift-card.tsx` - Added arrow icon
2. `components/ui/shift-card-demo.tsx` - Integrated cinematic GSAP animations and preloading
3. `app/products/[slug]/page.tsx` - Added page transitions with cinematic support
4. `components/layout/sections/products.tsx` - Updated "View All Products" button
5. `app/products/page.tsx` - Updated category "View all" buttons
6. `package.json` - Added GSAP dependency

## How It Works

### Cinematic Zoom Transition Flow:

1. **Preloading**: Page is preloaded on component mount and hover (300ms delay)
2. **State Capture**: When user clicks "View Product" or "Buy Now", GSAP captures the current card state
3. **Button Animation**: Button scales down then bounces back with smooth easing
4. **Cinematic Container Setup**: Creates overlay with background darkening and page preview container
5. **Card Clone Creation**: Clones the card element for independent animation
6. **Phase 1 - Card Zoom** (0.8s): Card scales up 3.5x and moves to center of viewport
7. **Phase 2 - Page Emergence** (starts at 0.4s): Page preview appears behind card, scaling from 0.1x to 0.3x
8. **Phase 3 - Page Full Scale** (0.6s): Page scales from 0.3x to full size (1x)
9. **Phase 4 - Card Fade** (0.4s): Card fades out while scaling to 4x
10. **Phase 5 - Navigation**: Page navigation occurs with session storage flag set
11. **Page Entrance**: New page detects cinematic flag and animates from 0.3x scale to full size

### Technical Implementation:

```typescript
// Example usage in shift-card-demo.tsx
const handleViewProduct = () => {
  if (cardRef.current && viewButtonRef.current) {
    // Animate button click
    createButtonClickAnimation(viewButtonRef.current)
    
    // Create card to page transition
    createCardToPageTransition(
      cardRef.current,
      () => onViewProduct(),
      {
        duration: 1.0,
        ease: "power2.inOut"
      }
    )
  }
}
```

## Animation Specifications

### Timing:
- Button click animation: 0.6s with back.out(1.7) easing
- Card transition: 1.0s with power2.inOut easing
- Page entrance: 0.8s with power2.out easing

### Effects:
- Scale transformations for depth
- Opacity changes for smooth fades
- Overlay effects for visual continuity
- Responsive to user interactions

## Browser Compatibility

- Modern browsers supporting ES6+
- GSAP handles cross-browser compatibility
- Graceful fallbacks for unsupported features
- SSR-safe implementation with proper checks

## Performance Considerations

- GSAP uses hardware acceleration when possible
- Animations are optimized for 60fps
- Minimal DOM manipulation during transitions
- Proper cleanup to prevent memory leaks

## Usage Examples

### Basic Card Animation:
```tsx
<ShiftCardDemo
  product={product}
  onViewProduct={() => {
    // GSAP animation automatically triggers
    router.push(`/products/${slug}`)
  }}
/>
```

### Custom Page Transition:
```tsx
<GSAPPageTransition>
  <YourPageContent />
</GSAPPageTransition>
```

## Future Enhancements

1. **Scroll-triggered animations** using GSAP ScrollTrigger
2. **Stagger animations** for multiple cards
3. **Morphing transitions** between different layouts
4. **3D transforms** for more dynamic effects
5. **Physics-based animations** for natural movement

## Troubleshooting

### Common Issues:

1. **Animations not working**: Check if GSAP is properly imported and registered
2. **Jerky transitions**: Ensure `box-sizing: border-box` is set on elements
3. **SSR errors**: Verify window checks are in place for server-side rendering

### Debug Mode:
Add `GSDevTools.create()` to enable GSAP's debugging tools in development.

## References

- [GSAP Flip Plugin Documentation](https://gsap.com/docs/v3/Plugins/Flip/)
- [Modern Animation Best Practices](https://web.dev/animations/)
- [GSAP Performance Tips](https://gsap.com/docs/v3/GSAP/gsap.set())
