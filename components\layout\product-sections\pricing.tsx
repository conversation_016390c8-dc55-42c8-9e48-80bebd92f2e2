import { useState, useEffect, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { SubscriptionInfoDialog } from "@/components/trial/subscription-info-dialog";
import { extractProductCode, arePlansFromSameProduct, areExactSamePlan } from "@/src/utils/plan-utils";
import { SubscriptionButton } from "@/components/subscription/subscription-button";
import { Check, X, Percent, Star, Badge, Info, ArrowRight, ArrowUp, Calendar, ArrowDown, AlertTriangle, ChevronDown } from "lucide-react";
import SectionHeader from "../section-header";
import SectionContainer from "../section-container";
import { PeriodToggle } from "@/components/ui/period-toggle";
import { getPricingContentBySlug } from "@/app/api/pricingSection";
import { useCart } from "@/context/cart-context";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface PricingSectionProps {
  slug: string;
  sectionComponent?: string;
  sectionId?: number;
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  planCode?: string;
  quantity: number;
  image: string;
  slug?: string;
  productName?: string;
  productLogo?: string;
  planDuration?: "trial" | "monthly" | "yearly";
}

interface Feature {
  feature: string;
  isIncluded: boolean;
  id?: string;
}

interface Plan {
  name: string;
  description?: string;
  monthlyPricing?: number;
  yearlyPricing?: number;
  plan_code_monthly?: string;
  plan_code_yearly?: string;
  plan_code?: string;
  trialDurationInDays?: number;
  features?: Feature[];
  button?: {
    text: string;
    id: string;
  };
}

interface SubscriptionPlan {
  id: string;
  name: string;
  monthlyPricing: number;
  yearlyPricing: number;
  plan_code_monthly: string;
  plan_code_yearly: string;
  description: string;
  tag: string | null;
  fetaures: Feature[];
  button: {
    text: string;
    id: string;
  };
}

interface TrialPlan {
  id: string;
  name: string;
  trialDurationInDays: number;
  plan_code: string;
  description: string | null;
  tag: string | null;
  features: Feature[];
  button: {
    text: string;
    id: string;
  };
}

interface EnterprisePlan {
  id: string;
  name: string;
  description: string;
  tag: string | null;
  features: Feature[];
  button: {
    text: string;
    id: string;
  };
}

interface PricingData {
  id: string;
  heading: string;
  title: string;
  subscriptionPlan: SubscriptionPlan[];
  trialPlan: TrialPlan | null;
  enterprisePlan: EnterprisePlan | null;
  productInfo?: {
    productName: string;
    productLogo: string | null;
  };
  discountTag: string | null;
}

export const PricingSection = ({ slug }: PricingSectionProps) => {
  const [billingPeriod, setBillingPeriod] = useState<"monthly" | "yearly">("monthly");
  const [pricingData, setPricingData] = useState<PricingData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { items, addItem, replaceItem, setOnItemAdded } = useCart();
  const { status, data: session } = useSession();
  const [isAlertOpen, setIsAlertOpen] = useState(false);
  const [isPlanChangeProcessing, setIsPlanChangeProcessing] = useState(false);
  const [isSubscriptionInfoOpen, setIsSubscriptionInfoOpen] = useState(false);
  const [existingSubscription, setExistingSubscription] = useState<{
    isPaidPlan: boolean;
    isTrialPlan: boolean;
    planName?: string;
    planExpiration?: string;
    isActive: boolean;
    isExpired: boolean;
    isPending?: boolean;
    daysRemaining?: number;
  } | null>(null);
  const [planToChange, setPlanToChange] = useState<{
    existingPlan: CartItem;
    newPlan: { name: string; price: number };
    newItem: CartItem;
    changeType: "billing" | "upgrade" | "downgrade" | "different";
  } | null>(null);

  // Track the lowest-priced plan in cart to show upgrade benefits
  const [lowestPricePlanInCart, setLowestPricePlanInCart] = useState<CartItem | null>(null);



  // Calculate maximum savings percentage across all plans
  const [maxSavingsPercentage, setMaxSavingsPercentage] = useState<number>(0);

  // Reference to the period toggle component for programmatic toggling
  const periodToggleRef = useRef<HTMLDivElement>(null);

  // Enhanced state to store feature comparison data for the dialog
  const [planComparison, setPlanComparison] = useState<{
    addedFeatures: string[];
    removedFeatures: string[];
    priceDifference: number;
    percentageChange: number;
  } | null>(null);

  // Function to switch to yearly billing and update UI
  const switchToYearlyBilling = () => {
    setBillingPeriod("yearly");

    // Programmatically update the toggle state if needed
    const toggle = periodToggleRef.current?.querySelector('button[role="switch"]');
    if (toggle && toggle.getAttribute('aria-checked') === 'false') {
      (toggle as HTMLButtonElement).click();
    }
  };

  // Set up the cart item added callback to auto-switch to yearly billing
  useEffect(() => {
    // Define the callback function
    const handleItemAdded = (item: CartItem) => {
      // Safety check - ensure item is defined and has required properties
      if (!item) {
        console.error('AUTO-SWITCH - Received undefined item in callback');
        return;
      }

      // Log the item for debugging
      console.log(`🔄 AUTO-SWITCH - Item added to cart:`, {
        id: item.id,
        name: item.name,
        planDuration: item.planDuration || 'undefined',
        slug: item.slug || 'undefined'
      });

      // Always auto-switch if a monthly plan is added, regardless of current view
      if (item.planDuration === "monthly" && item.slug && item.slug === slug) {
        console.log(`🔄 AUTO-SWITCH - Monthly plan added to cart, switching to yearly view to show savings`);

        // Add a small delay to ensure the cart update completes first
        setTimeout(() => {
          // Only switch if we're not already on yearly
          if (billingPeriod !== "yearly") {
            switchToYearlyBilling();

            // Show a toast explaining the switch
            toast.info(
              "We've switched to yearly billing to show you potential savings. You can switch back to monthly anytime.",
              {
                duration: 5000,
                action: {
                  label: "Switch Back",
                  onClick: () => {
                    setBillingPeriod("monthly");
                    // Update toggle UI
                    const toggle = periodToggleRef.current?.querySelector('button[role="switch"]');
                    if (toggle && toggle.getAttribute('aria-checked') === 'true') {
                      (toggle as HTMLButtonElement).click();
                    }
                  }
                }
              }
            );
          }
        }, 500);
      }
    };

    // Register the callback
    setOnItemAdded(handleItemAdded);

    // Clean up the callback when component unmounts
    return () => setOnItemAdded(undefined);
  }, [billingPeriod, slug, setOnItemAdded]);

  useEffect(() => {
    const fetchPricingData = async () => {
      if (!slug) {
        setError("No product slug provided");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        console.log("Fetching pricing data for slug:", slug);
        const data = await getPricingContentBySlug(slug);
        console.log("Raw pricing data:", data);

        // Log all keys to identify what's available
        if (data) {
          console.log("Available keys in pricing data:", Object.keys(data));
        }

        setPricingData(data);
        if (!data) {
          console.warn("No pricing data returned for slug:", slug);
          setError("No pricing information available for this product.");
        }
      } catch (error) {
        console.error("Error in pricing component:", error);
        setError("Failed to load pricing data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchPricingData();
  }, [slug]);

  useEffect(() => {
    // Update upgrade opportunities whenever cart items or billing period changes
    if (pricingData) {
      const upgrades: Record<string, boolean> = {};
      let lowestPricePlan: CartItem | null = null;

      // Check if any monthly plans in cart can be upgraded to yearly
      // Also track the lowest-priced plan for this product in the cart
      items.forEach(item => {
        // Safety check - ensure item has required properties
        if (!item || !item.id || typeof item.price !== 'number') {
          console.warn('PRICING - Skipping invalid cart item:', item);
          return;
        }

        // Only consider items for this product
        if (item.slug === slug) {
          // Flag monthly plans as having an upgrade opportunity
          if (item.planDuration === "monthly") {
            upgrades[item.id] = true;
          }

          // Track lowest price plan
          if (!lowestPricePlan || item.price < lowestPricePlan.price) {
            lowestPricePlan = item;
          }
        }
      });


      setLowestPricePlanInCart(lowestPricePlan);

      // Log for debugging
      if (lowestPricePlan && 'name' in lowestPricePlan && 'price' in lowestPricePlan) {
        const plan = lowestPricePlan as CartItem;
        console.log(`🔄 PRICING - Lowest price plan in cart: ${plan.name} (${plan.price})`);
      }
    }
  }, [items, billingPeriod, pricingData, slug]);

  // Add useEffect to calculate max savings percentage when pricing data loads
  useEffect(() => {
    if (pricingData && Array.isArray(pricingData.subscriptionPlan)) {
      // Calculate savings for each plan and find the maximum
      const savingsPercentages = pricingData.subscriptionPlan
        .filter(plan => plan.monthlyPricing && plan.yearlyPricing)
        .map(plan => {
          const yearlyTotal = plan.monthlyPricing * 12;
          const savings = yearlyTotal - plan.yearlyPricing;
          return Math.round((savings / yearlyTotal) * 100);
        });

      const maxPercentage = Math.max(0, ...savingsPercentages);
      setMaxSavingsPercentage(maxPercentage);
    }
  }, [pricingData]);

  // Helper function to extract features from a plan by name
  const extractFeaturesFromPlan = useCallback((planName: string): Feature[] => {
    // For subscription plans
    const subscriptionPlan = pricingData?.subscriptionPlan?.find(plan => plan.name === planName);
    if (subscriptionPlan) {
      return getPlanFeatures(subscriptionPlan).map(feature => ({
        feature: feature.feature,
        isIncluded: feature.isIncluded
      }));
    }

    // For trial plan
    if (pricingData?.trialPlan && pricingData.trialPlan.name === planName) {
      return (pricingData.trialPlan.features || []).map(feature => ({
        feature: feature.feature,
        isIncluded: feature.isIncluded
      }));
    }

    // For enterprise plan
    if (pricingData?.enterprisePlan && pricingData.enterprisePlan.name === planName) {
      return (pricingData.enterprisePlan.features || []).map(feature => ({
        feature: feature.feature,
        isIncluded: feature.isIncluded
      }));
    }

    // Fallback: return empty array if plan not found
    return [];
  }, [pricingData]);

  // Enhanced plan change type determination
  const determineChangeType = useCallback((existingPlan: CartItem, newItem: CartItem, newPlanFull: Plan | { name: string; price: number }) => {
    // Compare features between plans to highlight differences
    const existingPlanFeatures = extractFeaturesFromPlan(existingPlan.id);
    const newPlanFeatures = extractFeaturesFromPlan(newPlanFull.name || newItem.id);

    const addedFeatures: string[] = [];
    const removedFeatures: string[] = [];

    // Find features that are in the new plan but not in the existing plan
    newPlanFeatures.forEach(newFeature => {
      if (!existingPlanFeatures.some(existingFeature =>
        existingFeature.feature === newFeature.feature && existingFeature.isIncluded === newFeature.isIncluded)) {
        if (newFeature.isIncluded) {
          addedFeatures.push(newFeature.feature);
        }
      }
    });

    // Find features that are in the existing plan but not in the new plan
    existingPlanFeatures.forEach(existingFeature => {
      if (!newPlanFeatures.some(newFeature =>
        newFeature.feature === existingFeature.feature && newFeature.isIncluded === existingFeature.isIncluded)) {
        if (existingFeature.isIncluded) {
          removedFeatures.push(existingFeature.feature);
        }
      }
    });

    // Calculate price difference and percentage
    let priceDifference = newItem.price - existingPlan.price;
    let basePrice = existingPlan.price;

    // Normalize prices for comparing monthly to yearly or vice versa
    if (existingPlan.planDuration === "monthly" && newItem.planDuration === "yearly") {
      // Compare annual cost for fair comparison
      const existingAnnualCost = existingPlan.price * 12;
      priceDifference = newItem.price - existingAnnualCost;
      basePrice = existingAnnualCost;
    } else if (existingPlan.planDuration === "yearly" && newItem.planDuration === "monthly") {
      // Compare annual cost for fair comparison
      const newAnnualCost = newItem.price * 12;
      priceDifference = newAnnualCost - existingPlan.price;
      basePrice = existingPlan.price;
    }

    const percentageChange = basePrice !== 0
      ? Math.round((priceDifference / basePrice) * 100)
      : 100;

    // Store comparison data for dialog
    setPlanComparison({
      addedFeatures,
      removedFeatures,
      priceDifference,
      percentageChange
    });

    // Determine the type of change
    if (existingPlan.id === newItem.id) {
      // If switching from monthly to yearly, it's an upgrade
      if (existingPlan.planDuration === "monthly" && newItem.planDuration === "yearly") {
        return "upgrade";
      }
      return "billing";
    }

    // For different plans, determine if upgrading or downgrading based on price
    if (priceDifference > 0) {
      return "upgrade";
    } else if (priceDifference < 0) {
      return "downgrade";
    }

    return "different";
  }, [extractFeaturesFromPlan, setPlanComparison]);

  // Check for pending subscriptions when user logs in
  useEffect(() => {
    const checkPendingSubscription = async () => {
      if (status !== "authenticated" || !session?.user) return;

      const user = session.user as { id?: string; name?: string; email?: string; image?: string };
      if (!user.id) return;

      try {
        const pendingPlan = localStorage.getItem("pending_subscription_plan");
        const pendingProduct = localStorage.getItem("pending_subscription_product");

        if (!pendingPlan || !pendingProduct) return;

        // Use the new endpoint to check for active subscriptions
        const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(user.id)}&plan_code=${pendingPlan}&status[]=active`;

        console.log(`🔍 PRICING - Checking active subscriptions for user ${user.id} and plan ${pendingPlan}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          const subscriptions = data.subscriptions || [];

          if (subscriptions.length > 0) {
            console.log(`⚠️ PRICING - User already has an active subscription for plan ${pendingPlan}`);

            // Update the subscription state
            const subscription = subscriptions[0];
            setExistingSubscription({
              isPaidPlan: !subscription.plan_code.includes('trial'),
              isTrialPlan: subscription.plan_code.includes('trial'),
              planName: subscription.name || subscription.plan_code,
              planExpiration: subscription.ending_at,
              isActive: subscription.status === 'active',
              isExpired: false,
              isPending: subscription.status === 'pending',
              daysRemaining: subscription.ending_at ? Math.ceil((new Date(subscription.ending_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : undefined
            });

            // Show the subscription info dialog
            setIsSubscriptionInfoOpen(true);

            // Also show a toast for immediate feedback
            toast.info("You already have an active subscription for this plan.", {
              duration: 3000
            });

            // Clear the pending subscription info
            localStorage.removeItem("pending_subscription_plan");
            localStorage.removeItem("pending_subscription_product");
            localStorage.removeItem("pending_subscription_slug");
            localStorage.removeItem("pending_subscription_name");
            localStorage.removeItem("pending_subscription_price");
            localStorage.removeItem("pending_subscription_duration");

            return;
          }

          // No active subscription found, check if the user already has this plan in their cart
          const exactPlanInCart = items.find(item => {
            return item.planCode && areExactSamePlan(item.planCode, pendingPlan);
          });

          if (exactPlanInCart) {
            console.log(`⚠️ PRICING - User already has the exact same plan in cart: ${exactPlanInCart.planCode}`);

            toast.info("This plan is already in your cart", {
              duration: 3000,
              action: {
                label: "View Cart",
                onClick: () => window.location.href = "/cart"
              }
            });

            // Clear the pending subscription info
            localStorage.removeItem("pending_subscription_plan");
            localStorage.removeItem("pending_subscription_product");
            localStorage.removeItem("pending_subscription_slug");
            localStorage.removeItem("pending_subscription_name");
            localStorage.removeItem("pending_subscription_price");
            localStorage.removeItem("pending_subscription_duration");

            return;
          }

          // Check if a different plan from the same product is in cart
          const differentPlanSameProduct = items.find(item => {
            return item.planCode &&
              arePlansFromSameProduct(item.planCode, pendingPlan) &&
              !areExactSamePlan(item.planCode, pendingPlan);
          });

          if (differentPlanSameProduct) {
            console.log(`⚠️ PRICING - User has a different plan from same product in cart: ${differentPlanSameProduct.planCode}`);

            // Create the new item object for the plan change dialog
            const pendingName = localStorage.getItem("pending_subscription_name") || "";
            const pendingPrice = parseFloat(localStorage.getItem("pending_subscription_price") || "0");
            const pendingDuration = localStorage.getItem("pending_subscription_duration") || "monthly";

            const newItem = {
              id: pendingName,
              name: pendingName,
              price: pendingPrice,
              planCode: pendingPlan,
              quantity: 1,
              image: pricingData?.productInfo?.productLogo || "",
              slug,
              productName: pricingData?.productInfo?.productName || "",
              productLogo: pricingData?.productInfo?.productLogo || undefined,
              planDuration: pendingDuration as "trial" | "monthly" | "yearly"
            };

            // Determine if this is an upgrade, downgrade, or just a different plan
            const changeType = determineChangeType(differentPlanSameProduct, newItem, {
              name: pendingName,
              price: pendingPrice
            });

            // Set up the plan change dialog
            setPlanToChange({
              existingPlan: differentPlanSameProduct,
              newPlan: {
                name: pendingName,
                price: pendingPrice
              },
              newItem,
              changeType
            });
            setIsAlertOpen(true);

            // Clear the pending subscription info
            localStorage.removeItem("pending_subscription_plan");
            localStorage.removeItem("pending_subscription_product");
            localStorage.removeItem("pending_subscription_slug");
            localStorage.removeItem("pending_subscription_name");
            localStorage.removeItem("pending_subscription_price");
            localStorage.removeItem("pending_subscription_duration");

            return;
          }

          // No existing subscription or cart item found, add the pending plan to cart
          const pendingName = localStorage.getItem("pending_subscription_name") || "";
          const pendingPrice = parseFloat(localStorage.getItem("pending_subscription_price") || "0");
          const pendingDuration = localStorage.getItem("pending_subscription_duration") || "monthly";

          // Create cart item
          const newItem = {
            id: pendingName,
            name: pendingName,
            price: pendingPrice,
            planCode: pendingPlan,
            quantity: 1,
            image: pricingData?.productInfo?.productLogo || "",
            slug,
            productName: pricingData?.productInfo?.productName || "",
            productLogo: pricingData?.productInfo?.productLogo || undefined,
            planDuration: pendingDuration as "trial" | "monthly" | "yearly"
          };

          // Add to cart
          addItem(newItem);
          toast.success(`${pendingName} plan added to cart`);

          // Clear the pending subscription info
          localStorage.removeItem("pending_subscription_plan");
          localStorage.removeItem("pending_subscription_product");
          localStorage.removeItem("pending_subscription_slug");
          localStorage.removeItem("pending_subscription_name");
          localStorage.removeItem("pending_subscription_price");
          localStorage.removeItem("pending_subscription_duration");
        }
      } catch (error) {
        console.error("Error checking pending subscription:", error);
        toast.error("Failed to check subscription status. Please try again.");
      }
    };

    checkPendingSubscription();
  }, [status, session, slug, addItem, pricingData, items, determineChangeType]);

  const getPrice = (price: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
    }).format(price);
  };

  const handleAddPlan = async (plan: Plan) => {
    if (!plan) return;

    const planName = plan.name || "";
    const planPrice = 'monthlyPricing' in plan
      ? (billingPeriod === "monthly" ? plan.monthlyPricing : plan.yearlyPricing)
      : ('trialDurationInDays' in plan ? 0 : undefined);

    // If no valid price found, handle as enterprise plan
    if (typeof planPrice !== 'number') {
      window.location.href = "/contact";
      return;
    }

    // Extract the appropriate plan code based on billing period or trial
    const planCode = 'monthlyPricing' in plan
      ? (billingPeriod === "monthly" ? plan.plan_code_monthly : plan.plan_code_yearly)
      : ('trialDurationInDays' in plan ? plan.plan_code : null);

    // Verify that we have a planCode for subscription
    if (!planCode) {
      console.error("Missing plan_code for plan:", plan);
      toast.error("Cannot add this plan to cart - missing subscription code. Please contact support.");
      return;
    }

    // Log the selected plan code for debugging
    console.log(`Selected plan code for ${billingPeriod} billing:`, planCode);

    // Extract product code from the plan code for later use
    const productCode = extractProductCode(planCode);

    // If user is not logged in, store the plan info in localStorage and redirect to login
    if (status !== "authenticated" || !session?.user) {
      try {
        localStorage.setItem("pending_subscription_plan", planCode);
        localStorage.setItem("pending_subscription_product", productCode || "");
        localStorage.setItem("pending_subscription_slug", slug);
        localStorage.setItem("pending_subscription_name", planName);
        localStorage.setItem("pending_subscription_price", planPrice.toString());
        localStorage.setItem("pending_subscription_duration", 'trialDurationInDays' in plan ? "trial" : billingPeriod);

        console.log(`🔍 PRICING - User not logged in, storing plan info for post-login check: ${planCode}`);

        // Redirect to login page
        window.location.href = "/auth/signin";
        return;
      } catch (error) {
        console.error("Error storing pending subscription info:", error);
        toast.error("Failed to save plan information. Please try again.");
        return;
      }
    }

    const user = session.user as { id?: string; name?: string; email?: string; image?: string };
    if (!user.id) {
      toast.error("User ID not available. Please try logging in again.");
      return;
    }

    // Check for active subscriptions using the new endpoint
    try {
      const url = `${process.env.NEXT_PUBLIC_LAGO_API_URL}/subscriptions?external_customer_id=${encodeURIComponent(user.id)}&plan_code=${planCode}&status[]=active`;

      console.log(`🔍 PRICING - Checking active subscriptions for user ${user.id} and plan ${planCode}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_LAGO_API_KEY}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const subscriptions = data.subscriptions || [];

        if (subscriptions.length > 0) {
          console.log(`⚠️ PRICING - User already has an active subscription for plan ${planCode}`);

          // Update the subscription state
          const subscription = subscriptions[0];
          setExistingSubscription({
            isPaidPlan: !subscription.plan_code.includes('trial'),
            isTrialPlan: subscription.plan_code.includes('trial'),
            planName: subscription.name || subscription.plan_code,
            planExpiration: subscription.ending_at,
            isActive: subscription.status === 'active',
            isExpired: false,
            isPending: subscription.status === 'pending',
            daysRemaining: subscription.ending_at ? Math.ceil((new Date(subscription.ending_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : undefined
          });

          // Show the subscription info dialog
          setIsSubscriptionInfoOpen(true);

          // Also show a toast for immediate feedback
          toast.info("You already have an active subscription for this plan.", {
            duration: 3000
          });

          return;
        }
      }
    } catch (error) {
      console.error("Error checking existing subscriptions:", error);
      toast.error("Failed to check subscription status. Please try again.");
      return;
    }

    // First check if the exact same plan is already in cart
    const exactPlanCodeInCart = items.find(item => {
      return item.planCode && areExactSamePlan(item.planCode, planCode);
    });

    if (exactPlanCodeInCart) {
      console.log(`⚠️ PRICING - User already has the exact same plan in cart: ${exactPlanCodeInCart.planCode}`);
      toast.info("This plan is already in your cart");
      return;
    }

    // Get product information from the enhanced pricing section data
    const productName = pricingData?.productInfo?.productName || "";
    const productLogoUrl = pricingData?.productInfo?.productLogo || undefined;

    // Check if a different plan from the same product is in cart
    const differentPlanSameProduct = items.find(item => {
      return item.planCode &&
        arePlansFromSameProduct(item.planCode, planCode) &&
        !areExactSamePlan(item.planCode, planCode);
    });

    if (differentPlanSameProduct) {
      console.log(`⚠️ PRICING - User has a different plan from same product in cart: ${differentPlanSameProduct.planCode}`);

      // Create the new item object for the plan change dialog
      const newItem = {
        id: planName,
        name: planName,
        price: planPrice,
        planCode,
        quantity: 1,
        image: productLogoUrl || "",
        slug,
        productName: productName,
        productLogo: productLogoUrl,
        planDuration: ('trialDurationInDays' in plan ? "trial" : billingPeriod) as "trial" | "monthly" | "yearly",
        ...(plan.trialDurationInDays ? { trialDays: plan.trialDurationInDays } : {})
      };

      // Determine if this is an upgrade, downgrade, or just a different plan
      const changeType = determineChangeType(differentPlanSameProduct, newItem, plan);

      // Only proceed if not already processing a plan change
      if (isPlanChangeProcessing) {
        console.log(`⚠️ PRICING - Plan change already in progress, ignoring request`);
        return;
      }

      // Set up the plan change dialog
      setPlanToChange({
        existingPlan: differentPlanSameProduct,
        newPlan: { name: planName, price: planPrice },
        newItem,
        changeType
      });
      setIsAlertOpen(true);
      return;
    }

    const newItem = {
      id: planName,
      name: planName,
      price: planPrice,
      planCode,
      quantity: 1,
      image: productLogoUrl || "",
      slug,
      productName: productName,
      productLogo: productLogoUrl,
      planDuration: ('trialDurationInDays' in plan ? "trial" : billingPeriod) as "trial" | "monthly" | "yearly",
      ...(plan.trialDurationInDays ? { trialDays: plan.trialDurationInDays } : {})
    };

    // Check for the exact same plan in cart by slug, name and duration
    const exactMatchInCart = items.find(
      item => item.slug === slug && item.id === planName && item.planDuration === newItem.planDuration
    );

    if (exactMatchInCart) {
      toast.info("This plan is already in your cart");
      return;
    }

    // Check for the same plan with a different billing period
    const samePlanDifferentBilling = items.find(
      item => item.slug === slug && item.id === planName && item.planDuration !== newItem.planDuration
    );

    if (samePlanDifferentBilling) {
      // Calculate which is better (monthly vs yearly)
      const isUpgrade = samePlanDifferentBilling.planDuration === "monthly" && billingPeriod === "yearly";
      const changeType = isUpgrade ? "upgrade" : "billing";

      // Only proceed if not already processing a plan change
      if (isPlanChangeProcessing) {
        console.log(`⚠️ PRICING - Plan change already in progress, ignoring request`);
        return;
      }

      setPlanToChange({
        existingPlan: samePlanDifferentBilling,
        newPlan: { name: planName, price: planPrice },
        newItem,
        changeType
      });
      setIsAlertOpen(true);
      return;
    }

    // Check if user already has another plan from the same product in the cart
    const otherPlanFromSameProduct = items.find(
      item => item.slug === slug && item.id !== planName
    );

    if (otherPlanFromSameProduct) {
      // Determine if this is an upgrade, downgrade or just different plan
      const changeType = determineChangeType(otherPlanFromSameProduct, newItem, plan);

      // Only proceed if not already processing a plan change
      if (isPlanChangeProcessing) {
        console.log(`⚠️ PRICING - Plan change already in progress, ignoring request`);
        return;
      }

      setPlanToChange({
        existingPlan: otherPlanFromSameProduct,
        newPlan: { name: planName, price: planPrice },
        changeType,
        newItem
      });
      setIsAlertOpen(true);
      return;
    }

    // If no conflicts, add the plan to cart
    addItem(newItem);
    toast.success(`${newItem.planDuration === "trial" ? "Trial" : plan.name} plan added to cart`);
  };



  // Function to organize features showing included ones first
  const organizeFeatures = (features: Feature[]) => {
    if (!Array.isArray(features)) return [];

    // Sort features so included ones come first
    return [...features].sort((a, b) => {
      if (a.isIncluded === b.isIncluded) return 0;
      return a.isIncluded ? -1 : 1;
    });
  };

  // Calculate yearly savings with more details
  const calculateYearlySavings = (monthly: number, yearly: number) => {
    if (!monthly || !yearly) return {
      savings: 0,
      percentage: 0,
      yearlyMonthlyTotal: 0,
      monthlyEquivalent: 0,
      monthlySavings: 0
    };

    const yearlyMonthlyTotal = monthly * 12;
    const savings = yearlyMonthlyTotal - yearly;
    const percentage = Math.round((savings / yearlyMonthlyTotal) * 100);
    const monthlyEquivalent = yearly / 12;
    const monthlySavings = monthly - monthlyEquivalent;

    return {
      savings,
      percentage,
      yearlyMonthlyTotal,
      monthlyEquivalent,
      monthlySavings
    };
  };

  // Helper function to get pricing plan features based on the plan type
  const getPlanFeatures = (plan: Plan | SubscriptionPlan | TrialPlan | EnterprisePlan): Feature[] => {
    if (!plan) return [];

    if ('fetaures' in plan && Array.isArray(plan.fetaures)) {
      return plan.fetaures;
    } else if ('features' in plan && Array.isArray(plan.features)) {
      return plan.features;
    }

    return [];
  };

  // Helper function to get the appropriate styling for a tag
  const getTagStyling = (tag: string | null | undefined) => {
    if (!tag) return { bgColor: "", textColor: "", label: "" };

    const tagLower = tag.toLowerCase();

    if (tagLower.includes("popular") || tagLower.includes("recommended")) {
      return {
        bgColor: "bg-primary",
        textColor: "text-primary-foreground",
        label: tag
      };
    } else if (tagLower.includes("best") || tagLower.includes("value")) {
      return {
        bgColor: "bg-orange-500",
        textColor: "text-white",
        label: tag
      };
    } else if (tagLower.includes("trial") || tagLower.includes("free")) {
      return {
        bgColor: "bg-green-600",
        textColor: "text-white",
        label: tag
      };
    } else if (tagLower.includes("pro") || tagLower.includes("enterprise")) {
      return {
        bgColor: "bg-blue-600",
        textColor: "text-white",
        label: tag
      };
    } else {
      return {
        bgColor: "bg-primary",
        textColor: "text-primary-foreground",
        label: tag
      };
    }
  };

  // Show loading state or error
  if (isLoading) {
    return (
      <SectionContainer id="pricing">
        <div className="flex justify-center items-center min-h-[300px]">
          <p className="text-muted-foreground">Loading pricing information...</p>
        </div>
      </SectionContainer>
    );
  }

  if (error) {
    console.error("Pricing error:", error);
    return (
      <SectionContainer id="pricing">
        <div className="flex justify-center items-center min-h-[300px]">
          <p className="text-red-500">{error}</p>
        </div>
      </SectionContainer>
    );
  }

  // If no pricing data, don't render the section
  if (!pricingData) {
    console.warn("No pricing data available");
    return null;
  }

  console.log("Rendering pricing section with data:", {
    heading: pricingData.heading,
    title: pricingData.title,
    subscriptionPlan: pricingData.subscriptionPlan,
    isSPArray: Array.isArray(pricingData.subscriptionPlan),
    spLength: Array.isArray(pricingData.subscriptionPlan) ? pricingData.subscriptionPlan.length : 'N/A',
    enterprisePlan: !!pricingData.enterprisePlan,
    trialPlan: !!pricingData.trialPlan
  });

  // Try several potential naming conventions for subscription plans
  const possibleSubscriptionPlans = [
    pricingData.subscriptionPlan,
    (pricingData as unknown as Record<string, unknown>).subscriptionPlans,
    (pricingData as unknown as Record<string, unknown>).subscription_plan,
    (pricingData as unknown as Record<string, unknown>).subscription_plans
  ];

  let subscriptionPlans: Plan[] = [];

  // Find the first valid array among possible naming conventions
  for (const planArray of possibleSubscriptionPlans) {
    if (Array.isArray(planArray) && planArray.length > 0) {
      subscriptionPlans = planArray;
      console.log("Found subscription plans using key:", possibleSubscriptionPlans.indexOf(planArray));
      break;
    }
  }

  // If no valid array was found but we have a single subscription plan, wrap it in an array
  if (subscriptionPlans.length === 0 && typeof pricingData.subscriptionPlan === 'object' && pricingData.subscriptionPlan !== null) {
    if (!Array.isArray(pricingData.subscriptionPlan)) {
      subscriptionPlans = [pricingData.subscriptionPlan];
      console.log("Single subscription plan found and wrapped in array");
    }
  }

  const hasSubscriptionPlans = subscriptionPlans.length > 0;
  const hasTrialPlan = false; // Trial plans temporarily disabled
  const hasEnterprisePlan = !!pricingData.enterprisePlan;

  // If no plans at all, don't render the section
  if (!hasSubscriptionPlans && !hasTrialPlan && !hasEnterprisePlan) {
    console.warn("No subscription plans, trial plan, or enterprise plan available");
    return null;
  }

  // Log what we're actually going to render
  console.log("Will render:", {
    subscriptionPlansCount: subscriptionPlans.length,
    hasTrialPlan,
    hasEnterprisePlan
  });

  return (
    <SectionContainer id="pricing" className="relative">
      <SectionHeader
        subTitle={pricingData.heading || "Pricing"}
        title={pricingData.title || "Choose your plan"}
        description="Choose the perfect plan for your needs with flexible options that grow with your business"
      />

      {/* Add yearly savings banner above the toggle */}
      {hasSubscriptionPlans && billingPeriod === "monthly" && maxSavingsPercentage > 0 && (
        <div className="max-w-3xl mx-auto mb-8 rounded-lg bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 p-4 border border-primary/20">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full p-2 bg-primary/20 shrink-0">
                <Percent className="h-5 w-5 text-primary" />
              </div>
              <div className="text-center sm:text-left">
                <h3 className="text-lg font-medium">Save up to {maxSavingsPercentage}% by switching to yearly billing</h3>
                <p className="text-sm text-muted-foreground">Get the best value with our annual plans</p>
              </div>
            </div>
            <Button
              variant="default"
              onClick={switchToYearlyBilling}
              className="shrink-0"
              size="sm"
            >
              See yearly plans
            </Button>
          </div>
        </div>
      )}

      {hasSubscriptionPlans && (
        <div className="flex items-center justify-center gap-4 mb-10" ref={periodToggleRef}>
          <span className={billingPeriod === "monthly" ? "text-primary" : "text-muted-foreground"}>
            Monthly
          </span>
          <PeriodToggle
            aria-label="Toggle billing period"
            checked={billingPeriod === "yearly"}
            onPeriodChange={(period) => {
              setBillingPeriod(period);
              // When switching to yearly, highlight upgrade opportunities
              if (period === "yearly") {
                const monthlyPlans = items.filter(
                  item => item.slug === slug && item.planDuration === "monthly"
                );
                if (monthlyPlans.length > 0) {
                  toast.info(`Yearly plans save you money! Consider upgrading your ${monthlyPlans.length > 1 ? 'plans' : 'plan'}.`);
                }
              }
            }}
          />
          <span className={billingPeriod === "yearly" ? "text-primary" : "text-muted-foreground"}>
            Yearly
          </span>
          {pricingData.discountTag && (
            <div className="bg-primary/10 text-primary text-xs font-medium rounded-full px-2 py-0.5">
              {pricingData.discountTag}
            </div>
          )}
        </div>
      )}

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-4">
        {/* Trial plan temporarily disabled
        {hasTrialPlan && pricingData.trialPlan && (
          <Card className={cn(
            "relative overflow-hidden transition-all duration-300",
            pricingData.trialPlan.tag ? "border-primary shadow-lg" : ""
          )}>
            {pricingData.trialPlan.tag && (
              <div className={cn(
                "absolute -right-12 top-6 rotate-45 px-12 py-1 text-sm font-medium",
                getTagStyling(pricingData.trialPlan.tag).bgColor,
                getTagStyling(pricingData.trialPlan.tag).textColor
              )}>
                {getTagStyling(pricingData.trialPlan.tag).label}
              </div>
            )}
            <CardHeader className="pb-2">
              <CardTitle className="pb-2">{pricingData.trialPlan.name}</CardTitle>
              <CardDescription className="pb-4">
                {pricingData.trialPlan.description || `Try it free for ${pricingData.trialPlan.trialDurationInDays} days`}
              </CardDescription>
              <div className="flex items-center space-x-2 text-primary">
                <Clock className="h-5 w-5" />
                <span className="text-md font-medium">
                  {pricingData.trialPlan.trialDurationInDays} days free trial
                </span>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                {Array.isArray(pricingData.trialPlan.features) && pricingData.trialPlan.features.map((feature: any, idx: number) => (
                  <div key={feature.id || `trial-feature-${idx}`} className="flex items-start">
                    {feature.isIncluded ? (
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 shrink-0" />
                    ) : (
                      <X className="h-5 w-5 text-gray-300 mr-2 mt-0.5 shrink-0" />
                    )}
                    <span className={feature.isIncluded ? "" : "text-gray-400"}>
                      {feature.feature}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>

            <CardFooter>
              <Button
                variant="default"
                className="w-full"
                onClick={() => handleAddPlan(pricingData.trialPlan)}
                disabled={!!items.find(item => item.slug === slug && item.id === pricingData.trialPlan?.name)}
              >
                {items.find(item => item.slug === slug && item.id === pricingData.trialPlan?.name)
                  ? "Added to Cart"
                  : (pricingData.trialPlan.button?.text || "Start Free Trial")}
              </Button>
            </CardFooter>
          </Card>
        )}
        */}

        {subscriptionPlans.map((plan: Plan, idx: number) => {
          console.log(`Rendering subscription plan ${idx}:`, plan.name);

          // Ensure the plan has all required properties, use defaults if missing
          const planName = plan.name || `Plan ${idx + 1}`;
          const planDescription = plan.description || "No description provided";
          const monthlyPrice = typeof plan.monthlyPricing === 'number' ? plan.monthlyPricing : 0;
          const yearlyPrice = typeof plan.yearlyPricing === 'number' ? plan.yearlyPricing : 0;
          const planFeatures = organizeFeatures(getPlanFeatures(plan));

          const existingInCart = items.find(
            item => item.slug === slug && item.id === planName && item.planDuration === billingPeriod
          );

          // Check for same plan but with different billing period
          const samePlanDifferentBilling = items.find(
            item => item.slug === slug && item.id === planName && item.planDuration !== billingPeriod
          );

          const savings = calculateYearlySavings(monthlyPrice, yearlyPrice);
          const tagStyling = getTagStyling((plan as unknown as { tag?: string }).tag);
          const isHighlighted = !!(plan as unknown as { tag?: string }).tag;

          // Check if this is an opportunity to upgrade from monthly to yearly
          const canUpgradeFromMonthly = billingPeriod === "yearly" &&
            samePlanDifferentBilling &&
            samePlanDifferentBilling.planDuration === "monthly";

          // Check if this is an upgrade path from a lower-priced plan in cart
          // For yearly view, we need to compare equivalent monthly prices to be fair
          const isUpgradeFromLowerPlan = lowestPricePlanInCart &&
            typeof lowestPricePlanInCart.price === 'number' &&
            lowestPricePlanInCart.id !== planName &&
            (
              // If both are same billing period, direct comparison
              (lowestPricePlanInCart.planDuration === billingPeriod &&
               lowestPricePlanInCart.price < (billingPeriod === "monthly" ? monthlyPrice : yearlyPrice)) ||
              // If comparing monthly in cart to yearly view
              (lowestPricePlanInCart.planDuration === "monthly" && billingPeriod === "yearly" &&
               lowestPricePlanInCart.price * 12 > yearlyPrice && // Only show upgrade if yearly is actually cheaper
               !samePlanDifferentBilling) // Don't show upgrade path if it's the same plan with different billing
            );

          return (
            <Card
              key={(plan as unknown as { id?: string }).id || `plan-${idx}`}
              className={cn(
                "relative overflow-hidden transition-all duration-300",
                isHighlighted ? "border-primary shadow-lg lg:scale-[1.02] z-10" : "",
                canUpgradeFromMonthly ? "border-green-500 shadow-lg lg:scale-[1.02] z-10" : "",
                isUpgradeFromLowerPlan && !isHighlighted && !canUpgradeFromMonthly ? "border-blue-500 shadow-md hover:shadow-lg hover:border-blue-600 hover:scale-[1.01] z-5" : ""
              )}
            >
              {/* Plan tag/ribbon */}
              {(plan as unknown as { tag?: string }).tag && (
                <div className={cn(
                  "absolute -right-12 top-6 rotate-45 px-12 py-1 text-sm font-medium",
                  tagStyling.bgColor,
                  tagStyling.textColor
                )}>
                  {tagStyling.label}
                </div>
              )}

              {/* Priority order for ribbons:
                  1. Plan tag (most important)
                  2. Monthly to Yearly upgrade opportunity
                  3. Upgrade path from lower plan
                  4. General savings percentage (least important)
              */}

              {/* Upgrade opportunity tag - Monthly to Yearly */}
              {canUpgradeFromMonthly && !(plan as unknown as { tag?: string }).tag && (
                <div className="absolute -right-12 top-6 rotate-45 px-12 py-1 text-sm font-medium bg-green-500 text-white">
                  Save {savings.percentage}%
                </div>
              )}

              {/* Upgrade path tag - Lower to Higher plan */}
              {isUpgradeFromLowerPlan && lowestPricePlanInCart && typeof lowestPricePlanInCart.price === 'number' &&
               !canUpgradeFromMonthly && !(plan as unknown as { tag?: string }).tag && (
                <div className="absolute -right-12 top-6 rotate-45 px-12 py-1 text-sm font-medium bg-blue-500 text-white">
                  Upgrade Path
                </div>
              )}

              {/* Dynamic saving percentage ribbon - Only show if no other ribbons */}
              {billingPeriod === "yearly" && savings.percentage > 0 && !(plan as unknown as { tag?: string }).tag &&
               !canUpgradeFromMonthly && !isUpgradeFromLowerPlan && (
                <div className="absolute -right-12 top-6 rotate-45 px-12 py-1 text-sm font-medium bg-green-500 text-white">
                  Save {savings.percentage}%
                </div>
              )}

              <CardHeader className="pb-2">
                <CardTitle className="pb-2 flex items-center gap-2">
                  {planName}
                  {isHighlighted && (
                    <Star className="h-5 w-5 text-yellow-500 fill-yellow-500" />
                  )}
                </CardTitle>
                <CardDescription className="pb-4">
                  {planDescription}
                </CardDescription>

                {/* Price display */}
                <div className="flex items-baseline space-x-2">
                  <span className={cn(
                    "text-3xl font-bold",
                    (isHighlighted || canUpgradeFromMonthly) ? "text-primary" : ""
                  )}>
                    {getPrice(billingPeriod === "monthly" ? monthlyPrice : yearlyPrice)}
                  </span>
                  <span className="text-muted-foreground">
                    {billingPeriod === "monthly" ? "/month" : "/year"}
                  </span>
                  {billingPeriod === "yearly" && monthlyPrice > 0 && yearlyPrice > 0 && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs flex items-center">
                            <Percent className="h-3 w-3 mr-1" />
                            Save {savings.percentage}%
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="space-y-1">
                            <p>Save {getPrice(savings.savings)} compared to monthly billing</p>
                            <p>Equivalent to {getPrice(savings.monthlyEquivalent)}/month</p>
                            <p>({getPrice(savings.monthlySavings)} less than monthly rate)</p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>

                {/* Upgrade notification: Monthly in cart, viewing yearly */}
                {samePlanDifferentBilling && billingPeriod === "yearly" && samePlanDifferentBilling.planDuration === "monthly" && (
                  <div className="mt-4 p-3 border border-dashed border-green-500 bg-green-50 dark:bg-green-900/10 rounded-md text-sm">
                    <p className="font-medium text-green-700 dark:text-green-400 flex items-center gap-1">
                      <ArrowUp className="h-4 w-4" />
                      Upgrade opportunity
                    </p>
                    <ul className="mt-2 space-y-1 text-green-600 dark:text-green-300">
                      <li className="flex items-start gap-1">
                        <Calendar className="h-3.5 w-3.5 mt-0.5" />
                        <span>You have the monthly plan ({getPrice(monthlyPrice)}/mo)</span>
                      </li>
                      <li className="flex items-start gap-1">
                        <Percent className="h-3.5 w-3.5 mt-0.5" />
                        <span>Yearly price: {getPrice(yearlyPrice)} ({getPrice(savings.monthlyEquivalent)}/mo)</span>
                      </li>
                      <li className="flex items-start gap-1">
                        <Info className="h-3.5 w-3.5 mt-0.5" />
                        <span>Save {getPrice(savings.savings)} per year ({savings.percentage}% off)</span>
                      </li>
                    </ul>
                  </div>
                )}

                {/* Upgrade notification: Lower-priced plan in cart, viewing higher-priced plan */}
                {isUpgradeFromLowerPlan && lowestPricePlanInCart && typeof lowestPricePlanInCart.price === 'number' &&
                 !samePlanDifferentBilling && !existingInCart && !canUpgradeFromMonthly && (
                  <div className="mt-4 p-3 border border-dashed border-blue-500 bg-blue-50 dark:bg-blue-900/10 rounded-md text-sm">
                    <p className="font-medium text-blue-700 dark:text-blue-400 flex items-center gap-1">
                      <ArrowUp className="h-4 w-4" />
                      Upgrade Benefits
                    </p>
                    <ul className="mt-2 space-y-1 text-blue-600 dark:text-blue-300">
                      <li className="flex items-start gap-1">
                        <Info className="h-3.5 w-3.5 mt-0.5" />
                        <span>Current plan: {lowestPricePlanInCart.name} ({getPrice(lowestPricePlanInCart.price)}/{lowestPricePlanInCart.planDuration})</span>
                      </li>
                      <li className="flex items-start gap-1">
                        <ArrowUp className="h-3.5 w-3.5 mt-0.5" />
                        <span>Upgrade to: {planName} ({getPrice(billingPeriod === "monthly" ? monthlyPrice : yearlyPrice)}/{billingPeriod})</span>
                      </li>
                      <li className="flex items-start gap-1">
                        <Check className="h-3.5 w-3.5 mt-0.5 text-green-500" />
                        <span>Get additional premium features</span>
                      </li>
                    </ul>
                  </div>
                )}

                {/* Notification: Yearly in cart, viewing monthly */}
                {samePlanDifferentBilling && billingPeriod === "monthly" && samePlanDifferentBilling.planDuration === "yearly" && (
                  <div className="mt-4 p-3 border border-dashed border-amber-500 bg-amber-50 dark:bg-amber-900/10 rounded-md text-sm">
                    <p className="font-medium text-amber-700 dark:text-amber-400 flex items-center">
                      <Info className="h-4 w-4 mr-1" />
                      You have the yearly plan in cart
                    </p>
                    <p className="mt-1 text-amber-600 dark:text-amber-300">
                      Your yearly plan ({getPrice(yearlyPrice)}) saves you {getPrice(savings.savings)} compared to monthly billing.
                    </p>
                  </div>
                )}
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  {planFeatures.map((feature: Feature, featureIdx: number) => (
                    <div
                      key={feature.id || `feature-${idx}-${featureIdx}`}
                      className={cn(
                        "flex items-start",
                        !feature.isIncluded && "opacity-70"
                      )}
                    >
                      {feature.isIncluded ? (
                        <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 shrink-0" />
                      ) : (
                        <X className="h-5 w-5 text-gray-300 mr-2 mt-0.5 shrink-0" />
                      )}
                      <span className={feature.isIncluded ? "" : "text-gray-400"}>
                        {feature.feature}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>

              <CardFooter>
                {/* Smart button state handling */}
                {existingInCart ? (
                  <Button
                    variant="outline"
                    className="w-full border-green-500 text-green-600"
                    disabled={true}
                  >
                    <Check className="mr-1 h-4 w-4" /> Added to Cart
                  </Button>
                ) : samePlanDifferentBilling && billingPeriod === "yearly" ? (
                  <SubscriptionButton
                    plan={{
                      id: planName,
                      name: planName,
                      price: yearlyPrice,
                      planCode: plan.plan_code_yearly || '',
                      planDuration: "yearly"
                    }}
                    planCode={plan.plan_code_yearly || ''}
                    planName={planName}
                    planPrice={yearlyPrice}
                    planDuration={billingPeriod}
                    productSlug={slug}
                    productName={pricingData?.productInfo?.productName || slug}
                    productLogo={pricingData?.productInfo?.productLogo}
                    variant="default"
                    className="w-full bg-green-600 hover:bg-green-700"
                    onPlanChange={(existingPlan, newPlan, changeType) => {
                      // Convert Plan to CartItem format
                      const existingCartItem = {
                        id: existingPlan.name,
                        name: existingPlan.name,
                        price: existingPlan.price,
                        planCode: existingPlan.planCode || '',
                        quantity: 1,
                        image: pricingData?.productInfo?.productLogo || "",
                        slug,
                        productName: pricingData?.productInfo?.productName || "",
                        productLogo: pricingData?.productInfo?.productLogo || "",
                        planDuration: existingPlan.planDuration || "monthly"
                      };

                      // Create new cart item
                      const newCartItem = {
                        id: planName,
                        name: planName,
                        price: yearlyPrice,
                        planCode: plan.plan_code_yearly || '',
                        quantity: 1,
                        image: pricingData?.productInfo?.productLogo || "",
                        slug,
                        productName: pricingData?.productInfo?.productName || "",
                        productLogo: pricingData?.productInfo?.productLogo || "",
                        planDuration: "yearly" as "trial" | "monthly" | "yearly"
                      };

                      // Set up the plan change dialog
                      setPlanToChange({
                        existingPlan: existingCartItem,
                        newPlan: { name: planName, price: yearlyPrice },
                        newItem: newCartItem,
                        changeType
                      });
                      setIsAlertOpen(true);
                    }}
                  />
                ) : (
                  <SubscriptionButton
                    plan={{
                      id: planName,
                      name: planName,
                      price: billingPeriod === "monthly" ? monthlyPrice : yearlyPrice,
                      planCode: billingPeriod === "monthly" ? plan.plan_code_monthly || '' : plan.plan_code_yearly || '',
                      planDuration: billingPeriod
                    }}
                    planCode={billingPeriod === "monthly" ? plan.plan_code_monthly || '' : plan.plan_code_yearly || ''}
                    planName={planName}
                    planPrice={billingPeriod === "monthly" ? monthlyPrice : yearlyPrice}
                    planDuration={billingPeriod}
                    productSlug={slug}
                    productName={pricingData?.productInfo?.productName || slug}
                    productLogo={pricingData?.productInfo?.productLogo}
                    variant={isHighlighted ? "default" : isUpgradeFromLowerPlan ? "default" : "secondary"}
                    className={cn(
                      "w-full",
                      isHighlighted ? "bg-primary hover:bg-primary/90" : "",
                      isUpgradeFromLowerPlan && !isHighlighted ? "bg-blue-500 hover:bg-blue-600" : ""
                    )}
                    onPlanChange={(existingPlan, newPlan, changeType) => {
                      // Convert Plan to CartItem format
                      const existingCartItem = {
                        id: existingPlan.name,
                        name: existingPlan.name,
                        price: existingPlan.price,
                        planCode: existingPlan.planCode || '',
                        quantity: 1,
                        image: pricingData?.productInfo?.productLogo || "",
                        slug,
                        productName: pricingData?.productInfo?.productName || "",
                        productLogo: pricingData?.productInfo?.productLogo || "",
                        planDuration: existingPlan.planDuration || "monthly"
                      };

                      // Create new cart item
                      const newCartItem = {
                        id: planName,
                        name: planName,
                        price: billingPeriod === "monthly" ? monthlyPrice : yearlyPrice,
                        planCode: billingPeriod === "monthly" ? plan.plan_code_monthly || '' : plan.plan_code_yearly || '',
                        quantity: 1,
                        image: pricingData?.productInfo?.productLogo || "",
                        slug,
                        productName: pricingData?.productInfo?.productName || "",
                        productLogo: pricingData?.productInfo?.productLogo || "",
                        planDuration: billingPeriod
                      };

                      // Set up the plan change dialog
                      setPlanToChange({
                        existingPlan: existingCartItem,
                        newPlan: { name: planName, price: billingPeriod === "monthly" ? monthlyPrice : yearlyPrice },
                        newItem: newCartItem,
                        changeType
                      });
                      setIsAlertOpen(true);
                    }}
                  />
                )}
              </CardFooter>
            </Card>
          );
        })}

        {hasEnterprisePlan && pricingData.enterprisePlan && (
          <Card className={cn(
            "relative overflow-hidden transition-all duration-300",
            pricingData.enterprisePlan.tag ? "border-primary shadow-lg" : ""
          )}>
            {pricingData.enterprisePlan.tag && (
              <div className={cn(
                "absolute -right-12 top-6 rotate-45 px-12 py-1 text-sm font-medium",
                getTagStyling(pricingData.enterprisePlan.tag).bgColor,
                getTagStyling(pricingData.enterprisePlan.tag).textColor
              )}>
                {getTagStyling(pricingData.enterprisePlan.tag).label}
              </div>
            )}
            <CardHeader className="pb-2">
              <CardTitle className="pb-2 flex items-center gap-2">
                {pricingData.enterprisePlan.name}
                <Badge className="h-5 w-5 text-blue-600" />
              </CardTitle>
              <CardDescription className="pb-4">
                {pricingData.enterprisePlan.description}
              </CardDescription>
              <div className="text-xl font-semibold">
                Custom Pricing
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                {Array.isArray(pricingData.enterprisePlan.features) && pricingData.enterprisePlan.features.map((feature: Feature, idx: number) => (
                  <div key={feature.id || `enterprise-feature-${idx}`} className="flex items-start">
                    {feature.isIncluded ? (
                      <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 shrink-0" />
                    ) : (
                      <X className="h-5 w-5 text-gray-300 mr-2 mt-0.5 shrink-0" />
                    )}
                    <span className={feature.isIncluded ? "" : "text-gray-400"}>
                      {feature.feature}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>

            <CardFooter>
              <Button
                variant={pricingData.enterprisePlan.tag ? "default" : "outline"}
                className="w-full"
                onClick={() => pricingData.enterprisePlan && handleAddPlan(pricingData.enterprisePlan)}
              >
                {pricingData.enterprisePlan.button?.text || "Contact Us"}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>

      {/* Upgrade banner that appears at the bottom if there are upgrade opportunities */}
      {/* {Object.keys(plansWithUpgrade).length > 0 && billingPeriod === "monthly" && (
        <div className="mt-10 rounded-lg bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 p-4 border border-primary/20">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="rounded-full p-2 bg-primary/20">
                <Percent className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="text-lg font-medium">Save by switching to yearly billing</h3>
                <p className="text-sm text-muted-foreground">Get up to {maxSavingsPercentage}% discount when you choose annual billing</p>
              </div>
            </div>
            <Button
              variant="default"
              onClick={switchToYearlyBilling}
              className="md:self-center"
            >
              See yearly plans
            </Button>
          </div>
        </div>
      )} */}

      <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
        <AlertDialogContent className="max-w-xl overflow-y-auto max-h-[90vh]">
          <AlertDialogHeader>
            <AlertDialogTitle>
              {planToChange?.changeType === "billing"
                ? "Change Billing Period"
                : planToChange?.changeType === "upgrade"
                  ? "Upgrade Your Plan"
                  : planToChange?.changeType === "downgrade"
                    ? "Change to a Lower-Tier Plan"
                    : "Change Your Plan"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {/* Monthly to Yearly upgrade dialog content */}
              {planToChange?.changeType === "upgrade" && planToChange.existingPlan.planDuration === "monthly" && planToChange.newItem.planDuration === "yearly" && (
                <>
                  <p className="mb-2">
                    You&apos;re upgrading from <strong>monthly</strong> to <strong>yearly</strong> billing
                    for the <strong>{planToChange.newItem.name}</strong> plan.
                  </p>

                  <div className="mt-4 p-3 border border-dashed border-green-600 bg-green-50 dark:bg-green-900/20 rounded-md">
                    <p className="font-medium text-green-700 dark:text-green-400 flex items-center">
                      <Percent className="h-4 w-4 mr-2" />
                      Your savings by switching to yearly:
                    </p>
                    <ul className="mt-2 space-y-1 text-sm">
                      <li>• Monthly: {getPrice(planToChange.existingPlan.price)} × 12 = {getPrice(planToChange.existingPlan.price * 12)}</li>
                      <li>• Yearly: {getPrice(planToChange.newItem.price)}</li>
                      <li>• You save: {getPrice((planToChange.existingPlan.price * 12) - planToChange.newItem.price)} per year</li>
                      <li>• That&apos;s {Math.round(((planToChange.existingPlan.price * 12) - planToChange.newItem.price) / (planToChange.existingPlan.price * 12) * 100)}% discount</li>
                      <li>• Equivalent to paying {getPrice(planToChange.newItem.price / 12)}/month</li>
                    </ul>
                  </div>
                </>
              )}

              {/* Same plan, billing period change */}
              {/* ...existing billing change content... */}

              {/* Plan upgrade content (different plans) */}
              {planToChange?.changeType === "upgrade" &&
               !(planToChange.existingPlan.planDuration === "monthly" && planToChange.newItem.planDuration === "yearly" &&
                  planToChange.existingPlan.id === planToChange.newItem.id) && (
                <>
                  <p className="mb-2">
                    You&apos;re upgrading from <strong>{planToChange.existingPlan.name}</strong> ({planToChange.existingPlan.planDuration})
                    to <strong>{planToChange.newItem.name}</strong> ({planToChange.newItem.planDuration}).
                  </p>

                  {planToChange.existingPlan.planDuration !== planToChange.newItem.planDuration && (
                    <p className="text-sm mb-4">
                      Note: You&apos;re also changing your billing cycle from {planToChange.existingPlan.planDuration} to {planToChange.newItem.planDuration}.
                    </p>
                  )}

                  {/* Price comparison */}
                  <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                    <h4 className="font-medium text-blue-700 dark:text-blue-400 flex items-center gap-2">
                      <ArrowUp className="h-4 w-4" />
                      Price Change
                    </h4>

                    <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="font-medium">Current Plan:</p>
                        <p>{getPrice(planToChange.existingPlan.price)} / {planToChange.existingPlan.planDuration}</p>
                      </div>
                      <div>
                        <p className="font-medium">New Plan:</p>
                        <p>{getPrice(planToChange.newItem.price)} / {planToChange.newItem.planDuration}</p>
                      </div>
                    </div>

                    {planComparison && (
                      <div className="mt-2 text-sm flex items-center">
                        <span className="font-medium">Price difference: </span>
                        <span className="ml-1 font-bold text-green-600">
                          {planComparison.priceDifference > 0
                            ? `+${getPrice(Math.abs(planComparison.priceDifference))} (${planComparison.percentageChange}% higher)`
                            : `${getPrice(planComparison.priceDifference)} (${Math.abs(planComparison.percentageChange)}% lower)`
                          }
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Added features */}
                  {planComparison && planComparison.addedFeatures.length > 0 && (
                    <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-md">
                      <h4 className="font-medium text-green-700 dark:text-green-400 flex items-center gap-2">
                        <Check className="h-4 w-4" />
                        New Features You&apos;ll Get
                      </h4>
                      <ul className="mt-2 space-y-1 text-sm">
                        {planComparison.addedFeatures.map((feature, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <ArrowRight className="h-4 w-4 text-green-600 shrink-0 mt-0.5" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </>
              )}

              {/* Plan downgrade content */}
              {planToChange?.changeType === "downgrade" && (
                <>
                  <p className="mb-2">
                    You&apos;re changing from <strong>{planToChange.existingPlan.name}</strong> ({planToChange.existingPlan.planDuration})
                    to <strong>{planToChange.newItem.name}</strong> ({planToChange.newItem.planDuration}).
                  </p>

                  {/* Warning about feature loss */}
                  <div className="mt-4 p-3 border border-dashed border-amber-600 bg-amber-50 dark:bg-amber-900/20 rounded-md">
                    <p className="font-medium text-amber-700 dark:text-amber-400 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Important Notice
                    </p>
                    <p className="mt-2 text-sm">
                      This change will result in a lower-tier plan with fewer features.
                    </p>
                  </div>

                  {/* Price comparison */}
                  <div className="mt-4 mb-4 p-4 bg-amber-50/50 dark:bg-amber-900/10 rounded-md">
                    <h4 className="font-medium text-amber-700 dark:text-amber-400 flex items-center gap-2">
                      <ArrowDown className="h-4 w-4" />
                      Price Change
                    </h4>

                    <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="font-medium">Current Plan:</p>
                        <p>{getPrice(planToChange.existingPlan.price)} / {planToChange.existingPlan.planDuration}</p>
                      </div>
                      <div>
                        <p className="font-medium">New Plan:</p>
                        <p>{getPrice(planToChange.newItem.price)} / {planToChange.newItem.planDuration}</p>
                      </div>
                    </div>

                    {planComparison && (
                      <div className="mt-2 text-sm flex items-center">
                        <span className="font-medium">Price difference: </span>
                        <span className="ml-1 font-bold text-amber-600">
                          {getPrice(planComparison.priceDifference)} ({Math.abs(planComparison.percentageChange)}% lower)
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Features you'll lose */}
                  {planComparison && planComparison.removedFeatures.length > 0 && (
                    <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/10 rounded-md">
                      <h4 className="font-medium text-red-700 dark:text-red-400 flex items-center gap-2">
                        <X className="h-4 w-4" />
                        Features You&apos;ll Lose
                      </h4>
                      <ul className="mt-2 space-y-1 text-sm">
                        {planComparison.removedFeatures.map((feature, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <X className="h-4 w-4 text-red-500 shrink-0 mt-0.5" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </>
              )}

              {/* Switch between plans of similar tier */}
              {planToChange?.changeType === "different" && (
                <>
                  <p className="mb-2">
                    You&apos;re changing from <strong>{planToChange.existingPlan.name}</strong> ({planToChange.existingPlan.planDuration})
                    to <strong>{planToChange.newItem.name}</strong> ({planToChange.newItem.planDuration}).
                  </p>

                  {/* Comparison of plans */}
                  <div className="mt-4 mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                    <h4 className="font-medium text-blue-700 dark:text-blue-400 flex items-center gap-2">
                      <ChevronDown className="h-4 w-4" />
                      Plan Comparison
                    </h4>

                    <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Current Plan:</p>
                        <p>{getPrice(planToChange.existingPlan.price)} / {planToChange.existingPlan.planDuration}</p>
                      </div>
                      <div>
                        <p className="font-medium">New Plan:</p>
                        <p>{getPrice(planToChange.newItem.price)} / {planToChange.newItem.planDuration}</p>
                      </div>
                    </div>
                  </div>

                  {/* Feature differences */}
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {/* Features you'll gain */}
                    {planComparison && planComparison.addedFeatures.length > 0 && (
                      <div className="p-3 bg-green-50 dark:bg-green-900/10 rounded-md">
                        <h4 className="text-sm font-medium text-green-700 dark:text-green-400 flex items-center gap-2">
                          <Check className="h-4 w-4" />
                          Features You&apos;ll Gain
                        </h4>
                        <ul className="mt-2 space-y-1 text-xs">
                          {planComparison.addedFeatures.map((feature, idx) => (
                            <li key={idx} className="flex items-start gap-1">
                              <Check className="h-3 w-3 text-green-500 shrink-0 mt-0.5" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Features you'll lose */}
                    {planComparison && planComparison.removedFeatures.length > 0 && (
                      <div className="p-3 bg-red-50 dark:bg-red-900/10 rounded-md">
                        <h4 className="text-sm font-medium text-red-700 dark:text-red-400 flex items-center gap-2">
                          <X className="h-4 w-4" />
                          Features You&apos;ll Lose
                        </h4>
                        <ul className="mt-2 space-y-1 text-xs">
                          {planComparison.removedFeatures.map((feature, idx) => (
                            <li key={idx} className="flex items-start gap-1">
                              <X className="h-3 w-3 text-red-500 shrink-0 mt-0.5" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isPlanChangeProcessing}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              disabled={isPlanChangeProcessing}
              onClick={async () => {
                if (!planToChange) return;

                try {
                  // Set processing state to prevent multiple clicks
                  setIsPlanChangeProcessing(true);

                  // First, close the dialog to prevent UI freezing
                  setIsAlertOpen(false);

                  // Small delay to ensure dialog is closed before cart update
                  await new Promise(resolve => setTimeout(resolve, 100));

                  // Now update the cart
                  replaceItem(planToChange.existingPlan.id, planToChange.newItem);

                  // Dynamic success messages based on change type
                  const actionText =
                    planToChange.changeType === "billing" && planToChange.newItem.planDuration === "yearly"
                      ? "Upgraded to yearly plan"
                      : planToChange.changeType === "upgrade"
                        ? "Plan upgraded successfully"
                        : planToChange.changeType === "downgrade"
                          ? "Plan changed successfully"
                          : "Plan changed successfully";

                  toast.success(actionText);
                } catch (error) {
                  console.error("Error updating plan:", error);
                  toast.error("Failed to update plan. Please try again.");
                } finally {
                  // Reset processing state after a short delay
                  setTimeout(() => {
                    setIsPlanChangeProcessing(false);
                  }, 500);
                }
              }}
            >
              {isPlanChangeProcessing ? (
                <span className="flex items-center gap-2">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : planToChange?.changeType === "upgrade" ? (
                "Confirm Upgrade"
              ) : planToChange?.changeType === "downgrade" ? (
                "Confirm Downgrade"
              ) : (
                "Confirm Change"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Subscription Info Dialog */}
      {existingSubscription && (
        <SubscriptionInfoDialog
          open={isSubscriptionInfoOpen}
          onClose={() => setIsSubscriptionInfoOpen(false)}
          productName={pricingData?.productInfo?.productName || slug}
          productSlug={slug}
          currentPlan={existingSubscription}
          onViewPricing={() => setIsSubscriptionInfoOpen(false)}
          onGoToDashboard={() => window.location.href = `${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`}
        />
      )}
    </SectionContainer>
  );
};


