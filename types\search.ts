/**
 * Types for the search functionality
 */

/**
 * Represents a section within a product
 */
export interface ProductSection {
  __component: string;
  id: number;
  title: string;
}

/**
 * Raw search hit from Meilisearch
 */
export interface MeilisearchHit {
  _meilisearch_id?: string;
  id: number;
  documentId?: string;
  slug: string;
  createdAt?: string;
  updatedAt?: string;
  publishedAt?: string;
  ProductCard: {
    id?: number;
    productName: string;
    productDescription?: string;
    label?: string;
    productCategory?: string;
    productRedirectURL?: string;
  };
  sections?: ProductSection[];
}

/**
 * Processed search hit for UI display
 */
export interface SearchHit {
  productName: string;
  slug: string;
  sectionTitle: string | null;   // null = product-level match
  sectionAnchor: string | null;  // null = product-level match
}

/**
 * Search response from Meilisearch
 */
export interface MeilisearchResponse {
  hits: MeilisearchHit[];
  processingTimeMs: number;
  query: string;
}

/**
 * Search state
 */
export interface SearchState {
  isLoading: boolean;
  error: Error | null;
  data: SearchHit[] | null;
  latency: number | null;
}
