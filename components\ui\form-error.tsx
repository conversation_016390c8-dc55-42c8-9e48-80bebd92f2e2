"use client";

import { <PERSON>ert<PERSON>ircle, CheckCircle, Info, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";

interface FormErrorProps {
  error?: string;
  type?: 'error' | 'success' | 'warning' | 'info';
  className?: string;
}

export function FormError({ error, type = 'error', className }: FormErrorProps) {
  if (!error) return null;

  const icons = {
    error: AlertCircle,
    success: CheckCircle,
    warning: AlertTriangle,
    info: Info,
  };

  const styles = {
    error: "text-destructive bg-destructive/10 border-destructive/20",
    success: "text-green-600 bg-green-50 border-green-200",
    warning: "text-orange-600 bg-orange-50 border-orange-200",
    info: "text-blue-600 bg-blue-50 border-blue-200",
  };

  const Icon = icons[type];

  return (
    <div className={cn(
      "flex items-center gap-2 p-3 rounded-md border text-sm",
      styles[type],
      className
    )}>
      <Icon className="h-4 w-4 flex-shrink-0" />
      <span>{error}</span>
    </div>
  );
}

interface FormFieldErrorProps {
  error?: string;
  className?: string;
}

export function FormFieldError({ error, className }: FormFieldErrorProps) {
  if (!error) return null;

  return (
    <p className={cn("text-sm text-destructive flex items-center gap-1", className)}>
      <AlertCircle className="h-3 w-3" />
      {error}
    </p>
  );
}
